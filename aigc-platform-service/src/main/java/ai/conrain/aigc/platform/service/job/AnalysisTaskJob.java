package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.agent.SearchUtil;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.MapUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AnalysisTaskJob extends JavaProcessor {

    /**
     * 固定大小的虚拟线程池，最多20个并发任务
     */
    private static final ExecutorService VIRTUAL_THREAD_POOL = Executors.newFixedThreadPool(20,
            Thread.ofVirtual().name("image-processing-", 0).factory());

    /**
     * 无界阻塞队列，存储待处理的image_ids
     */
    private static final BlockingQueue<Integer> TASK_QUEUE = new LinkedBlockingQueue<>();

    @Autowired
    private ImageGroupDAO imageGroupDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private CaptionUserService captionUserService;

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        log.info("[AnalysisTaskJob] start, params: {}, jobId: {}", params, context.getJobId());

        CaptionUserVO llm = captionUserService.getOrCreateByUsername("gemini-flash");

        try {
            // 获取所有未标注的图像ID
            List<Integer> imageIds = imageGroupDAO.selectAllImageIdsNotCaptionByUser(llm.getId());
            if (CollectionUtils.isNotEmpty(params.getImageIds())) {
                imageIds = params.getImageIds();
            }
            log.info("[AnalysisTaskJob] Found {} images not captioned by llm {}", imageIds.size(), llm.getId());

            // 添加到任务队列
            int addedCount = addToTaskQueue(imageIds, context.getJobId());
            log.info("[AnalysisTaskJob] Added {} images to task queue, current queue size: {}",
                    addedCount, TASK_QUEUE.size());

            // 提交任务到虚拟线程池处理
            submitTasksToThreadPool(llm, context.getJobId());
            log.info("[AnalysisTaskJob] Submitted tasks to virtual thread pool for processing");

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }

    /**
     * 添加图像ID到任务队列（避免重复添加）
     *
     * @param imageIds 图像ID列表
     * @param jobId 任务ID
     * @return 实际添加的数量
     */
    private int addToTaskQueue(List<Integer> imageIds, Long jobId) {
        if (imageIds == null || imageIds.isEmpty()) {
            return 0;
        }

        int addedCount = 0;

        // 添加图像ID到队列，避免重复
        for (Integer imageId : imageIds) {
            // 检查队列中是否已存在该imageId
            if (!TASK_QUEUE.contains(imageId)) {
                try {
                    // 添加到无界队列
                    TASK_QUEUE.put(imageId);
                    addedCount++;

                    log.debug("[AnalysisTaskJob] Added image {} to task queue, jobId: {}", imageId, jobId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("[AnalysisTaskJob] Interrupted while adding image {} to queue", imageId, e);
                    break;
                }
            } else {
                log.debug("[AnalysisTaskJob] Image {} already exists in queue, skipping", imageId);
            }
        }

        log.info("[AnalysisTaskJob] Added {} new images to task queue, current queue size: {}",
                addedCount, TASK_QUEUE.size());

        return addedCount;
    }

    /**
     * 提交任务到虚拟线程池处理
     *
     * @param user 用户信息
     * @param jobId 任务ID
     */
    private void submitTasksToThreadPool(CaptionUserVO user, Long jobId) {
        int queueSize = TASK_QUEUE.size();
        log.info("[AnalysisTaskJob] Starting to submit {} tasks to virtual thread pool", queueSize);

        // 提交所有队列中的任务到线程池
        // 线程池会自动管理并发数量（最多20个并发）
        while (!TASK_QUEUE.isEmpty()) {
            try {
                Integer imageId = TASK_QUEUE.take(); // 阻塞获取

                // 提交任务到虚拟线程池
                VIRTUAL_THREAD_POOL.submit(() -> {
                    try {
                        processImage(imageId, user, jobId);
                        log.info("[AnalysisTaskJob] Successfully processed image {} in virtual thread", imageId);
                    } catch (Exception e) {
                        log.error("[AnalysisTaskJob] Failed to process image {} in virtual thread", imageId, e);
                    }
                });

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[AnalysisTaskJob] Interrupted while taking task from queue", e);
                break;
            }
        }

        log.info("[AnalysisTaskJob] All tasks submitted to virtual thread pool");
    }

    /**
     * 处理单个图像
     *
     * @param imageId 图像ID
     * @param llm 用户信息
     * @param jobId 任务ID
     */
    private void processImage(Integer imageId, CaptionUserVO llm, Long jobId) throws InterruptedException {
        // 1. 调用AI服务进行图像分析
        ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
        imageCaptionUserQuery.setImageId(imageId);
        imageCaptionUserQuery.setUserId(llm.getId());
        List<ImageCaptionUserVO> list = imageCaptionUserService.queryImageCaptionUserList(imageCaptionUserQuery);
        if (!list.isEmpty()) {
            log.info("[AnalysisTaskJob] Image {} already captioned by user {}, skip", imageId, llm.getId());
            return;
        }
        ImageVO imageVO = imageService.selectById(imageId);
        if (imageVO == null) {
            log.info("[AnalysisTaskJob] Image {} not found, skip", imageId);
            return;
        }
        // 2. 保存标注结果
        String cacheKey = DigestUtils.md5Hex(imageVO.getUrl());
        ImageAnalysisResult result = tairService.getObject(cacheKey, ImageAnalysisResult.class);

        if (result == null || result.getAnalysis() == null
            || !SearchUtil.isValidImageAnalysisCloth(result.getAnalysis())) {

            ImageAnalysisTaskResponse task = imageAnalysisService.createAnalysisTask(
                imageVO.getUrl(),
                llm.getUsername(),
                ImageTypeEnum.CLOTH.name().equals(imageVO.getType()),
                true,
                imageVO.getMetadata().getString("clothType"));

            for (;;) {
                result = imageAnalysisService.getAnalysisResult(task.getTaskId());
                if (result != null && result.getAnalysis() != null) {
                    tairService.setObject(cacheKey, result, 60 * 60 * 24);
                    break;
                } else {
                    Thread.sleep(10 * 1000);
                }
            }
        }
        // 3. 更新数据库状态
        Map<String, Object> flattenedCaption = MapUtil.flatten(JSONObject.parseObject(result.getRaw()));
        ImageCaptionUserVO imageCaptionUserVO = new ImageCaptionUserVO();
        imageCaptionUserVO.setImageId(imageId);
        imageCaptionUserVO.setUserId(llm.getId());
        imageCaptionUserVO.setCaption(new com.alibaba.fastjson2.JSONObject(flattenedCaption));
        imageCaptionUserService.insert(imageCaptionUserVO);

        log.info("[AnalysisTaskJob] Processing image {} for user {} in job {}", imageId, llm.getId(), jobId);
    }

    /**
     * 关闭线程池（应用关闭时调用）
     */
    public static void shutdown() {
        VIRTUAL_THREAD_POOL.shutdown();
        try {
            if (!VIRTUAL_THREAD_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                VIRTUAL_THREAD_POOL.shutdownNow();
            }
        } catch (InterruptedException e) {
            VIRTUAL_THREAD_POOL.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("[AnalysisTaskJob] Virtual thread pool shutdown completed");
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 图像ids
         */
        private List<Integer> imageIds;
    }
}
