package ai.conrain.aigc.platform.web.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import ai.conrain.aigc.platform.service.component.impl.WeChatPayService;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.wx.WeixinNotifyUtils;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.CreateQrCodeRequest;
import ai.conrain.aigc.platform.service.model.vo.PayQRCode;
import ai.conrain.aigc.platform.service.model.vo.PayQueryRet;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.github.binarywang.wxpay.bean.notify.OriginNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.service.WxPayService;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <a href="https://maxqiu.com/article/detail/135">...</a>
 */
@Slf4j
@RestController
@RequestMapping("/wx/pay")
public class WeChatPayController {

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private WxPayService wxPayService;

    /**
     * native下单
     * <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_4_1.shtml">...</a>
     */
    @PostMapping("/qrcode")
    public Result<PayQRCode> createWeChatPayQRCode(@Valid @RequestBody CreateQrCodeRequest request) {
        try {
            return Result.success(weChatPayService.createPayQRCode(request));
        } catch (Exception e) {
            log.error("创建支付二维码失败", e);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "创建支付二维码失败");
    }

    @PostMapping("/query")
    public Result<PayQueryRet> queryWeChatPayResult(@JsonArg @NotBlank String orderNo) {
        try {
            return Result.success(weChatPayService.queryPayResult(orderNo));
        } catch (Throwable e) {
            log.error("查询支付订单失败", e);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "查询支付订单失败");
    }

    /**
     * native支付成功回调通知
     * <a href="https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_4_5.shtml">...</a>
     * @param request
     * @param response
     * @param notifyData
     * @return
     */
    @PostMapping("/notify")
    public Map<String, String> notify(HttpServletRequest request, HttpServletResponse response, @RequestBody String notifyData) {

        try {
            log.info("接收到微信通知:{}", notifyData);
            // 获取请求头信息
            SignatureHeader signatureHeader = WeixinNotifyUtils.getSignatureHeader(request);
            // 将请求体json字符串转换为实体
            OriginNotifyResponse notifyResponse = CommonUtil.parseObject(notifyData, OriginNotifyResponse.class);
            if (notifyResponse == null) {
                log.error("解析通知内容为OriginNotifyResponse对象失败，忽略");
                return notifySuccessOrIgnore();
            }

            if ("TRANSACTION.SUCCESS".equals(notifyResponse.getEventType())) {
                // 解析支付结果通知
                WxPayNotifyV3Result result = wxPayService.parseOrderNotifyV3Result(notifyData, signatureHeader);

                String orderNo = result.getResult().getOutTradeNo();
                log.info("微信支付成功: orderNo={},result={}", orderNo, result);

                AssertUtil.assertTrue(weChatPayService.onPaySuccess(orderNo), "支付成功但处理失败，需要重试");
            } else {
                // 其它类型事件，返回异常，理论上不会走到
                log.error("收到微信支付结果通知，但eventType={}不是支付成功，忽略", notifyResponse.getEventType());
            }
            return notifySuccessOrIgnore();

        } catch (Throwable t) {
            log.error("支付结果通知处理失败，返回进行重试", t);
            DingTalkNoticeHelper.sendMsg2DevGroup("微信支付结果通知处理失败，返回进行重试\ntraceId:" + MDC.get("traceId"));
            return notifyFailed(response);
        }
    }

    // 返回成功（无需数据，系统状态码为200即可）
    private Map<String,String> notifySuccessOrIgnore(){
        return new HashMap<>();
    }

    // 返回失败，微信会重发
    private Map<String, String> notifyFailed(HttpServletResponse response) {
        response.setStatus(503);
        Map<String, String> map = new HashMap<>();
        map.put("code", "FAIL");
        map.put("message", "系统繁忙");
        return map;
    }
}
