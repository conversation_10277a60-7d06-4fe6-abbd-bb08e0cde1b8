/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.constraints.NotBlank;

import ai.conrain.aigc.platform.service.component.SmsService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.validation.Mobile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信相关控制器
 *
 * <AUTHOR>
 * @version : SmsController.java, v 0.1 2024/1/20 20:18 renxiao.wu Exp $
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/sms")
public class SmsController {
    @Autowired
    private UserService userService;
    @Autowired
    private SmsService smsService;

    @PostMapping("/sendCaptcha")
    public Result<?> sendCaptcha(@Mobile @JsonArg String mobile) {
        boolean success = smsService.sendCaptcha(mobile);
        if (!success) {
            return Result.failedWithMessage(ResultCode.SYS_ERROR, "短信验证码发送失败");
        }
        return Result.success();
    }

    @PostMapping("/verifyCaptcha")
    public Result<?> verifyCaptcha(@Mobile @JsonArg String mobile, @NotBlank @JsonArg String code) {
        boolean success = smsService.verifyCaptcha(mobile, code);
        if (!success) {
            return Result.failedWithMessage(ResultCode.ILLEGAL_CAPTCHA, "验证短信验证码失败");
        }
        //打上标记，将mobile缓存下来
        OperationContextHolder.getContext().setAttribute(CommonConstants.TAIR_REST_PSWD_MOBLE, mobile);
        return Result.success();
    }
}
