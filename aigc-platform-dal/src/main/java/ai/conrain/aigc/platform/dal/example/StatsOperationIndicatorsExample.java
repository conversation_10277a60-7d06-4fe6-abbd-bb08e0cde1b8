package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StatsOperationIndicatorsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public StatsOperationIndicatorsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public StatsOperationIndicatorsExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public StatsOperationIndicatorsExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public StatsOperationIndicatorsExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }



        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNull() {
            addCriterion("stats_type is null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNotNull() {
            addCriterion("stats_type is not null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeEqualTo(String value) {
            addCriterion("stats_type =", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotEqualTo(String value) {
            addCriterion("stats_type <>", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThan(String value) {
            addCriterion("stats_type >", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stats_type >=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThan(String value) {
            addCriterion("stats_type <", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThanOrEqualTo(String value) {
            addCriterion("stats_type <=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLike(String value) {
            addCriterion("stats_type like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotLike(String value) {
            addCriterion("stats_type not like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIn(List<String> values) {
            addCriterion("stats_type in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotIn(List<String> values) {
            addCriterion("stats_type not in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeBetween(String value1, String value2) {
            addCriterion("stats_type between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotBetween(String value1, String value2) {
            addCriterion("stats_type not between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNull() {
            addCriterion("stats_date is null");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNotNull() {
            addCriterion("stats_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatsDateEqualTo(String value) {
            addCriterion("stats_date =", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotEqualTo(String value) {
            addCriterion("stats_date <>", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThan(String value) {
            addCriterion("stats_date >", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThanOrEqualTo(String value) {
            addCriterion("stats_date >=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThan(String value) {
            addCriterion("stats_date <", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThanOrEqualTo(String value) {
            addCriterion("stats_date <=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLike(String value) {
            addCriterion("stats_date like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotLike(String value) {
            addCriterion("stats_date not like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateIn(List<String> values) {
            addCriterion("stats_date in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotIn(List<String> values) {
            addCriterion("stats_date not in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateBetween(String value1, String value2) {
            addCriterion("stats_date between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotBetween(String value1, String value2) {
            addCriterion("stats_date not between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "name");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIsNull() {
            addCriterion("customer_conversion_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIsNotNull() {
            addCriterion("customer_conversion_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountEqualTo(Integer value) {
            addCriterion("customer_conversion_count =", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotEqualTo(Integer value) {
            addCriterion("customer_conversion_count <>", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountGreaterThan(Integer value) {
            addCriterion("customer_conversion_count >", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_conversion_count >=", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountLessThan(Integer value) {
            addCriterion("customer_conversion_count <", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_conversion_count <=", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIn(List<Integer> values) {
            addCriterion("customer_conversion_count in", values, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotIn(List<Integer> values) {
            addCriterion("customer_conversion_count not in", values, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_conversion_count between", value1, value2, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_conversion_count not between", value1, value2, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIsNull() {
            addCriterion("customer_consumption_points is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIsNotNull() {
            addCriterion("customer_consumption_points is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsEqualTo(Integer value) {
            addCriterion("customer_consumption_points =", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotEqualTo(Integer value) {
            addCriterion("customer_consumption_points <>", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsGreaterThan(Integer value) {
            addCriterion("customer_consumption_points >", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_consumption_points >=", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsLessThan(Integer value) {
            addCriterion("customer_consumption_points <", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsLessThanOrEqualTo(Integer value) {
            addCriterion("customer_consumption_points <=", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIn(List<Integer> values) {
            addCriterion("customer_consumption_points in", values, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotIn(List<Integer> values) {
            addCriterion("customer_consumption_points not in", values, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsBetween(Integer value1, Integer value2) {
            addCriterion("customer_consumption_points between", value1, value2, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_consumption_points not between", value1, value2, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgIsNull() {
            addCriterion("customer_consumption_points_avg is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgIsNotNull() {
            addCriterion("customer_consumption_points_avg is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgEqualTo(String value) {
            addCriterion("customer_consumption_points_avg =", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgNotEqualTo(String value) {
            addCriterion("customer_consumption_points_avg <>", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgGreaterThan(String value) {
            addCriterion("customer_consumption_points_avg >", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgGreaterThanOrEqualTo(String value) {
            addCriterion("customer_consumption_points_avg >=", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgLessThan(String value) {
            addCriterion("customer_consumption_points_avg <", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgLessThanOrEqualTo(String value) {
            addCriterion("customer_consumption_points_avg <=", value, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgIn(List<String> values) {
            addCriterion("customer_consumption_points_avg in", values, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgNotIn(List<Integer> values) {
            addCriterion("customer_consumption_points_avg not in", values, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgBetween(Integer value1, Integer value2) {
            addCriterion("customer_consumption_points_avg between", value1, value2, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsAvgNotBetween(String value1, String value2) {
            addCriterion("customer_consumption_points_avg not between", value1, value2, "customerConsumptionPointsAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIsNull() {
            addCriterion("customer_activity_rate is null");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIsNotNull() {
            addCriterion("customer_activity_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateEqualTo(String value) {
            addCriterion("customer_activity_rate =", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotEqualTo(String value) {
            addCriterion("customer_activity_rate <>", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateGreaterThan(String value) {
            addCriterion("customer_activity_rate >", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateGreaterThanOrEqualTo(String value) {
            addCriterion("customer_activity_rate >=", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLessThan(String value) {
            addCriterion("customer_activity_rate <", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLessThanOrEqualTo(String value) {
            addCriterion("customer_activity_rate <=", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLike(String value) {
            addCriterion("customer_activity_rate like", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotLike(String value) {
            addCriterion("customer_activity_rate not like", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIn(List<String> values) {
            addCriterion("customer_activity_rate in", values, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotIn(List<String> values) {
            addCriterion("customer_activity_rate not in", values, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateBetween(String value1, String value2) {
            addCriterion("customer_activity_rate between", value1, value2, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotBetween(String value1, String value2) {
            addCriterion("customer_activity_rate not between", value1, value2, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIsNull() {
            addCriterion("customer_repurchase_rate is null");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIsNotNull() {
            addCriterion("customer_repurchase_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateEqualTo(String value) {
            addCriterion("customer_repurchase_rate =", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotEqualTo(String value) {
            addCriterion("customer_repurchase_rate <>", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateGreaterThan(String value) {
            addCriterion("customer_repurchase_rate >", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateGreaterThanOrEqualTo(String value) {
            addCriterion("customer_repurchase_rate >=", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLessThan(String value) {
            addCriterion("customer_repurchase_rate <", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLessThanOrEqualTo(String value) {
            addCriterion("customer_repurchase_rate <=", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLike(String value) {
            addCriterion("customer_repurchase_rate like", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotLike(String value) {
            addCriterion("customer_repurchase_rate not like", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIn(List<String> values) {
            addCriterion("customer_repurchase_rate in", values, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotIn(List<String> values) {
            addCriterion("customer_repurchase_rate not in", values, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateBetween(String value1, String value2) {
            addCriterion("customer_repurchase_rate between", value1, value2, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotBetween(String value1, String value2) {
            addCriterion("customer_repurchase_rate not between", value1, value2, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIsNull() {
            addCriterion("custom_model_customers is null");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIsNotNull() {
            addCriterion("custom_model_customers is not null");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersEqualTo(String value) {
            addCriterion("custom_model_customers =", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotEqualTo(String value) {
            addCriterion("custom_model_customers <>", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersGreaterThan(String value) {
            addCriterion("custom_model_customers >", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersGreaterThanOrEqualTo(String value) {
            addCriterion("custom_model_customers >=", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersLessThan(String value) {
            addCriterion("custom_model_customers <", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersLessThanOrEqualTo(String value) {
            addCriterion("custom_model_customers <=", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersLike(String value) {
            addCriterion("custom_model_customers like", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotLike(String value) {
            addCriterion("custom_model_customers not like", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIn(List<String> values) {
            addCriterion("custom_model_customers in", values, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotIn(List<String> values) {
            addCriterion("custom_model_customers not in", values, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersBetween(String value1, String value2) {
            addCriterion("custom_model_customers between", value1, value2, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotBetween(String value1, String value2) {
            addCriterion("custom_model_customers not between", value1, value2, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIsNull() {
            addCriterion("custom_scene_customers is null");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIsNotNull() {
            addCriterion("custom_scene_customers is not null");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersEqualTo(String value) {
            addCriterion("custom_scene_customers =", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotEqualTo(String value) {
            addCriterion("custom_scene_customers <>", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersGreaterThan(String value) {
            addCriterion("custom_scene_customers >", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersGreaterThanOrEqualTo(String value) {
            addCriterion("custom_scene_customers >=", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersLessThan(String value) {
            addCriterion("custom_scene_customers <", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersLessThanOrEqualTo(String value) {
            addCriterion("custom_scene_customers <=", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersLike(String value) {
            addCriterion("custom_scene_customers like", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotLike(String value) {
            addCriterion("custom_scene_customers not like", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIn(List<String> values) {
            addCriterion("custom_scene_customers in", values, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotIn(List<String> values) {
            addCriterion("custom_scene_customers not in", values, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersBetween(String value1, String value2) {
            addCriterion("custom_scene_customers between", value1, value2, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotBetween(String value1, String value2) {
            addCriterion("custom_scene_customers not between", value1, value2, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIsNull() {
            addCriterion("customer_protection_metrics is null");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIsNotNull() {
            addCriterion("customer_protection_metrics is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsEqualTo(Integer value) {
            addCriterion("customer_protection_metrics =", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotEqualTo(Integer value) {
            addCriterion("customer_protection_metrics <>", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsGreaterThan(Integer value) {
            addCriterion("customer_protection_metrics >", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_protection_metrics >=", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsLessThan(Integer value) {
            addCriterion("customer_protection_metrics <", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsLessThanOrEqualTo(Integer value) {
            addCriterion("customer_protection_metrics <=", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIn(List<Integer> values) {
            addCriterion("customer_protection_metrics in", values, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotIn(List<Integer> values) {
            addCriterion("customer_protection_metrics not in", values, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsBetween(Integer value1, Integer value2) {
            addCriterion("customer_protection_metrics between", value1, value2, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_protection_metrics not between", value1, value2, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountIsNull() {
            addCriterion("delivery_clothing_count is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountIsNotNull() {
            addCriterion("delivery_clothing_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountEqualTo(Integer value) {
            addCriterion("delivery_clothing_count =", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountNotEqualTo(Integer value) {
            addCriterion("delivery_clothing_count <>", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountGreaterThan(Integer value) {
            addCriterion("delivery_clothing_count >", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_clothing_count >=", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountLessThan(Integer value) {
            addCriterion("delivery_clothing_count <", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_clothing_count <=", value, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountIn(List<Integer> values) {
            addCriterion("delivery_clothing_count in", values, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountNotIn(List<Integer> values) {
            addCriterion("delivery_clothing_count not in", values, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountBetween(Integer value1, Integer value2) {
            addCriterion("delivery_clothing_count between", value1, value2, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryClothingCountNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_clothing_count not between", value1, value2, "deliveryClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountIsNull() {
            addCriterion("approve_clothing_count is null");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountIsNotNull() {
            addCriterion("approve_clothing_count is not null");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountEqualTo(Integer value) {
            addCriterion("approve_clothing_count =", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountNotEqualTo(Integer value) {
            addCriterion("approve_clothing_count <>", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountGreaterThan(Integer value) {
            addCriterion("approve_clothing_count >", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("approve_clothing_count >=", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountLessThan(Integer value) {
            addCriterion("approve_clothing_count <", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountLessThanOrEqualTo(Integer value) {
            addCriterion("approve_clothing_count <=", value, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountIn(List<Integer> values) {
            addCriterion("approve_clothing_count in", values, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountNotIn(List<Integer> values) {
            addCriterion("approve_clothing_count not in", values, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountBetween(Integer value1, Integer value2) {
            addCriterion("approve_clothing_count between", value1, value2, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveClothingCountNotBetween(Integer value1, Integer value2) {
            addCriterion("approve_clothing_count not between", value1, value2, "approveClothingCount");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateIsNull() {
            addCriterion("approve_error_rate is null");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateIsNotNull() {
            addCriterion("approve_error_rate is not null");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateEqualTo(String value) {
            addCriterion("approve_error_rate =", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateNotEqualTo(String value) {
            addCriterion("approve_error_rate <>", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateGreaterThan(String value) {
            addCriterion("approve_error_rate >", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateGreaterThanOrEqualTo(String value) {
            addCriterion("approve_error_rate >=", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateLessThan(String value) {
            addCriterion("approve_error_rate <", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateLessThanOrEqualTo(String value) {
            addCriterion("approve_error_rate <=", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateLike(String value) {
            addCriterion("approve_error_rate like", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateNotLike(String value) {
            addCriterion("approve_error_rate not like", value, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateIn(List<String> values) {
            addCriterion("approve_error_rate in", values, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateNotIn(List<String> values) {
            addCriterion("approve_error_rate not in", values, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateBetween(String value1, String value2) {
            addCriterion("approve_error_rate between", value1, value2, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andApproveErrorRateNotBetween(String value1, String value2) {
            addCriterion("approve_error_rate not between", value1, value2, "approveErrorRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateIsNull() {
            addCriterion("garment_rebate_rate is null");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateIsNotNull() {
            addCriterion("garment_rebate_rate is not null");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateEqualTo(String value) {
            addCriterion("garment_rebate_rate =", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateNotEqualTo(String value) {
            addCriterion("garment_rebate_rate <>", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateGreaterThan(String value) {
            addCriterion("garment_rebate_rate >", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateGreaterThanOrEqualTo(String value) {
            addCriterion("garment_rebate_rate >=", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateLessThan(String value) {
            addCriterion("garment_rebate_rate <", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateLessThanOrEqualTo(String value) {
            addCriterion("garment_rebate_rate <=", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateLike(String value) {
            addCriterion("garment_rebate_rate like", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateNotLike(String value) {
            addCriterion("garment_rebate_rate not like", value, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateIn(List<String> values) {
            addCriterion("garment_rebate_rate in", values, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateNotIn(List<String> values) {
            addCriterion("garment_rebate_rate not in", values, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateBetween(String value1, String value2) {
            addCriterion("garment_rebate_rate between", value1, value2, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andGarmentRebateRateNotBetween(String value1, String value2) {
            addCriterion("garment_rebate_rate not between", value1, value2, "garmentRebateRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateIsNull() {
            addCriterion("customer_complaint_rate is null");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateIsNotNull() {
            addCriterion("customer_complaint_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateEqualTo(String value) {
            addCriterion("customer_complaint_rate =", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateNotEqualTo(String value) {
            addCriterion("customer_complaint_rate <>", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateGreaterThan(String value) {
            addCriterion("customer_complaint_rate >", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateGreaterThanOrEqualTo(String value) {
            addCriterion("customer_complaint_rate >=", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateLessThan(String value) {
            addCriterion("customer_complaint_rate <", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateLessThanOrEqualTo(String value) {
            addCriterion("customer_complaint_rate <=", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateLike(String value) {
            addCriterion("customer_complaint_rate like", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateNotLike(String value) {
            addCriterion("customer_complaint_rate not like", value, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateIn(List<String> values) {
            addCriterion("customer_complaint_rate in", values, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateNotIn(List<String> values) {
            addCriterion("customer_complaint_rate not in", values, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateBetween(String value1, String value2) {
            addCriterion("customer_complaint_rate between", value1, value2, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCustomerComplaintRateNotBetween(String value1, String value2) {
            addCriterion("customer_complaint_rate not between", value1, value2, "customerComplaintRate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIsNull() {
            addCriterion("customer_total_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIsNotNull() {
            addCriterion("customer_total_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountEqualTo(Integer value) {
            addCriterion("customer_total_count =", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotEqualTo(Integer value) {    
            addCriterion("customer_total_count <>", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThan(Integer value) {
            addCriterion("customer_total_count >", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_total_count >=", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThan(Integer value) {
            addCriterion("customer_total_count <", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountLessThanOrEqualTo(Integer value) { 
            addCriterion("customer_total_count <=", value, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountIn(List<Integer> values) {
            addCriterion("customer_total_count in", values, "customerTotalCount");
            return (Criteria) this; 
        }

        public Criteria andCustomerTotalCountNotIn(List<Integer> values) {
            addCriterion("customer_total_count not in", values, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_total_count between", value1, value2, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_total_count not between", value1, value2, "customerTotalCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountIsNull() {
            addCriterion("video_count is null");
            return (Criteria) this;
        }

        public Criteria andVideoCountIsNotNull() {
            addCriterion("video_count is not null");
            return (Criteria) this;
        }

        public Criteria andVideoCountEqualTo(Integer value) {   
            addCriterion("video_count =", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountNotEqualTo(Integer value) {
            addCriterion("video_count <>", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountGreaterThan(Integer value) {
            addCriterion("video_count >", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("video_count >=", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountLessThan(Integer value) {
            addCriterion("video_count <", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountLessThanOrEqualTo(Integer value) {
            addCriterion("video_count <=", value, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountIn(List<Integer> values) {
            addCriterion("video_count in", values, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountNotIn(List<Integer> values) {
            addCriterion("video_count not in", values, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountBetween(Integer value1, Integer value2) {
            addCriterion("video_count between", value1, value2, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountNotBetween(Integer value1, Integer value2) {
            addCriterion("video_count not between", value1, value2, "videoCount");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgIsNull() {
            addCriterion("video_count_avg is null");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgIsNotNull() {
            addCriterion("video_count_avg is not null");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgEqualTo(String value) {
            addCriterion("video_count_avg =", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgNotEqualTo(String value) {
            addCriterion("video_count_avg <>", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgGreaterThan(String value) {
            addCriterion("video_count_avg >", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgGreaterThanOrEqualTo(String value) {
            addCriterion("video_count_avg >=", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgLessThan(String value) {
            addCriterion("video_count_avg <", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgLessThanOrEqualTo(String value) {
            addCriterion("video_count_avg <=", value, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgIn(List<String> values) {
            addCriterion("video_count_avg in", values, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgNotIn(List<String> values) {
            addCriterion("video_count_avg not in", values, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgBetween(String value1, String value2) {
            addCriterion("video_count_avg between", value1, value2, "videoCountAvg");   
            return (Criteria) this;
        }

        public Criteria andVideoCountAvgNotBetween(String value1, String value2) {
            addCriterion("video_count_avg not between", value1, value2, "videoCountAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountAvgIsNull() {
            addCriterion("customer_total_count_avg is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountAvgIsNotNull() {
            addCriterion("customer_total_count_avg is not null");   
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountAvgEqualTo(String value) {
            addCriterion("customer_total_count_avg =", value, "customerTotalCountAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountAvgNotEqualTo(String value) {
            addCriterion("customer_total_count_avg <>", value, "customerTotalCountAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerTotalCountAvgGreaterThan(String value) {
            addCriterion("customer_total_count_avg >", value, "customerTotalCountAvg");
            return (Criteria) this;
        }

        public Criteria andCustomerUploadMaterialCountIsNull() {
            addCriterion("customer_upload_material_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerUploadMaterialCountIsNotNull() {
            addCriterion("customer_upload_material_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerUploadMaterialCountEqualTo(Integer value) {
            addCriterion("customer_upload_material_count =", value, "customerUploadMaterialCount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}