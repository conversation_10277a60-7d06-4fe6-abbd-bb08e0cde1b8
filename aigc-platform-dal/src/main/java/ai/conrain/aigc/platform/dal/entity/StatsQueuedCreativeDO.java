/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 统计队列中的创作DO
 *
 * <AUTHOR>
 * @version : StatsQueuedCreativeDO.java, v 0.1 2025/5/13 00:46 renxiao.wu Exp $
 */
@Data
public class StatsQueuedCreativeDO implements Serializable {
    private static final long serialVersionUID = -8292901424550307949L;
    /** 商家等待队列长度 */
    private Integer merchantCnt;
    /** 系统等待队列长度 */
    private Integer systemCnt;
    /** 后台用户等待队列长度 */
    private Integer backUserCnt;
}
