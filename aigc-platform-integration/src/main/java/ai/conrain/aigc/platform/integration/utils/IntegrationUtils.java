package ai.conrain.aigc.platform.integration.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.org.apache.commons.lang.time.DateFormatUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.UUID;

@Slf4j
public class IntegrationUtils {

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public static boolean isValidJsonArray(String s) {
        try {
            return JSONArray.parse(s) != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isValidJsonObject(String json) {
        try {
            return JSONObject.parseObject(json) != null;
        } catch (Exception e){
            return false;
        }
    }

    public static String getOssBucketName(String ossFileUrl){
        OssUrlParser.ParsedOssUrl parsedUrlWithSignature = OssUrlParser.parseOssUrl(ossFileUrl);
        return parsedUrlWithSignature != null ? parsedUrlWithSignature.getBucketName() : null;
    }

    /**
     * 从url中获取文件名
     * https://a.com/b.mp4?c=d => b.mp4
     * @param fileUrl 路径字符串
     * @return 带扩展名的文件名部分
     */
    public static String getFileNameFromUrl(String fileUrl) {
        String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        if (fileName.contains("?")) {
            return fileName.substring(0, fileName.indexOf('?'));
        } else {
            return fileName;
        }
    }

    /**
     * 构建oss对象名
     * @param fileName 文件名
     * @param suffix 后缀
     * @param userId 用户id
     * @param ext 扩展名（可选）
     * @return 对象名
     * @throws IllegalArgumentException 当无法解析文件扩展名时抛出
     */
    public static String buildOssObjectName(Integer userId, String fileName, String suffix, String ext) {
        String finalExt = "";
        String originExt = getFileExtensionFromUrl(fileName);
        // 确定使用的扩展名
        if (StringUtils.isNotBlank(ext)) {
            finalExt = ext;
        } else if (StringUtils.isNotBlank(originExt)) {
            finalExt = originExt;
        } else {
            throw new IllegalArgumentException("文件扩展名解析失败，请手动传入扩展名");
        }

        if (StringUtils.isBlank(finalExt)) {
            return null;
        }
        
        // 使用已有方法正确获取文件名（自动处理查询参数）
        String fileNameOnly = getFileNameFromUrl(fileName);

        // 获取文件名中不含扩展名的部分
        String nameWithoutExt = fileNameOnly;
        int dotIndex = fileNameOnly.lastIndexOf('.');
        if (dotIndex > 0) {
            nameWithoutExt = fileNameOnly.substring(0, dotIndex);
        }

        // 如果文件名为空，则使用UUID
        if (StringUtils.isBlank(nameWithoutExt)) {
            nameWithoutExt = IntegrationUtils.uuid();
        } else {
            // 清除文件名中不应该存在的字符（只保留字母、数字、下划线和连字符）
            nameWithoutExt = nameWithoutExt.replaceAll("[^a-zA-Z0-9_\\-]", "");
        }

        // 构建OSS对象名称，格式：年月/用户ID/文件名.后缀
        String yearMonth = DateFormatUtils.format(new Date(), "yyyyMM");
        StringBuilder builder = new StringBuilder(yearMonth);
        
        // 添加用户ID部分
        if (userId != null) {
            builder.append("/").append(userId);
        }
        
        // 添加文件名部分
        builder.append("/").append(nameWithoutExt);
        
        // 添加后缀部分
        if (StringUtils.isNotBlank(suffix)) {
            builder.append("_").append(suffix);
        }
        
        // 添加扩展名
        builder.append(".").append(finalExt);
        
        return builder.toString();
    }

    /**
     * 从url中获取文件扩展名
     * 注意返回值没有.点号，如 jpg
     * @param fileUrl
     * @return
     */
    public static String getFileExtensionFromUrl(String fileUrl){
        return getFileExtensionFromPath(getFileNameFromUrl(fileUrl));
    }

    /**
     * 获取文件的扩展名 a.mp4 => mp4
     * @param fileName 文件名（包括路径）
     * @return 文件的扩展名，如果没有扩展名则返回空字符串
     */
    public static String getFileExtensionFromPath(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }

        // 获取文件名中最后一个点（.）的位置
        int dotIndex = fileName.lastIndexOf('.');

        // 如果没有点，或者点在文件名的开头（隐藏文件），则没有扩展名
        if (dotIndex < 0 || dotIndex == 0) {
            return "";
        }

        // 返回点之后的部分作为扩展名
        return fileName.substring(dotIndex + 1);
    }

    /**
     * 从ossUrl中获取objectName
     * https://aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com/202408/100007/product_92305_1723170828_4967248_0_RtYIb.png?Expires=3299970833&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49Rh&Signature=
     * => 202408/100007/product_92305_1723170828_4967248_0_RtYIb.png
     * @param ossUrl
     * @return
     */
    public static String getOssObjectNameFromUrl(String ossUrl) {
        String fileName = StringUtils.substringBeforeLast(ossUrl, "?");
        fileName = StringUtils.substringAfter(fileName, "://");
        fileName = StringUtils.substringAfter(fileName, "/");

        return fileName;
    }

    /**
     * 从 OSS URL 中获取 fileKeyDir（目录部分）
     * 当输入的 ossUrl 为：https://aigc-platform-dev.oss-cn-zhangjiakou.aliyuncs.com/202408/100007/product_92305_1723170828_4967248_0_RtYIb.png?Expires=3299970833&OSSAccessKeyId=LTAI5tGtrXAPAtQoPoBw49R
     * => 202408/100007
     * @param ossUrl OSS URL
     * @return fileKeyDir
     */
    public static String getOssFileDirFromUrl(String ossUrl) {
        // 获取 objectName
        String objectName = getOssObjectNameFromUrl(ossUrl);

        // 获取最后一个 '/' 的位置
        int lastSlashIndex = objectName.lastIndexOf('/');

        // 如果没有 '/'，则表示没有目录部分
        if (lastSlashIndex < 0) {
            return "";
        }

        // 截取目录部分
        return objectName.substring(0, lastSlashIndex);
    }



    /**
     * 递归处理对象中的"None"字符串，将其转换为null
     *
     * @param obj 要处理的对象
     */
    public static void processNoneToNull(Object obj) {
        if (obj == null) {
            return;
        }

        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(obj);

                if (value == null) {
                    continue;
                }

                // 处理String类型字段
                if (value instanceof String) {
                    String strValue = (String) value;
                    if ("None".equals(strValue)) {
                        field.set(obj, null);
                    }
                }
                // 递归处理嵌套对象
                else if (!isPrimitiveOrWrapper(value.getClass()) && !value.getClass().getName().startsWith("java.")) {
                    processNoneToNull(value);
                }
            } catch (IllegalAccessException e) {
                log.warn("Failed to process field: {}", field.getName(), e);
            }
        }
    }

    /**
     * 判断是否为基本类型或其包装类
     */
    private static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz == Boolean.class ||
                clazz == Byte.class ||
                clazz == Character.class ||
                clazz == Short.class ||
                clazz == Integer.class ||
                clazz == Long.class ||
                clazz == Float.class ||
                clazz == Double.class;
    }
}
