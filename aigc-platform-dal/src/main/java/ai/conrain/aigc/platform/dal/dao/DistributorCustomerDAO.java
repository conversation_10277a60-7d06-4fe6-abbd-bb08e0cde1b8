package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.DistributorCustomerDO;
import ai.conrain.aigc.platform.dal.example.DistributorCustomerExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DistributorCustomerDAO {
    long countByExample(DistributorCustomerExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DistributorCustomerDO record);

    int insertSelective(DistributorCustomerDO record);

    List<DistributorCustomerDO> selectByExample(DistributorCustomerExample example);

    DistributorCustomerDO selectByPrimaryKey(Integer id);

    DistributorCustomerDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") DistributorCustomerDO record, @Param("example") DistributorCustomerExample example);

    int updateByExample(@Param("record") DistributorCustomerDO record, @Param("example") DistributorCustomerExample example);

    int updateByPrimaryKeySelective(DistributorCustomerDO record);

    int updateByPrimaryKey(DistributorCustomerDO record);

    int logicalDeleteByExample(@Param("example") DistributorCustomerExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}