package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 搜索结果图片
 * 对应数据表：search_result_img
 */
@Data
public class SearchResultImgDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 搜索记录id
     */
    private Integer searchId;

    /**
     * 搜索结果批次，唯一标识符
     */
    private String searchRetBatch;

    /**
     * 图片id
     */
    private Integer imageId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 图片展示url
     */
    private String imageShowUrl;

    /**
     * 图片打标id
     */
    private Integer imageCaptionId;

    /**
     * 图片类别
     */
    private String genre;

    /**
     * 背景聚类index
     */
    private String bgClusterId;

    /**
     * 风格相似度
     */
    private Double styleSimilarity;

    /**
     * 匹配得分
     */
    private Double matchScore;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}