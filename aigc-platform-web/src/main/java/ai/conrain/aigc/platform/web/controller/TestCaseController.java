package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.TestCaseService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.TestCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * TestCase控制器
 *
 * <AUTHOR>
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testCase")
public class TestCaseController {

    /** testCaseService */
    @Autowired
    private TestCaseService testCaseService;

    @GetMapping("/getById/{id}")
    public Result<TestCaseVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(testCaseService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody TestCaseVO testCase) {
        return Result.success(testCaseService.insert(testCase).getId());
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        testCaseService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody TestCaseVO testCase) {
        testCaseService.updateByIdSelective(testCase);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<TestCaseVO>> queryTestCaseList(@Valid @RequestBody TestCaseQuery query) {
        return Result.success(testCaseService.queryTestCaseList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<TestCaseVO>> getTestCaseByPage(@Valid @RequestBody TestCaseQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(testCaseService.queryTestCaseByPage(query));
    }

    @PostMapping("/importFromExcel")
    public Result<?> importFromExcel(@NotNull @RequestParam("file") MultipartFile file,
                                     @NotBlank @RequestParam("name") String name,
                                     @NotBlank @RequestParam("type") String type) throws IOException {
        TestCaseVO testCase = testCaseService.importFromExcel(name, type, file);
        return Result.success(testCase);
    }
}