package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.ShootingStyleVO;
import ai.conrain.aigc.platform.service.model.query.ShootingStyleQuery;
import ai.conrain.aigc.platform.service.component.ShootingStyleService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ShootingStyle控制器
 *
 * <AUTHOR>
 * @version ShootingStyleService.java v 0.1 2025-07-04 05:16:37
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/shootingStyle")
public class ShootingStyleController {

	/** shootingStyleService */
	@Autowired
	private ShootingStyleService shootingStyleService;
	
	@GetMapping("/getById/{id}")
	public Result<ShootingStyleVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(shootingStyleService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ShootingStyleVO shootingStyle){
		try {
			ShootingStyleVO data = shootingStyleService.insert(shootingStyle);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加拍摄风格配置失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建拍摄风格配置失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		shootingStyleService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody ShootingStyleVO shootingStyle){
		shootingStyleService.updateById(shootingStyle);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<ShootingStyleVO>> findAll(){
		return Result.success(shootingStyleService.findAll());
	}

}
