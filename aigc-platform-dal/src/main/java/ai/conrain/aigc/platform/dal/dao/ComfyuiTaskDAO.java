package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ComfyuiTaskDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ComfyuiTaskDAO {
    long countByExample(ComfyuiTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ComfyuiTaskDO record);

    int insertSelective(ComfyuiTaskDO record);

    List<ComfyuiTaskDO> selectByExampleWithBLOBs(ComfyuiTaskExample example);

    List<ComfyuiTaskDO> selectByExample(ComfyuiTaskExample example);

    ComfyuiTaskDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ComfyuiTaskDO record, @Param("example") ComfyuiTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") ComfyuiTaskDO record, @Param("example") ComfyuiTaskExample example);

    int updateByExample(@Param("record") ComfyuiTaskDO record, @Param("example") ComfyuiTaskExample example);

    int updateByPrimaryKeySelective(ComfyuiTaskDO record);

    int updateByPrimaryKeyWithBLOBs(ComfyuiTaskDO record);

    int updateByPrimaryKey(ComfyuiTaskDO record);
}