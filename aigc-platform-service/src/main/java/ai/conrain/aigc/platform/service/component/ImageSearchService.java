package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.agent.ShootStyleImageRecommendParam;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendResult;

/**
 * 图像检索服务
 */
public interface ImageSearchService {

    /**
     * 搜索并推荐
     * @param param
     * @return
     */
    StyleImageRecommendResult searchAndRecommend(ShootStyleImageRecommendParam param);

    /**
     * 根据服装图片搜索并建立配对，用于训练排序模型
     */
    void searchByClothImgAndBuildPairs(MaterialModelVO cloth) throws InterruptedException;
}
