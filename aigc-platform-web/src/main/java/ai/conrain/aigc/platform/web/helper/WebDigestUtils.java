/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.helper;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * web层摘要日志工具类
 *
 * <AUTHOR>
 * @version : WebDigestUtils.java, v 0.1 2023/10/15 18:27 renxiao.wu Exp $
 */
public abstract class WebDigestUtils {
    /** 日志 */
    private static final Logger DIGEST_LOG = LoggerFactory.getLogger("web-digest");

    /**
     * 打印摘要日志
     */
    public static void digest(String action, String ip, boolean succ, ResultCode code) {
        OperationContext context = OperationContextHolder.getContext();
        long start = context.getActionStartTime();

        StringBuilder sb = new StringBuilder("[web-digest],");
        sb.append(action).append(",");

        RoleTypeEnum roleType = context.getRoleType();
        sb.append(roleType != null ? roleType : "-").append(",");
        boolean fromMiniApp = context.isRequestFromMiniApp();
        sb.append(fromMiniApp ? "wx" : "pc").append(",");

        sb.append(ip).append(",");
        sb.append(succ ? "Y" : "N").append(",");
        sb.append(code != null ? code : "-").append(",");

        sb.append(System.currentTimeMillis() - start).append("ms");

        sb.append(",").append(context.getOpKey() != null ? context.getOpKey() : "-");

        DIGEST_LOG.info(sb.toString());
    }
}
