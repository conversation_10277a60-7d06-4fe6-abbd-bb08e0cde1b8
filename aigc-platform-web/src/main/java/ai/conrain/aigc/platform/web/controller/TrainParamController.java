package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.TrainParamDO;
import ai.conrain.aigc.platform.service.model.vo.TrainParamVO;
import ai.conrain.aigc.platform.service.model.query.TrainParamQuery;
import ai.conrain.aigc.platform.service.component.TrainParamService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * TrainParam控制器
 *
 * <AUTHOR>
 * @version TrainParamService.java v 0.1 2024-11-19 08:51:42
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/trainParam")
public class TrainParamController {

	/** trainParamService */
	@Autowired
	private TrainParamService trainParamService;
	
	@GetMapping("/getById/{id}")
	public Result<TrainParamVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(trainParamService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TrainParamVO trainParam){
		try {
			TrainParamVO data = trainParamService.insert(trainParam);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		trainParamService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody TrainParamVO trainParam){
		trainParamService.updateById(trainParam);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<TrainParamVO>> findAll(){
		return Result.success(trainParamService.findAll());
	}

}
