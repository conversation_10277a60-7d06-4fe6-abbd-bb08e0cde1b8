/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.service.component.PermissionService;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.web.helper.IpUtils;
import ai.conrain.aigc.platform.web.helper.MessageHelper;
import ai.conrain.aigc.platform.web.helper.WebDigestUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.ServletRequest;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @version : GlobalExceptionHandler.java, v 0.1 2023/9/4 00:12 renxiao.wu Exp $
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    @Autowired
    private MessageHelper messageHelper;
    @Autowired
    private PermissionService permissionService;

    @ExceptionHandler(Throwable.class)
    public Result<?> handle(Throwable e, ServletRequest request) {
        try {
            if (e instanceof BizException) {
                log.warn("业务异常", e);
                return buildResult(((BizException)e).getCode(), e.getMessage(), request);
            }

            if (e instanceof CannotAcquireLockException) {
                log.error("抢锁失败", e);
                return buildResult(ResultCode.CANNOT_ACQUIRE_LOCK, null, request);
            }

            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper)request;
                log.error("异常请求," + getReq(wrapper));
            }

            if (e instanceof ConstraintViolationException) {
                List<String> list = ((ConstraintViolationException)e).getConstraintViolations().stream().map(
                        ConstraintViolation::getMessage).collect(Collectors.toList());
                String msg = fetchError(list);
                log.error("非法的请求参数1,error={}", msg);
                return buildResult(ResultCode.PARAM_INVALID, msg, request);
            }

            if (e instanceof BindException) {
                List<String> list = ((BindException)e).getAllErrors().stream().map(this::parseErr).collect(
                        Collectors.toList());
                String msg = fetchError(list);
                log.error("非法的请求参数2,error={}", msg);
                return buildResult(ResultCode.PARAM_INVALID, msg, request);
            }

            if (e instanceof MethodArgumentNotValidException) {
                List<String> list = ((MethodArgumentNotValidException)e).getBindingResult().getAllErrors().stream().map(
                        this::parseErr).collect(Collectors.toList());
                String msg = fetchError(list);
                log.error("非法的请求参数3,error={}", msg);
                return buildResult(ResultCode.PARAM_INVALID, msg, request);
            }

            if (e instanceof MethodArgumentTypeMismatchException) {
                log.error("非法的请求参数4,{}", e.getMessage());
                return buildResult(ResultCode.PARAM_INVALID, null, request);
            }

            log.error("未知异常", e);
            return buildResult(ResultCode.SYS_ERROR, null, request);

        } catch (Throwable t){
            log.error("GlobalExceptionHandler接收到异常", e);
            log.error("GlobalExceptionHandler处理异常时发生异常", t);
            return buildResult(ResultCode.SYS_ERROR, null, request);
        }
    }

    private String getReq(ContentCachingRequestWrapper request) {

        StringBuilder msg = new StringBuilder();
        try {
            msg.append(request.getMethod()).append(" ");
            msg.append(request.getRequestURI());
            String queryString = request.getQueryString();
            if (queryString != null) {
                msg.append('?').append(queryString);
            }
            String payload = new String(request.getContentAsByteArray(), StandardCharsets.UTF_8);
            msg.append(",payload:").append(CommonUtil.formatJsonOneRow(payload));

            return msg.toString();
        } catch (Throwable t) {
            log.warn("组装请求日志异常，忽略", t);
            return msg.toString();
        }
    }

    /**
     * 构建异常结果
     *
     * @param code    结果码
     * @param message 消息内容
     * @return 异常结果
     */
    private Result<?> buildResult(ResultCode code, String message, ServletRequest servletRequest) {
       try {
           Result<?> ret = null;

           if (servletRequest instanceof HttpServletRequest) {
               HttpServletRequest request = (HttpServletRequest)servletRequest;
               String action = permissionService.parseRequestPath(request.getServletPath());
               WebDigestUtils.digest(action, IpUtils.getIpAddr(request), false, code);
           }

           ret = Result.failedWithMessage(code, messageHelper.fetchErrorMsg(code, message));

           log.info("buildResult构建异常结果:{}", JSONObject.toJSONString(ret));
           return ret;

       } catch (Throwable t){
           log.error("摘要日志异常", t);
           return Result.failedWithMessage(ResultCode.SYS_ERROR, null);
       }
    }

    private String parseErr(ObjectError objectError) {
        if (objectError instanceof FieldError) {
            FieldError fieldError = (FieldError)objectError;
            return (fieldError.getField() != null ? (fieldError.getField() + ": ") : "") + fieldError.getDefaultMessage();
        } else {
            return (objectError.getObjectName() != null ? (objectError.getObjectName() + ":") : "") +  objectError.getDefaultMessage();
        }
    }

    private String fetchError(List<String> errors) {
        if (CollectionUtils.isEmpty(errors)) {
            return null;
        }

        return errors.get(0);
    }
}
