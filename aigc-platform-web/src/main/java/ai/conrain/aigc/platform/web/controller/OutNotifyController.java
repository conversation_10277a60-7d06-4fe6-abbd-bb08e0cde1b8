package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.invoice.FPApiService;
import ai.conrain.aigc.platform.service.component.InvoiceInfoService;
import ai.conrain.aigc.platform.service.enums.InvoiceStatus;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.query.InvoiceInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 外部平台的异步通知接口
 */
@Slf4j
@Controller
public class OutNotifyController {

    @Autowired
    private FPApiService fpApiService;

    @Autowired
    private InvoiceInfoService invoiceInfoService;

    @Autowired
    private OssService ossService;

    /**
     * 咻图异步通知
     * https://docs.aixtsy.com/guide/api#%E5%BC%82%E6%AD%A5%E9%80%9A%E7%9F%A5%E8%AF%B7%E6%B1%82%E5%8F%82%E6%95%B0
     * 请求示例：
     * {
     *   "status": 200,
     *   "message": "success",
     *   "data": {
     *     "id": "添加任务时候的ID",
     *     "url": "效果图url", // 该地址是您指定`retKey`时上传生成的
     *     "auth_url": "授权了的url、使用自己的OSS不授权",
     *     "attach": "添加任务时候的attach透传参数" // 注意它是string类型
     *   }
     * }
     *
     * 响应示例：
     * {
     *   "code": 200, // 成功时200，其他情况均为失败
     *   "message": "OK", // 失败原因
     *   "data": {
     *     "id": "60497c617ee7d24d0c2b72f6" // 本次添加的任务照片任务ID
     *   }
     * }
     * @param notify
     * @return
     */
    @PostMapping("/notify/xiutu")
    @ResponseBody
    public JSONObject xiutuNotify(@RequestBody JSONObject notify) {
        log.info("接收到咻图通知:{}", notify);

        JSONObject res = new JSONObject();
        res.put("code", 200);
        res.put("message", "OK");

        JSONObject data = new JSONObject();
        if (notify.containsKey("data")) {
            data.put("id", notify.getJSONObject("data").getString("id"));
        }

        res.put("data", data);

        return res;
    }

    /**
     * 发票API异步通知
     * https://fpapi.com/doc/#/page/renwuHuiDiao
     *
     * 请求示例：
     * {
     *   "task_id": "2PmHR63vW9VJF5HUveUDSpNS",
     *   "task_type": 11,
     *   "task_state": 1,
     *   "task_msg": "success",
     *   "company_id": "L47xj3KCX7khBcTxD1DyD0sb",
     *   "invoice_list": [
     *     {
     *       "custom_invoice_no": "G2MKbb35",
     *       "status": 1,
     *       "message": "[成功]-",
     *       "electronic_invoice_no": "23442000000047645422",
     *       "invoice_url": "https://p.fpapi.com/nm3C79KE6RMcMAwW1cjF6QpD"
     *     },
     *     {
     *       "custom_invoice_no": "9MYLtciN",
     *       "status": 2,
     *       "message": "[失败]未查询到购买方信息，且未进行批量设置",
     *       "redraw_task_id": "dG85gHCv4kHQ63LuS7Uk97W6"
     *     },
     *     {
     *       "custom_invoice_no": "dm4TBL7E",
     *       "status": -1,
     *       "message": "[失败]购销方不能为同一家企业"
     *     }
     *   ]
     * }
     *
     * 开票成功回调请求：
     * {
     *   "task_id": "LhaSFyqK586A6C6gqh8bWYT9",
     *   "task_type": 11,
     *   "task_state": 1,
     *   "task_msg": "success",
     *   "company_id": "a3SEKmrK8xrpFf3Rgh2MNG0Y",
     *   "invoice_list": [
     *     {
     *       "status": 1,
     *       "custom_invoice_no": "209c3d4d725d484e",
     *       "message": "[成功]-",
     *       "electronic_invoice_no": "25332000000058356174",
     *       "invoice_url": "https://p.fpapi.com/3y3BW4yT7fyTb1vYEbX0PE8u",
     *       "pdf": "https://resource.zijizhang.com/tax_report/202502/91330110MACTWANK7G/8cbb.dzfp_25332000000058356174.pdf",
     *       "xml": "https://resource.zijizhang.com/tax_report/202502/25332000000058356174/7c41.dzfp_25332000000058356174_xml.zip"
     *     }
     *   ]
     * }
     */
    @PostMapping("/notify/fpapi")
    @ResponseBody
    public JSONObject fpapiNotify(@RequestHeader Map<String, String> headers, @RequestBody JSONObject notify) {
        try {
            log.info("接收到发票API通知，header:{}, notify:{}", headers, notify);

            // TODO: 验签

            // 解析任务结果
            Integer taskType = notify.getInteger("task_type");
            AssertUtil.assertNotNull(taskType, "任务类型不能为空");

            String taskId = notify.getString("task_id");
            AssertUtil.assertNotBlank(taskId, "taskId不能为空");

            //任务状态，表明系统处理结果，不代表发票开具结果
            Integer taskState = notify.getInteger("task_state");
            AssertUtil.assertNotNull(taskState, "taskState不能为空");

            switch (taskState) {
                //任务中断，可通过重发任务 接口重发任务
                case -2: {
                    log.warn("发票开具任务中断，进行重发，taskId:{}", taskId);
                    fpApiService.retryInvoice(taskId);
                    break;
                }
                //执行错误
                case -1: {
                    log.warn("发票开具任务执行错误，taskId:{}", taskId);
                    DingTalkNoticeHelper.sendMsg2DevGroup(String.format("三方开票结果失败\ntraceId：%s", MDC.get("traceId")), Collections.singletonList("15906660486"));
                    break;
                }
                //处理成功，不一定发票生成成功，也可能重开发票
                case 1: {
                    // 根据任务类型处理不同的任务结果
                    switch (taskType) {
                        // 发票开具任务
                        case 11: {
                            JSONArray invoiceList = notify.getJSONArray("invoice_list");
                            if (invoiceList != null) {
                                for (int i = 0; i < invoiceList.size(); i++) {
                                    JSONObject notifyInvoiceItem = invoiceList.getJSONObject(i);
                                    handleBlueInvoice(notifyInvoiceItem);
                                }
                            }
                            break;
                        }
                        // 重开发票任务
                        case 1101: {
                            JSONObject notifyInvoiceItem = notify.getJSONObject("invoice");
                            if (notifyInvoiceItem != null) {
                                handleBlueInvoice(notifyInvoiceItem);
                            }
                            break;
                        }
                        // 红冲发票任务
                        case 13: {
                            handleRedInvoice(notify);
                            break;
                        }
                        default: {
                            log.warn("未知的任务类型: {}", taskType);
                            break;
                        }
                    }

                }
                //执行中，可能需要扫脸验证
                case 2: {
                    //需要人脸验证
                    if (notify.containsKey("face_scan_info")) {
                        log.warn("发票开具任务执行中，taskId:{}，需要进行扫脸，扫脸信息:{}", taskId, notify.getJSONObject("face_scan_info"));
                        //钉钉消息报警，圈松然
                        DingTalkNoticeHelper.sendMsg2DevGroup(String.format("发票开具任务执行中，需要开票员扫脸\ntraceId：%s", MDC.get("traceId")), Collections.singletonList("15906660486"));
                    }
                    break;
                }
                //需要提交验证码，理论上因为在浙江，这里永远走不到
                case 3: {
                    log.error("需要提交验证码，理论上因为在浙江，这里永远走不到");
                    break;
                }
            }

        } catch (Exception e) {
            log.error("处理发票API任务结果异常", e);
        }

        //全部响应成功，发票api后台可以手工重发回调通知
        JSONObject res = new JSONObject();
        res.put("code", 200);
        res.put("message", "OK");

        return res;
    }

    //开发票的处理，蓝票
    private void handleBlueInvoice(JSONObject notifyInvoiceItem) {
        AssertUtil.assertNotNull(notifyInvoiceItem, "invoice is null");

        Integer status = notifyInvoiceItem.getInteger("status");
        AssertUtil.assertNotNull(status, "invoice.status is null");

        // 根据发票状态进行处理，发票开具状态， -1：开票失败，0：刚创建，1：开票成功，2：等待重开
        switch (status) {
            //开票失败
            case -1:{
                //钉钉消息报警，圈松然
                DingTalkNoticeHelper.sendMsg2DevGroup(String.format("三方开票结果失败\ntraceId：%s", MDC.get("traceId")), Collections.singletonList("15906660486"));
                break;
            }
            //开票成功
            case 1: {
                onNormalInvoiceSuccess(notifyInvoiceItem);
                break;
            }
            /**
             * 2：等待重开-失败
             * 目前只有这种情况：
             * {
             *       "custom_invoice_no": "9MYLtciN",
             *       "status": 2,
             *       "message": "[失败]未查询到购买方信息，且未进行批量设置",
             *       "redraw_task_id": "dG85gHCv4kHQ63LuS7Uk97W6"
             *     },
             */
            case 2: {
                //钉钉消息报警，圈松然
                DingTalkNoticeHelper.sendMsg2DevGroup(String.format("三方开票结果失败，等待重开\ntraceId：%s", MDC.get("traceId")), Collections.singletonList("15906660486"));
                break;
            }
            default:
                log.warn("未知的发票状态: {}", status);
                break;
        }
    }

    //处理红冲发票

    /**
     * {
     *   "task_id": "twVKmJh9fVmexSrGfytVA4L0",
     *   "task_type": 13,
     *   "task_state": 1,
     *   "task_msg": "success",
     *   "company_id": "G4DJAKEDf7cCFCC3EqFWkgNW",
     *   "be_offset_invoice": {
     *     "electronic_invoice_no": "23442000000178501228",
     *     "draw_date": "2023-08-28"
     *   },
     *   "offset_invoice": {
     *     "status": 1,
     *     "message": "[成功]-",
     *     "offset_invoice_no": "23442000000180092939",
     *     "invoice_url": "https://p.fpapi.com/80cnDLWX5xfmHxFWrvH4UUFm"
     *   }
     * }
     */
    private void handleRedInvoice(JSONObject notify){
        JSONObject originInvoice = notify.getJSONObject("be_offset_invoice");
        AssertUtil.assertNotNull(originInvoice, "originInvoice is null");

        JSONObject offsetInvoice = notify.getJSONObject("offset_invoice");
        AssertUtil.assertNotNull(offsetInvoice, "offsetInvoice is null");

        Integer status = offsetInvoice.getInteger("status");
        AssertUtil.assertNotNull(status, "offsetInvoice.status is null");

        //红冲票成功
        if (status == 1) {
            onOffsetInvoiceSuccess(notify, originInvoice, offsetInvoice);

            //红冲失败
        } else {
            //钉钉消息报警，圈松然
            DingTalkNoticeHelper.sendMsg2DevGroup(String.format("[发票红冲]三方开票结果失败\ntraceId：%s", MDC.get("traceId")), Collections.singletonList("15906660486"));
        }
    }

    //红冲成功
    private void onOffsetInvoiceSuccess(JSONObject notify, JSONObject originInvoice, JSONObject offsetInvoice) {
        //被红冲的发票号码
        String originInvoiceNo = originInvoice.getString("electronic_invoice_no");
        AssertUtil.assertNotBlank(originInvoiceNo, "originInvoiceNo is blank");

        //根据invoiceNo查询发票信息，更新发票信息
        InvoiceInfoQuery invoiceInfoQuery = new InvoiceInfoQuery();
        invoiceInfoQuery.setInvoiceNo(originInvoiceNo);
        List<InvoiceInfoVO> invoices = invoiceInfoService.queryInvoiceInfoList(invoiceInfoQuery);
        AssertUtil.assertTrue(invoices != null && invoices.size() == 1, "目标发票记录不存在或数量不为1");

        InvoiceInfoVO invoiceInfo = invoices.get(0);

        String offsetInvoiceNo = offsetInvoice.getString("offset_invoice_no");
        AssertUtil.assertNotBlank(offsetInvoiceNo, "offsetInvoiceNo is blank");
        invoiceInfo.setNegativeInvoiceNo(offsetInvoiceNo);
        invoiceInfo.setStatus(InvoiceStatus.INVOICE_REVERSED.getCode());

        JSONObject negativeInvoiceDetail = new JSONObject();
        {
            //红冲申请信息，negativeInvoiceTaskId/negativeInvoiceApplyTime
            //@see ai.conrain.aigc.platform.service.component.impl.InvoiceInfoServiceImpl.applyReverseInvoice
            if (CommonUtil.isValidJson(invoiceInfo.getNegativeInvoiceDetail())) {
                negativeInvoiceDetail.putAll(JSONObject.parseObject(invoiceInfo.getNegativeInvoiceDetail()));
            }

            negativeInvoiceDetail.putAll(offsetInvoice);
            negativeInvoiceDetail.put("negativeInvoiceFinishTime", DateUtils.formatTime(new Date()));

            String invoiceUrl = offsetInvoice.getString("pdf");
            if (StringUtils.isNotBlank(invoiceUrl)) {
                String innerInvoiceUrl = ossService.fetchStreamAndUpload(invoiceUrl);
                negativeInvoiceDetail.put("negativeInvoiceFileUrl", innerInvoiceUrl);
            }
        }

        invoiceInfo.setNegativeInvoiceDetail(negativeInvoiceDetail.toJSONString());
        invoiceInfoService.updateByIdSelective(invoiceInfo);
    }

    //正常蓝字发票开票成功
    private void onNormalInvoiceSuccess(JSONObject notifyInvoiceItem) {
        String customInvoiceNo = notifyInvoiceItem.getString("custom_invoice_no");
        AssertUtil.assertNotBlank(customInvoiceNo, "customInvoiceNo is blank");

        String invoiceUrl = notifyInvoiceItem.getString("pdf");

        //根据invoiceNo查询发票信息，更新发票信息
        InvoiceInfoQuery invoiceInfoQuery = new InvoiceInfoQuery();
        invoiceInfoQuery.setInnerInvoiceNo(customInvoiceNo);
        List<InvoiceInfoVO> invoices = invoiceInfoService.queryInvoiceInfoList(invoiceInfoQuery);
        AssertUtil.assertTrue(invoices != null && invoices.size() == 1, "notifyInvoiceItem is null");

        InvoiceInfoVO invoiceInfo = invoices.get(0);
        invoiceInfo.setStatus(InvoiceStatus.INVOICE_END.getCode());
        invoiceInfo.setFinishTime(new Date());
        invoiceInfo.setInvoiceNo(notifyInvoiceItem.getString("electronic_invoice_no"));
        invoiceInfo.setInvoiceTaskDetail(JSONObject.toJSONString(notifyInvoiceItem));

        AssertUtil.assertNotBlank(invoiceUrl, "invoiceUrl is blank");

        String innerInvoiceUrl = ossService.fetchStreamAndUpload(invoiceUrl);
        invoiceInfo.setInvoiceDownloadUrl(innerInvoiceUrl);

        invoiceInfoService.updateByIdSelective(invoiceInfo);
    }
}