package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TestCaseDO;
import ai.conrain.aigc.platform.dal.example.TestCaseExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestCaseDAO {
    long countByExample(TestCaseExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TestCaseDO record);

    int insertSelective(TestCaseDO record);

    List<TestCaseDO> selectByExampleWithBLOBs(TestCaseExample example);

    List<TestCaseDO> selectByExample(TestCaseExample example);

    TestCaseDO selectByPrimaryKey(Integer id);

    TestCaseDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") TestCaseDO record, @Param("example") TestCaseExample example);

    int updateByExampleWithBLOBs(@Param("record") TestCaseDO record, @Param("example") TestCaseExample example);

    int updateByExample(@Param("record") TestCaseDO record, @Param("example") TestCaseExample example);

    int updateByPrimaryKeySelective(TestCaseDO record);

    int updateByPrimaryKeyWithBLOBs(TestCaseDO record);

    int updateByPrimaryKey(TestCaseDO record);

    int logicalDeleteByExample(@Param("example") TestCaseExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}