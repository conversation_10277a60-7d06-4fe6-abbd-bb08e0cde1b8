package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CodeDO;
import ai.conrain.aigc.platform.dal.example.CodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CodeDAO {
    long countByExample(CodeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CodeDO record);

    int insertSelective(CodeDO record);

    List<CodeDO> selectByExample(CodeExample example);

    CodeDO selectByPrimaryKey(Integer id);

    CodeDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") CodeDO record, @Param("example") CodeExample example);

    int updateByExample(@Param("record") CodeDO record, @Param("example") CodeExample example);

    int updateByPrimaryKeySelective(CodeDO record);

    int updateByPrimaryKey(CodeDO record);

    int logicalDeleteByExample(@Param("example") CodeExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}