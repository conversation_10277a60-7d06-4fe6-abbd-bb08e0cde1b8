package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageCaptionUserExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageCaptionUserDAO {
    long countByExample(ImageCaptionUserExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageCaptionUserDO record);

    int insertSelective(ImageCaptionUserDO record);

    List<ImageCaptionUserDO> selectByExample(ImageCaptionUserExample example);

    ImageCaptionUserDO selectByPrimaryKey(Integer id);

    ImageCaptionUserDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageCaptionUserDO record, @Param("example") ImageCaptionUserExample example);

    int updateByExample(@Param("record") ImageCaptionUserDO record, @Param("example") ImageCaptionUserExample example);

    int updateByPrimaryKeySelective(ImageCaptionUserDO record);

    int updateByPrimaryKey(ImageCaptionUserDO record);

    int logicalDeleteByExample(@Param("example") ImageCaptionUserExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}