package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 对应数据表：stats_operation_indicators
 */
@Data
public class StatsOperationIndicatorsDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * 用户 id（渠道/销售/运营）
     */
    private Integer userId;

    /**
     * 名称（渠道/销售/运营）
     */
    private String name;

    /**
     * 客户转换量（新签 3999 以上）
     */
    private Integer customerConversionCount;

    /**
     * 客户消耗点数
     */
    private Integer customerConsumptionPoints;

    /**
     * 平均消耗点数
     */
    private String customerConsumptionPointsAvg;

    /**
     * 活跃客户率
     */
    private String customerActivityRate;

    /**
     * 客户复购率
     */
    private String customerRepurchaseRate;

    /**
     * 定制模特数量
     */
    private String customModelCustomers;

    /**
     * 定制场景数量
     */
    private String customSceneCustomers;

    /**
     * 大于 60 天未充值的客户
     */
    private Integer customerProtectionMetrics;

    /**
     * 交付服装量
     */
    private Integer deliveryClothingCount;

    /**
     * 审核服装量
     */
    private Integer approveClothingCount;

    /**
     * 审核错误率
     */
    private String approveErrorRate;

    /**
     * 服装返点率
     */
    private String garmentRebateRate;

    /**
     * 客户投诉率
     */
    private String customerComplaintRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 客户总数
     */
    private Integer customerTotalCount;

    /**
     * 视频数量 
     */
    private Integer videoCount;

    /**
     * 客均视频数量
     */
    private String videoCountAvg;

    /**
     * 上传服装总数
     */
    private Integer customerUploadMaterialCount;

    private static final long serialVersionUID = 1L;
}