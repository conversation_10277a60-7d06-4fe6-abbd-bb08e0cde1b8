package ai.conrain.aigc.platform.dal.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;

/**
 * MyBatis拦截器打印完整sql语句
 */
@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
             @Signature(type = Executor.class, method = "query",
                 args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})})
public class SqlMonitorInterceptor implements Interceptor {
    private static final DateFormat FORMATTER = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT,
        Locale.CHINA);
    /** 日志 */
    private static final Logger DIGEST_LOG = LoggerFactory.getLogger("dal-digest");

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 执行完上面的任务后，不改变原有的sql执行过程
        boolean success = true;
        String msg = "-";
        long start = System.currentTimeMillis();
        try {
            return invocation.proceed();
        } catch (Throwable e) {
            success = false;
            msg = e.getMessage();
            throw e;
        } finally {
            try {
                MappedStatement mappedStatement = (MappedStatement)invocation.getArgs()[0];
                Object parameter = null;
                if (invocation.getArgs().length > 1) {
                    parameter = invocation.getArgs()[1];
                }
                //sql语句的id，如：ai.conrain.aigc.platform.dal.dao.BackSkuDAO.selectByExample
                String sqlID = mappedStatement.getId();
                sqlID = StringUtils.replace(sqlID, "ai.conrain.aigc.platform.dal.dao.", "");

                //获取sql语句
                String sql = showSql(mappedStatement.getConfiguration(), mappedStatement.getBoundSql(parameter));

                log.info("[sqlMonitor],{},{},{},{}ms,{}", sqlID, sql, success ? "Y" : "N", System.currentTimeMillis() - start, msg != null ? msg : "-");

            } catch (Throwable e) {
                log.warn("SqlMonitorInterceptor error", e);
            }
        }
    }

    /**
     * 进行？的替换
     */
    public static String showSql(Configuration configuration, BoundSql boundSql) {
        // 获取参数
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        // sql语句中多个空格都用一个空格代替
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (CollectionUtils.isNotEmpty(parameterMappings) && parameterObject != null) {
            // 获取类型处理器注册器，类型处理器的功能是进行java类型和数据库类型的转换
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            // 如果根据parameterObject.getClass(）可以找到对应的类型，则替换
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameterValue(parameterObject)));
            } else {
                // MetaObject主要是封装了originalObject对象，提供了get和set的方法用于获取和设置originalObject的属性值,
                // 主要支持对JavaBean、Collection、Map三种类型对象的操作
                String[] sqlSlides = sql.split("\\?");
                StringBuilder ret = new StringBuilder(sqlSlides[0]);

                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (int i = 0; i < parameterMappings.size(); ++i) {

                    ParameterMapping parameterMapping = parameterMappings.get(i);

                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        ret.append(Matcher.quoteReplacement(getParameterValue(obj)));

                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        // 该分支是动态sql
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        ret.append(Matcher.quoteReplacement(getParameterValue(obj)));
                    }

                    if (i + 1 < sqlSlides.length) {
                        ret.append(sqlSlides[i + 1]);
                    }
                }

                sql = ret.toString();
            }
        }
        return sql;
    }

    /**
     * 如果参数是String，则添加单引号
     * 如果参数是日期，则转换为时间格式器并加单引号； 对参数是null和不是null的情况作了处理
     */
    private static String getParameterValue(Object obj) {
        if (null == obj) {
            return "''";
        }

        if (obj instanceof Date) {
            return "'" + FORMATTER.format((Date)obj) + "'";
        }

        if (obj instanceof String) {
            return "'" + obj + "'";
        }

        return obj.toString();
    }
}