/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.pgvector.PGvector;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 多数据源配置
 *
 * <AUTHOR>
 * @version DataSourceConfig.java, v 0.1 2023-12-01 10:00 conrain
 */
@Configuration
public class DataSourceConfig {

    /**
     * MySQL数据源配置
     */
    @Configuration
    @MapperScan(basePackages = "ai.conrain.aigc.platform.dal.dao", sqlSessionTemplateRef = "mysqlSqlSessionTemplate")
    static class MysqlDataSourceConfig {

        @Primary
        @Bean(name = "mysqlDataSource")
        @ConfigurationProperties("spring.datasource.mysql")
        public DataSource mysqlDataSource() {
            return DruidDataSourceBuilder.create().build();
        }

        @Primary
        @Bean(name = "mysqlSqlSessionFactory")
        public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("mysqlDataSource") DataSource dataSource)
                throws Exception {
            SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
            bean.setDataSource(dataSource);
            bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:sqlmap/*.xml"));
            bean.setTypeAliasesPackage("ai.conrain.aigc.platform.dal.entity");

            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            // 设置兼容性配置，解决Spring Boot 3.x的Bean定义问题
            configuration.setLazyLoadingEnabled(false);
            configuration.setAggressiveLazyLoading(false);

            // 添加SQL监控拦截器
            configuration.addInterceptor(new ai.conrain.aigc.platform.dal.interceptor.SqlMonitorInterceptor());

            bean.setConfiguration(configuration);

            return bean.getObject();
        }

        @Primary
        @Bean(name = "mysqlTransactionManager")
        public DataSourceTransactionManager mysqlTransactionManager(
                @Qualifier("mysqlDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }

        @Primary
        @Bean(name = "mysqlSqlSessionTemplate")
        public SqlSessionTemplate mysqlSqlSessionTemplate(
                @Qualifier("mysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }
    }

    /**
     * PostgreSQL数据源配置
     */
    @Configuration
    @MapperScan(basePackages = "ai.conrain.aigc.platform.dal.pgsql.dao", sqlSessionTemplateRef = "pgsqlSqlSessionTemplate")
    static class PgsqlDataSourceConfig {

        @Bean(name = "pgsqlDataSource")
        @ConfigurationProperties("spring.datasource.pgsql")
        public DataSource pgsqlDataSource() {
            return DruidDataSourceBuilder.create().build();
        }

        @Bean(name = "pgsqlSqlSessionFactory")
        public SqlSessionFactory pgsqlSqlSessionFactory(@Qualifier("pgsqlDataSource") DataSource dataSource)
                throws Exception {
            SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
            bean.setDataSource(dataSource);
            bean.setMapperLocations(
                    new PathMatchingResourcePatternResolver().getResources("classpath:sqlmap4pgsql/*.xml"));
            bean.setTypeAliasesPackage("ai.conrain.aigc.platform.dal.pgsql.entity");

            // 设置MyBatis配置
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            // 设置兼容性配置，解决Spring Boot 3.x的Bean定义问题
            configuration.setLazyLoadingEnabled(false);
            configuration.setAggressiveLazyLoading(false);

            // 添加SQL监控拦截器
            configuration.addInterceptor(new ai.conrain.aigc.platform.dal.interceptor.SqlMonitorInterceptor());

            bean.setConfiguration(configuration);

            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();

            // 注册PGvector类型的TypeHandler
            typeHandlerRegistry.register(PGvector.class, JdbcType.OTHER, VectorTypeHandler.class);

            // 注册JSONB类型的TypeHandler
            typeHandlerRegistry.register(String.class, JdbcType.OTHER, JsonbTypeHandler.class);

            return bean.getObject();
        }

        @Bean(name = "pgsqlTransactionManager")
        public DataSourceTransactionManager pgsqlTransactionManager(
                @Qualifier("pgsqlDataSource") DataSource dataSource) {
            return new DataSourceTransactionManager(dataSource);
        }

        @Bean(name = "pgsqlSqlSessionTemplate")
        public SqlSessionTemplate pgsqlSqlSessionTemplate(
                @Qualifier("pgsqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
            return new SqlSessionTemplate(sqlSessionFactory);
        }
    }
}