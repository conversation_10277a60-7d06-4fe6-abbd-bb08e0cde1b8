/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz.abtest;

import lombok.Data;

/**
 * 纯prompt创作测试用例项
 *
 * <AUTHOR>
 * @version : PromptCreativeItemDetail.java, v 0.1 2025/8/13 11:35 renxiao.wu Exp $
 */
@Data
public class PromptCreativeItemDetail implements BaseCaseItemDetail {
    private static final long serialVersionUID = -5065387986211463371L;
    /** 数据id */
    private Integer dataId;
    /** 图片路径 */
    private String imagePath;
    /** prompt */
    private String prompt;
    /** 图片url */
    private String imageUrl;
    /** 宽度 */
    private Integer width;
    /** 高度 */
    private Integer height;
}
