package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StatsClothesInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public StatsClothesInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public StatsClothesInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public StatsClothesInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public StatsClothesInfoExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNull() {
            addCriterion("stats_type is null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNotNull() {
            addCriterion("stats_type is not null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeEqualTo(String value) {
            addCriterion("stats_type =", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotEqualTo(String value) {
            addCriterion("stats_type <>", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThan(String value) {
            addCriterion("stats_type >", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stats_type >=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThan(String value) {
            addCriterion("stats_type <", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThanOrEqualTo(String value) {
            addCriterion("stats_type <=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLike(String value) {
            addCriterion("stats_type like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotLike(String value) {
            addCriterion("stats_type not like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIn(List<String> values) {
            addCriterion("stats_type in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotIn(List<String> values) {
            addCriterion("stats_type not in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeBetween(String value1, String value2) {
            addCriterion("stats_type between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotBetween(String value1, String value2) {
            addCriterion("stats_type not between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNull() {
            addCriterion("stats_date is null");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNotNull() {
            addCriterion("stats_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatsDateEqualTo(String value) {
            addCriterion("stats_date =", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotEqualTo(String value) {
            addCriterion("stats_date <>", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThan(String value) {
            addCriterion("stats_date >", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThanOrEqualTo(String value) {
            addCriterion("stats_date >=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThan(String value) {
            addCriterion("stats_date <", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThanOrEqualTo(String value) {
            addCriterion("stats_date <=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLike(String value) {
            addCriterion("stats_date like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotLike(String value) {
            addCriterion("stats_date not like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateIn(List<String> values) {
            addCriterion("stats_date in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotIn(List<String> values) {
            addCriterion("stats_date not in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateBetween(String value1, String value2) {
            addCriterion("stats_date between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotBetween(String value1, String value2) {
            addCriterion("stats_date not between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountIsNull() {
            addCriterion("vip_clothes_count is null");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountIsNotNull() {
            addCriterion("vip_clothes_count is not null");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountEqualTo(Integer value) {
            addCriterion("vip_clothes_count =", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountNotEqualTo(Integer value) {
            addCriterion("vip_clothes_count <>", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountGreaterThan(Integer value) {
            addCriterion("vip_clothes_count >", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vip_clothes_count >=", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountLessThan(Integer value) {
            addCriterion("vip_clothes_count <", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountLessThanOrEqualTo(Integer value) {
            addCriterion("vip_clothes_count <=", value, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountIn(List<Integer> values) {
            addCriterion("vip_clothes_count in", values, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountNotIn(List<Integer> values) {
            addCriterion("vip_clothes_count not in", values, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountBetween(Integer value1, Integer value2) {
            addCriterion("vip_clothes_count between", value1, value2, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andVipClothesCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vip_clothes_count not between", value1, value2, "vipClothesCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountIsNull() {
            addCriterion("auto_train_count is null");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountIsNotNull() {
            addCriterion("auto_train_count is not null");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountEqualTo(Integer value) {
            addCriterion("auto_train_count =", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountNotEqualTo(Integer value) {
            addCriterion("auto_train_count <>", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountGreaterThan(Integer value) {
            addCriterion("auto_train_count >", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("auto_train_count >=", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountLessThan(Integer value) {
            addCriterion("auto_train_count <", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountLessThanOrEqualTo(Integer value) {
            addCriterion("auto_train_count <=", value, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountIn(List<Integer> values) {
            addCriterion("auto_train_count in", values, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountNotIn(List<Integer> values) {
            addCriterion("auto_train_count not in", values, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountBetween(Integer value1, Integer value2) {
            addCriterion("auto_train_count between", value1, value2, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainCountNotBetween(Integer value1, Integer value2) {
            addCriterion("auto_train_count not between", value1, value2, "autoTrainCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountIsNull() {
            addCriterion("manual_delivery_count is null");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountIsNotNull() {
            addCriterion("manual_delivery_count is not null");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountEqualTo(Integer value) {
            addCriterion("manual_delivery_count =", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountNotEqualTo(Integer value) {
            addCriterion("manual_delivery_count <>", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountGreaterThan(Integer value) {
            addCriterion("manual_delivery_count >", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("manual_delivery_count >=", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountLessThan(Integer value) {
            addCriterion("manual_delivery_count <", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountLessThanOrEqualTo(Integer value) {
            addCriterion("manual_delivery_count <=", value, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountIn(List<Integer> values) {
            addCriterion("manual_delivery_count in", values, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountNotIn(List<Integer> values) {
            addCriterion("manual_delivery_count not in", values, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountBetween(Integer value1, Integer value2) {
            addCriterion("manual_delivery_count between", value1, value2, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andManualDeliveryCountNotBetween(Integer value1, Integer value2) {
            addCriterion("manual_delivery_count not between", value1, value2, "manualDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountIsNull() {
            addCriterion("auto_train_and_delivery_count is null");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountIsNotNull() {
            addCriterion("auto_train_and_delivery_count is not null");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountEqualTo(Integer value) {
            addCriterion("auto_train_and_delivery_count =", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountNotEqualTo(Integer value) {
            addCriterion("auto_train_and_delivery_count <>", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountGreaterThan(Integer value) {
            addCriterion("auto_train_and_delivery_count >", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("auto_train_and_delivery_count >=", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountLessThan(Integer value) {
            addCriterion("auto_train_and_delivery_count <", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountLessThanOrEqualTo(Integer value) {
            addCriterion("auto_train_and_delivery_count <=", value, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountIn(List<Integer> values) {
            addCriterion("auto_train_and_delivery_count in", values, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountNotIn(List<Integer> values) {
            addCriterion("auto_train_and_delivery_count not in", values, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountBetween(Integer value1, Integer value2) {
            addCriterion("auto_train_and_delivery_count between", value1, value2, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andAutoTrainAndDeliveryCountNotBetween(Integer value1, Integer value2) {
            addCriterion("auto_train_and_delivery_count not between", value1, value2, "autoTrainAndDeliveryCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountIsNull() {
            addCriterion("retry_matting_count is null");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountIsNotNull() {
            addCriterion("retry_matting_count is not null");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountEqualTo(Integer value) {
            addCriterion("retry_matting_count =", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountNotEqualTo(Integer value) {
            addCriterion("retry_matting_count <>", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountGreaterThan(Integer value) {
            addCriterion("retry_matting_count >", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("retry_matting_count >=", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountLessThan(Integer value) {
            addCriterion("retry_matting_count <", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountLessThanOrEqualTo(Integer value) {
            addCriterion("retry_matting_count <=", value, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountIn(List<Integer> values) {
            addCriterion("retry_matting_count in", values, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountNotIn(List<Integer> values) {
            addCriterion("retry_matting_count not in", values, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountBetween(Integer value1, Integer value2) {
            addCriterion("retry_matting_count between", value1, value2, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andRetryMattingCountNotBetween(Integer value1, Integer value2) {
            addCriterion("retry_matting_count not between", value1, value2, "retryMattingCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountIsNull() {
            addCriterion("update_prompt_count is null");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountIsNotNull() {
            addCriterion("update_prompt_count is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountEqualTo(Integer value) {
            addCriterion("update_prompt_count =", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountNotEqualTo(Integer value) {
            addCriterion("update_prompt_count <>", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountGreaterThan(Integer value) {
            addCriterion("update_prompt_count >", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_prompt_count >=", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountLessThan(Integer value) {
            addCriterion("update_prompt_count <", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountLessThanOrEqualTo(Integer value) {
            addCriterion("update_prompt_count <=", value, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountIn(List<Integer> values) {
            addCriterion("update_prompt_count in", values, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountNotIn(List<Integer> values) {
            addCriterion("update_prompt_count not in", values, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountBetween(Integer value1, Integer value2) {
            addCriterion("update_prompt_count between", value1, value2, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andUpdatePromptCountNotBetween(Integer value1, Integer value2) {
            addCriterion("update_prompt_count not between", value1, value2, "updatePromptCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountIsNull() {
            addCriterion("copy_count is null");
            return (Criteria) this;
        }

        public Criteria andCopyCountIsNotNull() {
            addCriterion("copy_count is not null");
            return (Criteria) this;
        }

        public Criteria andCopyCountEqualTo(Integer value) {
            addCriterion("copy_count =", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountNotEqualTo(Integer value) {
            addCriterion("copy_count <>", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountGreaterThan(Integer value) {
            addCriterion("copy_count >", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("copy_count >=", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountLessThan(Integer value) {
            addCriterion("copy_count <", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountLessThanOrEqualTo(Integer value) {
            addCriterion("copy_count <=", value, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountIn(List<Integer> values) {
            addCriterion("copy_count in", values, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountNotIn(List<Integer> values) {
            addCriterion("copy_count not in", values, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountBetween(Integer value1, Integer value2) {
            addCriterion("copy_count between", value1, value2, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCopyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("copy_count not between", value1, value2, "copyCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}