package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 拍摄风格配置表
 * 对应数据表：shooting_style
 */
public class ShootingStyleDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 一级分类名称
     */
    private String type1Name;

    /**
     * 二级分类名称
     */
    private String type2Name;

    /**
     * 英文名称
     */
    private String type2EnName;

    /**
     * 代表性品牌
     */
    private String representativeBrand;

    /**
     * 经典元素
     */
    private String classicElements;

    /**
     * 创建账号id
     */
    private Integer createBy;

    /**
     * 修改账号id
     */
    private Integer modifyBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 示例图片地址列表，jsonArray格式
     */
    private String examImageUrls;

    /**
     * 风格描述
     */
    private String styleDesc;

    /**
     * 模特类型，适合的男模特/女模特类型，男模标签列表，女模标签列表
     */
    private String modelTags;

    /**
     * 扩展
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType1Name() {
        return type1Name;
    }

    public void setType1Name(String type1Name) {
        this.type1Name = type1Name == null ? null : type1Name.trim();
    }

    public String getType2Name() {
        return type2Name;
    }

    public void setType2Name(String type2Name) {
        this.type2Name = type2Name == null ? null : type2Name.trim();
    }

    public String getType2EnName() {
        return type2EnName;
    }

    public void setType2EnName(String type2EnName) {
        this.type2EnName = type2EnName == null ? null : type2EnName.trim();
    }

    public String getRepresentativeBrand() {
        return representativeBrand;
    }

    public void setRepresentativeBrand(String representativeBrand) {
        this.representativeBrand = representativeBrand == null ? null : representativeBrand.trim();
    }

    public String getClassicElements() {
        return classicElements;
    }

    public void setClassicElements(String classicElements) {
        this.classicElements = classicElements == null ? null : classicElements.trim();
    }

    public Integer getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Integer createBy) {
        this.createBy = createBy;
    }

    public Integer getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(Integer modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getExamImageUrls() {
        return examImageUrls;
    }

    public void setExamImageUrls(String examImageUrls) {
        this.examImageUrls = examImageUrls == null ? null : examImageUrls.trim();
    }

    public String getStyleDesc() {
        return styleDesc;
    }

    public void setStyleDesc(String styleDesc) {
        this.styleDesc = styleDesc == null ? null : styleDesc.trim();
    }

    public String getModelTags() {
        return modelTags;
    }

    public void setModelTags(String modelTags) {
        this.modelTags = modelTags == null ? null : modelTags.trim();
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }
}