package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户收藏表
 * 对应数据表：user_favor
 */
@Data
public class UserFavorDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 收藏对象的id, model_id, element_id, taskId
     */
    private Integer itemId;

    /**
     * 类型
     */
    private String type;

    /**
     * 服装 id
     */
    private Integer modelId;

    /**
     * 服装名称
     */
    private String modelName;

    /**
     * 展示图片
     */
    private String showImage;

    /**
     * 备注
     */
    private String memo;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 收藏图片数量
     */
    private Integer imageCount;

    private static final long serialVersionUID = 1L;
}