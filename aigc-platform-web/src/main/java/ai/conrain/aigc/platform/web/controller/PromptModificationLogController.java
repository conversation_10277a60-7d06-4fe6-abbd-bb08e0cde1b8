package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.PromptModificationLogDO;
import ai.conrain.aigc.platform.service.model.vo.PromptModificationLogVO;
import ai.conrain.aigc.platform.service.model.query.PromptModificationLogQuery;
import ai.conrain.aigc.platform.service.component.PromptModificationLogService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * PromptModificationLog控制器
 *
 * <AUTHOR>
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/promptModificationLog")
public class PromptModificationLogController {

	/** promptModificationLogService */
	@Autowired
	private PromptModificationLogService promptModificationLogService;
	
	@GetMapping("/getById/{id}")
	public Result<PromptModificationLogVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(promptModificationLogService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody PromptModificationLogVO promptModificationLog){
		try {
			PromptModificationLogVO data = promptModificationLogService.insert(promptModificationLog);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加修改记录失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建修改记录失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		promptModificationLogService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody PromptModificationLogVO promptModificationLog){
		promptModificationLogService.updateByIdSelective(promptModificationLog);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<PromptModificationLogVO>> queryPromptModificationLogList(@Valid @RequestBody PromptModificationLogQuery query){
		return Result.success(promptModificationLogService.queryPromptModificationLogList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<PromptModificationLogVO>> getPromptModificationLogByPage(@Valid @RequestBody PromptModificationLogQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(promptModificationLogService.queryPromptModificationLogByPage(query));
	}
}
