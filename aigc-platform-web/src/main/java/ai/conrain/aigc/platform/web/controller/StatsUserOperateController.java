package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO;
import ai.conrain.aigc.platform.service.model.vo.StatsUserOperateVO;
import ai.conrain.aigc.platform.service.model.query.StatsUserOperateQuery;
import ai.conrain.aigc.platform.service.component.StatsUserOperateService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsUserOperate控制器
 *
 * <AUTHOR>
 * @version StatsUserOperateService.java v 0.1 2025-04-25 02:47:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsUserOperate")
public class StatsUserOperateController {

    /** statsUserOperateService */
    @Autowired
    private StatsUserOperateService statsUserOperateService;

    @GetMapping("/getById/{id}")
    public Result<StatsUserOperateVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(statsUserOperateService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody StatsUserOperateVO statsUserOperate) {
        try {
            StatsUserOperateVO data = statsUserOperateService.insert(statsUserOperate);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加用户操作统计表（出图、下载）失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户操作统计表（出图、下载）失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        statsUserOperateService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody StatsUserOperateVO statsUserOperate) {
        statsUserOperateService.updateByIdSelective(statsUserOperate);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<StatsUserOperateVO>> queryStatsUserOperateList(@Valid @RequestBody StatsUserOperateQuery query) {
        return Result.success(statsUserOperateService.queryStatsUserOperateList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<StatsUserOperateVO>> getStatsUserOperateByPage(@Valid @RequestBody StatsUserOperateQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(statsUserOperateService.queryStatsUserOperateByPage(query));
    }


    /**
     * 获取用户创作数据
     *
     * @param userId   用户id
     * @param isParent 是否是主账户
     * @return Map<String, Integer>
     */
    @GetMapping("/getUserCreativeData")
    public Result<Map<String, Object>> getUserCreativeData( @RequestParam("userId") Integer userId,  @RequestParam("isParent") Boolean isParent) {
        if (userId == null || isParent == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数不能为空！");
        }
        return Result.success(statsUserOperateService.getUserCreativeData(userId,isParent));
    }




}
