/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.DataStatsService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据统计控制器
 *
 * <AUTHOR>
 * @version : DataStatsController.java, v 0.1 2024/9/14 13:05 renxiao.wu Exp $
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data")
public class DataStatsController {
    @Autowired
    private DataStatsService dataStatsService;

    @PostMapping("/delivery")
    public Result<?> statsDelivery(@JsonArg String startDate, @JsonArg String endDate) {
        return Result.success(dataStatsService.statsDelivery(startDate, endDate));
    }
}
