package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户操作记录表
 * 对应数据表：user_op_log
 */
@Data
public class UserOpLogDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 主账号用户id
     */
    private Integer masterUserId;

    /**
     * 操作人用户id
     */
    private Integer operatorUserId;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 操作行为，新增、修改、废弃
     */
    private String opType;

    /**
     * 商品、订单、赊账、收入、支出、还款、客户、子账号
     */
    private String opBizType;

    /**
     * 操作目标id
     */
    private String opBizNo;

    /**
     * 备注
     */
    private String memo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 变更前明细
     */
    private String detailBefore;

    /**
     * 变更后明细
     */
    private String detailAfter;

    private static final long serialVersionUID = 1L;
}