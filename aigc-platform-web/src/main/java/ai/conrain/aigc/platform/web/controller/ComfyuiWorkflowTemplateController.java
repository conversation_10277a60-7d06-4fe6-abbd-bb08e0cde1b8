package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ComfyuiWorkflowTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ComfyuiWorkflowTemplate控制器
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateService.java v 0.1 2025-06-11 03:56:03
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/comfyuiWorkflowTemplate")
public class ComfyuiWorkflowTemplateController {

    /** comfyuiWorkflowTemplateService */
    @Autowired
    private ComfyuiWorkflowTemplateService comfyuiWorkflowTemplateService;

    @GetMapping("/getById/{id}")
    public Result<ComfyuiWorkflowTemplateVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(comfyuiWorkflowTemplateService.selectById(id));
    }

    @PostMapping("/createActiveVersion")
    public Result<ComfyuiWorkflowTemplateVO> createActiveVersion(
        @Valid @RequestBody ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate) {
        comfyuiWorkflowTemplate.setCreateBy(OperationContextHolder.getOperatorUserId());
        comfyuiWorkflowTemplate.setModifyBy(OperationContextHolder.getOperatorUserId());
        return Result.success(comfyuiWorkflowTemplateService.createActiveVersion(comfyuiWorkflowTemplate));
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        comfyuiWorkflowTemplateService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate) {
        comfyuiWorkflowTemplateService.updateByIdSelective(comfyuiWorkflowTemplate);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<ComfyuiWorkflowTemplateVO>> queryComfyuiWorkflowTemplateList(
        @Valid @RequestBody ComfyuiWorkflowTemplateQuery query) {
        return Result.success(comfyuiWorkflowTemplateService.queryComfyuiWorkflowTemplateList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<ComfyuiWorkflowTemplateVO>> getComfyuiWorkflowTemplateByPage(
        @Valid @RequestBody ComfyuiWorkflowTemplateQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(comfyuiWorkflowTemplateService.queryComfyuiWorkflowTemplateByPage(query));
    }

    @PostMapping("/activeTemplate")
    public Result<?> activeTemplate(@JsonArg @NotNull Integer id) {
        comfyuiWorkflowTemplateService.activeVersion(id);
        return Result.success();
    }

    @PostMapping("/rollbackById")
    public Result<?> rollbackById(@JsonArg @NotNull Integer id) {
        comfyuiWorkflowTemplateService.rollbackById(id);
        return Result.success();
    }
}
