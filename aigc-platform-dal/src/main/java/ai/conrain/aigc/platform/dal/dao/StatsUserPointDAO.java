package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsUserPointDO;
import ai.conrain.aigc.platform.dal.example.StatsUserPointExample;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsUserPointDAO {
    long countByExample(StatsUserPointExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsUserPointDO record);

    int insertSelective(StatsUserPointDO record);

    List<StatsUserPointDO> selectByExample(StatsUserPointExample example);

    List<StatsUserPointDO> selectByExampleWithBlob(StatsUserPointExample example);

    StatsUserPointDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsUserPointDO record, @Param("example") StatsUserPointExample example);

    int updateByExample(@Param("record") StatsUserPointDO record, @Param("example") StatsUserPointExample example);

    int updateByPrimaryKeySelective(StatsUserPointDO record);

    int updateByPrimaryKey(StatsUserPointDO record);

    /**
     * 按条件批量插入或更新统计数据
     */
    int batchInsertOrUpdateSelective(List<StatsUserPointDO> statsList);
    
    /**
     * 批量插入或更新统计数据
     */
    int batchInsertOrUpdate(List<StatsUserPointDO> statsList);

    /**
     * 按用户ID和统计类型查询统计数据
     */
    List<StatsUserPointDO> selectByUserIdAndType(@Param("userId") Integer userId, @Param("statsType") String statsType);

    /**
     * 按统计类型和日期范围查询
     */
    List<StatsUserPointDO> selectByTypeAndDateRange(@Param("statsType") String statsType,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);
    
    /**
     * 按周查询统计数据 - 格式：yyyy-MM-dd 表示周一的日期
     */
    List<StatsUserPointDO> selectByWeek(@Param("weekStartDate") String weekStartDate);

    /**
     * 按月查询统计数据 - 格式：yyyy-MM-dd 表示月初第一天的日期
     */
    List<StatsUserPointDO> selectByMonth(@Param("monthStartDate") String monthStartDate);
    
    /**
     * 删除指定日期和类型的统计数据（用于重新生成）
     */
    int deleteByDateAndType(@Param("statsDate") String statsDate, @Param("statsType") String statsType);
    
    /**
     * 查询日期范围内有消费记录的用户
     */
    List<Integer> findUsersWithConsumptionOrRecharge(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询用户在指定日期的消费点数
     */
    StatsUserPointDO getUserPointConsumption(@Param("userId") Integer userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询所有用户在指定日期的消费点数
     */
    StatsUserPointDO getAllUserPointConsumption(@Param("userId") Integer userId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取指定周的所有日统计数据
     */
    List<StatsUserPointDO> selectDailyStatsByWeek(@Param("weekStartDate") String weekStartDate);
    
    /**
     * 获取指定月的所有日统计数据
     */
    List<StatsUserPointDO> selectDailyStatsByMonth(@Param("monthStartDate") String monthStartDate);
    
    /**
     * 获取指定周的所有用户汇总日统计数据（userId=0）
     */
    List<StatsUserPointDO> selectDailyTotalStatsByWeek(@Param("weekStartDate") String weekStartDate);
    
    /**
     * 获取指定月的所有用户汇总日统计数据（userId=0）
     */
    List<StatsUserPointDO> selectDailyTotalStatsByMonth(@Param("monthStartDate") String monthStartDate);


    List<StatsUserPointDO> getUsersPointConsumptionBatch(List<Integer> userIds, Date startDate, Date endDate);

    /**
     * 获取指定日期的Total统计数据（前一天的Total统计）
     * @param statsDate 统计日期，格式：yyyy-MM-dd
     * @return Total统计数据列表
     */
    List<StatsUserPointDO> selectTotalStatsByDate(@Param("statsDate") String statsDate);
    
    /**
     * 获取指定日期的Daily统计数据（当天的Daily统计）
     * @param statsDate 统计日期，格式：yyyy-MM-dd
     * @return Daily统计数据列表
     */
    List<StatsUserPointDO> selectDailyStatsByDate(@Param("statsDate") String statsDate);
    
    /**
     * 获取指定日期的Total汇总统计数据（前一天的Total汇总统计，userId=0）
     * @param statsDate 统计日期，格式：yyyy-MM-dd
     * @return Total汇总统计数据列表
     */
    List<StatsUserPointDO> selectTotalTotalStatsByDate(@Param("statsDate") String statsDate);
    
    /**
     * 获取指定日期的Daily汇总统计数据（当天的Daily汇总统计，userId=0）
     * @param statsDate 统计日期，格式：yyyy-MM-dd
     * @return Daily汇总统计数据列表
     */
    List<StatsUserPointDO> selectDailyTotalStatsByDate(@Param("statsDate") String statsDate);
}