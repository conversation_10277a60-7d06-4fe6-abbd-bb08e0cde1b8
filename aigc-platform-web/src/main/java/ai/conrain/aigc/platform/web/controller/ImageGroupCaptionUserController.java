package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ImageGroupCaptionUser控制器
 *
 * <AUTHOR>
 * @version ImageGroupCaptionUserService.java v 0.1 2025-07-30 08:19:30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGroupCaptionUser")
public class ImageGroupCaptionUserController {

	/** imageGroupCaptionUserService */
	@Autowired
	private ImageGroupCaptionUserService imageGroupCaptionUserService;
	
	@GetMapping("/getById/{id}")
	public Result<ImageGroupCaptionUserVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageGroupCaptionUserService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageGroupCaptionUserVO imageGroupCaptionUser){
		return Result.success(imageGroupCaptionUserService.insert(imageGroupCaptionUser).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageGroupCaptionUserService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageGroupCaptionUserVO imageGroupCaptionUser){
		imageGroupCaptionUserService.updateByIdSelective(imageGroupCaptionUser);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<ImageGroupCaptionUserVO>> queryImageGroupCaptionUserList(@Valid @RequestBody ImageGroupCaptionUserQuery query){
		return Result.success(imageGroupCaptionUserService.queryImageGroupCaptionUserList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageGroupCaptionUserVO>> getImageGroupCaptionUserByPage(@Valid @RequestBody ImageGroupCaptionUserQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageGroupCaptionUserService.queryImageGroupCaptionUserByPage(query));
	}
}
