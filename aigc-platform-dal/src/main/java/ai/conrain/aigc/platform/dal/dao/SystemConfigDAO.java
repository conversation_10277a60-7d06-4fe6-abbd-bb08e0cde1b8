package ai.conrain.aigc.platform.dal.dao;

import java.util.List;

import ai.conrain.aigc.platform.dal.entity.SystemConfigDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface SystemConfigDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(SystemConfigDO record);

    SystemConfigDO selectByPrimaryKey(Integer id);

    List<SystemConfigDO> selectAll();

    int updateByPrimaryKey(SystemConfigDO record);
}