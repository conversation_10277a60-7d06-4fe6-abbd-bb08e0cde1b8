package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ComfyuiWorkflowTemplateExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public ComfyuiWorkflowTemplateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public ComfyuiWorkflowTemplateExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public ComfyuiWorkflowTemplateExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public ComfyuiWorkflowTemplateExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria)this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria)this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria)this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria)this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyIsNull() {
            addCriterion("template_key is null");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyIsNotNull() {
            addCriterion("template_key is not null");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyEqualTo(String value) {
            addCriterion("template_key =", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyNotEqualTo(String value) {
            addCriterion("template_key <>", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyGreaterThan(String value) {
            addCriterion("template_key >", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyGreaterThanOrEqualTo(String value) {
            addCriterion("template_key >=", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyLessThan(String value) {
            addCriterion("template_key <", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyLessThanOrEqualTo(String value) {
            addCriterion("template_key <=", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyLike(String value) {
            addCriterion("template_key like", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyNotLike(String value) {
            addCriterion("template_key not like", value, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyIn(List<String> values) {
            addCriterion("template_key in", values, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyNotIn(List<String> values) {
            addCriterion("template_key not in", values, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyBetween(String value1, String value2) {
            addCriterion("template_key between", value1, value2, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateKeyNotBetween(String value1, String value2) {
            addCriterion("template_key not between", value1, value2, "templateKey");
            return (Criteria)this;
        }

        public Criteria andTemplateDescIsNull() {
            addCriterion("template_desc is null");
            return (Criteria)this;
        }

        public Criteria andTemplateDescIsNotNull() {
            addCriterion("template_desc is not null");
            return (Criteria)this;
        }

        public Criteria andTemplateDescEqualTo(String value) {
            addCriterion("template_desc =", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescNotEqualTo(String value) {
            addCriterion("template_desc <>", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescGreaterThan(String value) {
            addCriterion("template_desc >", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescGreaterThanOrEqualTo(String value) {
            addCriterion("template_desc >=", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescLessThan(String value) {
            addCriterion("template_desc <", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescLessThanOrEqualTo(String value) {
            addCriterion("template_desc <=", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescLike(String value) {
            addCriterion("template_desc like", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescNotLike(String value) {
            addCriterion("template_desc not like", value, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescIn(List<String> values) {
            addCriterion("template_desc in", values, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescNotIn(List<String> values) {
            addCriterion("template_desc not in", values, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescBetween(String value1, String value2) {
            addCriterion("template_desc between", value1, value2, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andTemplateDescNotBetween(String value1, String value2) {
            addCriterion("template_desc not between", value1, value2, "templateDesc");
            return (Criteria)this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria)this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria)this;
        }

        public Criteria andVersionEqualTo(String value) {
            addCriterion("version =", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionNotEqualTo(String value) {
            addCriterion("version <>", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionGreaterThan(String value) {
            addCriterion("version >", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(String value) {
            addCriterion("version >=", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionLessThan(String value) {
            addCriterion("version <", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionLessThanOrEqualTo(String value) {
            addCriterion("version <=", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionLike(String value) {
            addCriterion("version like", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionNotLike(String value) {
            addCriterion("version not like", value, "version");
            return (Criteria)this;
        }

        public Criteria andVersionIn(List<String> values) {
            addCriterion("version in", values, "version");
            return (Criteria)this;
        }

        public Criteria andVersionNotIn(List<String> values) {
            addCriterion("version not in", values, "version");
            return (Criteria)this;
        }

        public Criteria andVersionBetween(String value1, String value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria)this;
        }

        public Criteria andVersionNotBetween(String value1, String value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria)this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria)this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria)this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria)this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria)this;
        }

        public Criteria andCreateByEqualTo(Integer value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByNotEqualTo(Integer value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByGreaterThan(Integer value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByLessThan(Integer value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Integer value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByIn(List<Integer> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByNotIn(List<Integer> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByBetween(Integer value1, Integer value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria)this;
        }

        public Criteria andCreateByNotBetween(Integer value1, Integer value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria)this;
        }

        public Criteria andModifyByIsNull() {
            addCriterion("modify_by is null");
            return (Criteria)this;
        }

        public Criteria andModifyByIsNotNull() {
            addCriterion("modify_by is not null");
            return (Criteria)this;
        }

        public Criteria andModifyByEqualTo(Integer value) {
            addCriterion("modify_by =", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByNotEqualTo(Integer value) {
            addCriterion("modify_by <>", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByGreaterThan(Integer value) {
            addCriterion("modify_by >", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_by >=", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByLessThan(Integer value) {
            addCriterion("modify_by <", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByLessThanOrEqualTo(Integer value) {
            addCriterion("modify_by <=", value, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByIn(List<Integer> values) {
            addCriterion("modify_by in", values, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByNotIn(List<Integer> values) {
            addCriterion("modify_by not in", values, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByBetween(Integer value1, Integer value2) {
            addCriterion("modify_by between", value1, value2, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andModifyByNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_by not between", value1, value2, "modifyBy");
            return (Criteria)this;
        }

        public Criteria andInTestVersion() {
            addCriterion("concat(template_key,'-',version) in (select concat(template_key,'-',test_version) "
                         + "from comfyui_workflow_template_active_version where test_version is not null)");
            return (Criteria)this;
        }

        public Criteria andNotDeleted() {
            addCriterion("deleted = 0");
            return (Criteria)this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}