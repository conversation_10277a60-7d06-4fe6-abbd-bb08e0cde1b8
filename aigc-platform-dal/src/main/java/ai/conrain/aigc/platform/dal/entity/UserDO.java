package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户信息表
 * 对应数据表：user
 */
@Data
public class UserDO implements Serializable {
    /**
     * 用户id
     */
    private Integer id;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 登录id
     */
    private String loginId;

    /**
     * 登录密码
     */
    private String pswd;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 自定义角色code（渠道商场景）
     */
    private String customRole;

    /**
     * 用户类型，MASTER、SUB
     */
    private String userType;

    /**
     * 主账号id
     */
    private Integer masterId;

    /**
     * 状态，ENABLED、DISABLED
     */
    private String status;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 注册来源
     */
    private String registerFrom;

    /**
     * 备注
     */
    private String memo;

    //公司名（当前用户创建时关联的企业名）,废弃字段，由于这里可能变更，因此使用视图字段relatedCorpName，从organization表取对应名称
    @Deprecated
    private String corpName;

    //公司组织id（当前用户创建时关联的企业组织id）
    private Integer corpOrgId;

    //用户审核信息
    private String userReviewInfo;

    /**
     * 登录失败次数
     */
    private Integer loginFailCount;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    //最近来访日期，yyyyMMDD
    private String lastVisitDate;

    //---------------user view columns----------------------
    private BigDecimal musePoint;
    private BigDecimal totalTopupAmount;
    private Integer visitIn15days;
    private Integer relatedDistributorMasterUserId;
    private Integer promptEngineerUserId;

    private String relatedCorpName;
    private Integer relatedDistributorCorpId;
    private String relatedDistributorCorpName;
    private Integer relatedSalesUserId;
    private String relatedSalesUserName;
    private String contractDate;
    //---------------user view columns----------------------

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}