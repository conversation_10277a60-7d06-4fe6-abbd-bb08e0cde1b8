package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.StatsSaleIndicatorsService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.StatsSaleIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsSaleIndicatorsVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StatsSaleIndicators控制器
 *
 * <AUTHOR>
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsSaleIndicators")
public class StatsSaleIndicatorsController {

	/** statsSaleIndicatorsService */
	@Autowired
	private StatsSaleIndicatorsService statsSaleIndicatorsService;
	
	@GetMapping("/getById/{id}")
	public Result<StatsSaleIndicatorsVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsSaleIndicatorsService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody StatsSaleIndicatorsVO statsSaleIndicators){
		try {
			StatsSaleIndicatorsVO data = statsSaleIndicatorsService.insert(statsSaleIndicators);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加销售指标失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建销售指标失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsSaleIndicatorsService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsSaleIndicatorsVO statsSaleIndicators){
		statsSaleIndicatorsService.updateByIdSelective(statsSaleIndicators);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<StatsSaleIndicatorsVO>> queryStatsSaleIndicatorsList(@Valid @RequestBody StatsSaleIndicatorsQuery query){
		return Result.success(statsSaleIndicatorsService.queryStatsSaleIndicatorsList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<StatsSaleIndicatorsVO>> getStatsSaleIndicatorsByPage(@Valid @RequestBody StatsSaleIndicatorsQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsSaleIndicatorsService.queryStatsSaleIndicatorsByPage(query));
	}
}
