package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;

/**
 * 图像标注表用户标注数据，存储用户和大模型打标的数据 Service定义
 *
 * <AUTHOR>
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
public interface ImageCaptionUserService {
	
	/**
	 * 查询图像标注表用户标注数据，存储用户和大模型打标的数据对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageCaptionUserVO selectById(Integer id);

	/**
	 * 删除图像标注表用户标注数据，存储用户和大模型打标的数据对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像标注表用户标注数据，存储用户和大模型打标的数据对象
	 * @param imageCaptionUser 对象参数
	 * @return 返回结果
	 */
	ImageCaptionUserVO insert(ImageCaptionUserVO imageCaptionUser);

    /**
     * 添加图像标注表用户标注数据，存储用户和大模型打标的数据对象
     *
     * @param imageCaptionUser
     * @return
     */
    ImageCaptionUserVO save(ImageCaptionUserVO imageCaptionUser);

	/**
	 * 修改图像标注表用户标注数据，存储用户和大模型打标的数据对象
	 * @param imageCaptionUser 对象参数
	 */
	void updateByIdSelective(ImageCaptionUserVO imageCaptionUser);

	/**
	 * 带条件批量查询图像标注表用户标注数据，存储用户和大模型打标的数据列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageCaptionUserVO> queryImageCaptionUserList(ImageCaptionUserQuery query);

	/**
	 * 带条件查询图像标注表用户标注数据，存储用户和大模型打标的数据数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageCaptionUserCount(ImageCaptionUserQuery query);

	/**
	 * 带条件分页查询图像标注表用户标注数据，存储用户和大模型打标的数据
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageCaptionUserVO> queryImageCaptionUserByPage(ImageCaptionUserQuery query);

    /**
     * 从上传的image中保存打标数据
     *
     * @param imageVO
     * @param data
     */
    void saveFromImage(ImageVO imageVO, JSONObject data);
}