package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.model.query.ImageRecordAggVO;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * ImageRecordVO
 */
@Data
public class ImageRecordVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 图像ID，自增主键 */
	private Integer id;

    /** 场景的图片 id */
    private Integer sceneId;

	/** 图像类型，用于区分服装图、风格示意图 */
	private String type;

	/** 图像原始URL地址 */
	private String url;

    private String pairUrl;

	/** 展示图像URL地址 */
	private String showImgUrl;

	/** 图片路径 */
	private String imagePath;

	/** 图片内容哈希 */
	private String imageHash;

	/** 图像元数据，JSON格式 */
	private JSONObject metadata;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 图片组标题结果数组，来自 image_group_caption.result */
	private JSONArray result;

    /** 聚合查询结果 */
    private List<ImageRecordAggVO> agg;

    /**
     * 服装类型
     */
    private String clothTypeDesc;

    /**
     * 流派
     */
    private String intendedUse;
}
