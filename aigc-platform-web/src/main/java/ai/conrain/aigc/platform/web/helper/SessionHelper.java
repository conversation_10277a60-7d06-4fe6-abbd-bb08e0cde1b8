/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.helper;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.common.OperationSession;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * session帮助类
 *
 * <AUTHOR>
 * @version : SessionHelper.java, v 0.1 2023/9/5 09:45 renxiao.wu Exp $
 */
@Slf4j
@Component
public class SessionHelper {
    /** 缓存服务 */
    @Autowired
    private TairService tairService;

    /**
     * 创建session id并写入cookie
     *
     * @param response http响应
     * @return 操作上下文会话
     */
    public OperationSession createSessionAndSetCookie(HttpServletResponse response) {

        OperationSession session = new OperationSession();
        String sessionId = genSessionId();
        session.setSessionId(sessionId);

        //更新到上下文，由拦截器最后统一刷新到tair
        OperationContextHolder.getContext().setOperationSession(session);

        int sessionExpireTime = getSessionExpireTime();

        log.info("生成sessionId:{},超时时间={}s", sessionId, sessionExpireTime);

        //将用户信息以sessionId的key存放到tair中
        tairService.setObject(sessionId, session, sessionExpireTime);

        //回写cookie
        Cookie cookie = CookieUtils.buildCookieWithExpire(CommonConstants.SESSION_KEY_ID, sessionId, sessionExpireTime);

        response.addCookie(cookie);

        return session;
    }

    /**
     * 刷新session过期时间
     *
     * @param request  http请求
     * @param response http响应
     */
    public void refreshSessionExpire(HttpServletRequest request, HttpServletResponse response) {

        //2.刷新tair中的session会话

        //2.1.如果未登录，则不刷新登录相关的信息
        OperationSession operationSession = OperationContextHolder.getContext().getOperationSession();
        if (null == operationSession) {
            return;
        }

        //2.2.如果是session上下文出现了变更，则将session内容更新到tair中
        String sessionId = fetchSessionId();

        //有登录态的用户操作，刷新cookie中sessionId、login标的过期时间
        if (operationSession.getLoginUser() != null) {
            CookieUtils.resetLoginedExpire(CommonConstants.SESSION_KEY_ID, sessionId, getSessionExpireTime(), request,
                response);
        }

        if (operationSession.isChanged()) {
            tairService.setObject(sessionId, operationSession, getSessionExpireTime());
        } else {
            //2.3.如果没有其他变更，那么刷新tair中session的过期时间
            if (operationSession.getLoginUser() != null) {
                tairService.expire(sessionId, getSessionExpireTime());
            }
        }
    }

    /**
     * 获取sessionId
     *
     * @return sessionId
     */
    public String fetchSessionId() {

        String sessionId = OperationContextHolder.getContext().getSessionId();
        if (StringUtils.isBlank(sessionId)) {
            throw new RuntimeException("线程session id为空");
        }

        return sessionId;
    }

    /**
     * 清理登录用户，状态和信息
     */
    public void clear(HttpServletResponse response) {
        //清理登录态
        OperationContextHolder.getContext().getOperationSession().setLoginUser(null);

        String sessionId = fetchSessionId();
        //cookie删除sessionId
        CookieUtils.deleteCookie(response, CommonConstants.SESSION_KEY_ID, sessionId);
        //清除tair内容
        tairService.clear(sessionId);
    }

    /**
     * 初始化session
     */
    public void saveLoginSession(UserVO userVO) {
        //生成sessionId并返回给客户端/前端
        String sessionId = fetchSessionId();

        //初始化session
        OperationSession session = OperationContextHolder.getContext().getOperationSession();
        if (session == null) {
            session = new OperationSession();
        }

        session.setLoginUser(userVO);

        //更新到上下文，由拦截器最后统一刷新到tair
        OperationContextHolder.getContext().setOperationSession(session);

        //将用户信息以sessionId的key存放到tair中
        tairService.setObject(sessionId, session, getSessionExpireTime());
    }

    /**
     * 生成sessionId
     *
     * @return sessionId
     */
    private String genSessionId() {
        return CommonConstants.SESSION_PREFIX + CommonUtil.uuid();
    }

    /**
     * 获取session过期时间
     *
     * @return session过期时间
     */
    private static int getSessionExpireTime() {
        boolean fromMiniApp = OperationContextHolder.getContext().isRequestFromMiniApp();
        return fromMiniApp ? CommonConstants.MINI_APP_SESSION_EXPIRE_TIME : CommonConstants.PC_SESSION_EXPIRE_TIME;
    }

}
