package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StatsSaleIndicatorsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public StatsSaleIndicatorsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public StatsSaleIndicatorsExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public StatsSaleIndicatorsExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public StatsSaleIndicatorsExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNull() {
            addCriterion("stats_type is null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNotNull() {
            addCriterion("stats_type is not null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeEqualTo(String value) {
            addCriterion("stats_type =", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotEqualTo(String value) {
            addCriterion("stats_type <>", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThan(String value) {
            addCriterion("stats_type >", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stats_type >=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThan(String value) {
            addCriterion("stats_type <", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThanOrEqualTo(String value) {
            addCriterion("stats_type <=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLike(String value) {
            addCriterion("stats_type like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotLike(String value) {
            addCriterion("stats_type not like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIn(List<String> values) {
            addCriterion("stats_type in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotIn(List<String> values) {
            addCriterion("stats_type not in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeBetween(String value1, String value2) {
            addCriterion("stats_type between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotBetween(String value1, String value2) {
            addCriterion("stats_type not between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNull() {
            addCriterion("stats_date is null");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNotNull() {
            addCriterion("stats_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatsDateEqualTo(String value) {
            addCriterion("stats_date =", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotEqualTo(String value) {
            addCriterion("stats_date <>", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThan(String value) {
            addCriterion("stats_date >", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThanOrEqualTo(String value) {
            addCriterion("stats_date >=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThan(String value) {
            addCriterion("stats_date <", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThanOrEqualTo(String value) {
            addCriterion("stats_date <=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLike(String value) {
            addCriterion("stats_date like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotLike(String value) {
            addCriterion("stats_date not like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateIn(List<String> values) {
            addCriterion("stats_date in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotIn(List<String> values) {
            addCriterion("stats_date not in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateBetween(String value1, String value2) {
            addCriterion("stats_date between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotBetween(String value1, String value2) {
            addCriterion("stats_date not between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "name");
            return (Criteria) this;
        }


        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }


        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountIsNull() {
            addCriterion("clothes_exp_count is null");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountIsNotNull() {
            addCriterion("clothes_exp_count is not null");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountEqualTo(Integer value) {
            addCriterion("clothes_exp_count =", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountNotEqualTo(Integer value) {
            addCriterion("clothes_exp_count <>", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountGreaterThan(Integer value) {
            addCriterion("clothes_exp_count >", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("clothes_exp_count >=", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountLessThan(Integer value) {
            addCriterion("clothes_exp_count <", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountLessThanOrEqualTo(Integer value) {
            addCriterion("clothes_exp_count <=", value, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountIn(List<Integer> values) {
            addCriterion("clothes_exp_count in", values, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountNotIn(List<Integer> values) {
            addCriterion("clothes_exp_count not in", values, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountBetween(Integer value1, Integer value2) {
            addCriterion("clothes_exp_count between", value1, value2, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andClothesExpCountNotBetween(Integer value1, Integer value2) {
            addCriterion("clothes_exp_count not between", value1, value2, "clothesExpCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIsNull() {
            addCriterion("customer_conversion_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIsNotNull() {
            addCriterion("customer_conversion_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountEqualTo(Integer value) {
            addCriterion("customer_conversion_count =", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotEqualTo(Integer value) {
            addCriterion("customer_conversion_count <>", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountGreaterThan(Integer value) {
            addCriterion("customer_conversion_count >", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_conversion_count >=", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountLessThan(Integer value) {
            addCriterion("customer_conversion_count <", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_conversion_count <=", value, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountIn(List<Integer> values) {
            addCriterion("customer_conversion_count in", values, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotIn(List<Integer> values) {
            addCriterion("customer_conversion_count not in", values, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_conversion_count between", value1, value2, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConversionCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_conversion_count not between", value1, value2, "customerConversionCount");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIsNull() {
            addCriterion("customer_consumption_points is null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIsNotNull() {
            addCriterion("customer_consumption_points is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsEqualTo(Integer value) {
            addCriterion("customer_consumption_points =", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotEqualTo(Integer value) {
            addCriterion("customer_consumption_points <>", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsGreaterThan(Integer value) {
            addCriterion("customer_consumption_points >", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_consumption_points >=", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsLessThan(Integer value) {
            addCriterion("customer_consumption_points <", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsLessThanOrEqualTo(Integer value) {
            addCriterion("customer_consumption_points <=", value, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsIn(List<Integer> values) {
            addCriterion("customer_consumption_points in", values, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotIn(List<Integer> values) {
            addCriterion("customer_consumption_points not in", values, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsBetween(Integer value1, Integer value2) {
            addCriterion("customer_consumption_points between", value1, value2, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerConsumptionPointsNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_consumption_points not between", value1, value2, "customerConsumptionPoints");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIsNull() {
            addCriterion("customer_activity_rate is null");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIsNotNull() {
            addCriterion("customer_activity_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateEqualTo(String value) {
            addCriterion("customer_activity_rate =", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotEqualTo(String value) {
            addCriterion("customer_activity_rate <>", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateGreaterThan(String value) {
            addCriterion("customer_activity_rate >", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateGreaterThanOrEqualTo(String value) {
            addCriterion("customer_activity_rate >=", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLessThan(String value) {
            addCriterion("customer_activity_rate <", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLessThanOrEqualTo(String value) {
            addCriterion("customer_activity_rate <=", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateLike(String value) {
            addCriterion("customer_activity_rate like", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotLike(String value) {
            addCriterion("customer_activity_rate not like", value, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateIn(List<String> values) {
            addCriterion("customer_activity_rate in", values, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotIn(List<String> values) {
            addCriterion("customer_activity_rate not in", values, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateBetween(String value1, String value2) {
            addCriterion("customer_activity_rate between", value1, value2, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerActivityRateNotBetween(String value1, String value2) {
            addCriterion("customer_activity_rate not between", value1, value2, "customerActivityRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIsNull() {
            addCriterion("customer_repurchase_rate is null");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIsNotNull() {
            addCriterion("customer_repurchase_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateEqualTo(String value) {
            addCriterion("customer_repurchase_rate =", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotEqualTo(String value) {
            addCriterion("customer_repurchase_rate <>", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateGreaterThan(String value) {
            addCriterion("customer_repurchase_rate >", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateGreaterThanOrEqualTo(String value) {
            addCriterion("customer_repurchase_rate >=", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLessThan(String value) {
            addCriterion("customer_repurchase_rate <", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLessThanOrEqualTo(String value) {
            addCriterion("customer_repurchase_rate <=", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateLike(String value) {
            addCriterion("customer_repurchase_rate like", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotLike(String value) {
            addCriterion("customer_repurchase_rate not like", value, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateIn(List<String> values) {
            addCriterion("customer_repurchase_rate in", values, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotIn(List<String> values) {
            addCriterion("customer_repurchase_rate not in", values, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateBetween(String value1, String value2) {
            addCriterion("customer_repurchase_rate between", value1, value2, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRepurchaseRateNotBetween(String value1, String value2) {
            addCriterion("customer_repurchase_rate not between", value1, value2, "customerRepurchaseRate");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIsNull() {
            addCriterion("custom_model_customers is null");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIsNotNull() {
            addCriterion("custom_model_customers is not null");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersEqualTo(String value) {
            addCriterion("custom_model_customers =", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotEqualTo(Integer value) {
            addCriterion("custom_model_customers <>", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersGreaterThan(Integer value) {
            addCriterion("custom_model_customers >", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersGreaterThanOrEqualTo(Integer value) {
            addCriterion("custom_model_customers >=", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersLessThan(Integer value) {
            addCriterion("custom_model_customers <", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersLessThanOrEqualTo(Integer value) {
            addCriterion("custom_model_customers <=", value, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersIn(List<Integer> values) {
            addCriterion("custom_model_customers in", values, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotIn(List<Integer> values) {
            addCriterion("custom_model_customers not in", values, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersBetween(Integer value1, Integer value2) {
            addCriterion("custom_model_customers between", value1, value2, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomModelCustomersNotBetween(Integer value1, Integer value2) {
            addCriterion("custom_model_customers not between", value1, value2, "customModelCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIsNull() {
            addCriterion("custom_scene_customers is null");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIsNotNull() {
            addCriterion("custom_scene_customers is not null");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersEqualTo(String value) {
            addCriterion("custom_scene_customers =", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotEqualTo(Integer value) {
            addCriterion("custom_scene_customers <>", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersGreaterThan(Integer value) {
            addCriterion("custom_scene_customers >", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersGreaterThanOrEqualTo(Integer value) {
            addCriterion("custom_scene_customers >=", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersLessThan(Integer value) {
            addCriterion("custom_scene_customers <", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersLessThanOrEqualTo(Integer value) {
            addCriterion("custom_scene_customers <=", value, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersIn(List<Integer> values) {
            addCriterion("custom_scene_customers in", values, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotIn(List<Integer> values) {
            addCriterion("custom_scene_customers not in", values, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersBetween(Integer value1, Integer value2) {
            addCriterion("custom_scene_customers between", value1, value2, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomSceneCustomersNotBetween(Integer value1, Integer value2) {
            addCriterion("custom_scene_customers not between", value1, value2, "customSceneCustomers");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIsNull() {
            addCriterion("customer_protection_metrics is null");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIsNotNull() {
            addCriterion("customer_protection_metrics is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsEqualTo(Integer value) {
            addCriterion("customer_protection_metrics =", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotEqualTo(Integer value) {
            addCriterion("customer_protection_metrics <>", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsGreaterThan(Integer value) {
            addCriterion("customer_protection_metrics >", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_protection_metrics >=", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsLessThan(Integer value) {
            addCriterion("customer_protection_metrics <", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsLessThanOrEqualTo(Integer value) {
            addCriterion("customer_protection_metrics <=", value, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsIn(List<Integer> values) {
            addCriterion("customer_protection_metrics in", values, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotIn(List<Integer> values) {
            addCriterion("customer_protection_metrics not in", values, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsBetween(Integer value1, Integer value2) {
            addCriterion("customer_protection_metrics between", value1, value2, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCustomerProtectionMetricsNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_protection_metrics not between", value1, value2, "customerProtectionMetrics");
            return (Criteria) this;
        }

        public Criteria andCreateCountIsNull() {
            addCriterion("create_count is null");
            return (Criteria) this;
        }

        public Criteria andCreateCountIsNotNull() {
            addCriterion("create_count is not null");
            return (Criteria) this;
        }

        public Criteria andCreateCountEqualTo(Integer value) {
            addCriterion("create_count =", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountNotEqualTo(Integer value) {
            addCriterion("create_count <>", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountGreaterThan(Integer value) {
            addCriterion("create_count >", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_count >=", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountLessThan(Integer value) {
            addCriterion("create_count <", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountLessThanOrEqualTo(Integer value) {
            addCriterion("create_count <=", value, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountIn(List<Integer> values) {
            addCriterion("create_count in", values, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountNotIn(List<Integer> values) {
            addCriterion("create_count not in", values, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountBetween(Integer value1, Integer value2) {
            addCriterion("create_count between", value1, value2, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateCountNotBetween(Integer value1, Integer value2) {
            addCriterion("create_count not between", value1, value2, "createCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}