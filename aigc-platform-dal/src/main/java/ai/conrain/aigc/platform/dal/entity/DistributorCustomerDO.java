package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 渠道商客户信息表
 * 对应数据表：distributor_customer
 */
@Data
public class DistributorCustomerDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 客户主账号id
     */
    private Integer customerMasterUserId;

    /**
     * 渠道商主体id
     */
    private Integer distributorCorpOrgId;

    /**
     * 渠道商主体名称（快照）,废弃字段，改为使用 relatedDistributorCorpName
     */
    @Deprecated
    private String distributorCorpName;

    /**
     * 渠道商主账号id
     */
    private Integer distributorMasterUserId;

    /**
     * 渠道商运营人员id
     */
    private Integer distributorOperatorUserId;

    /**
     * 渠道商销售人员id
     */
    private Integer distributorSalesUserId;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    //view columns
    private String customerMasterNick;
    private String customerMasterCorpName;
    private BigDecimal customerMusePoint;
    private String relatedDistributorCorpName;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}