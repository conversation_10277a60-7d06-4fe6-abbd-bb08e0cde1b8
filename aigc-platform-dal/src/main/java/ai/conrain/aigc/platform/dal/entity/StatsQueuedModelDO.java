/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 统计队列中的模型DO
 *
 * <AUTHOR>
 * @version : StatsQueuedModelDO.java, v 0.1 2025/5/13 00:46 renxiao.wu Exp $
 */
@Data
public class StatsQueuedModelDO implements Serializable {
    private static final long serialVersionUID = -8292901424550307949L;
    /** 预处理等待队列长度 */
    private Integer queuedPreProcess;
    /** 训练等待队列长度 */
    private Integer queuedTrain;
}
