package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * comfyui任务表
 * 对应数据表：comfyui_task
 */
@Data
public class ComfyuiTaskDO implements Serializable {
    /**
     * 模型训练任务id
     */
    private Integer id;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 操作人账号id
     */
    private Integer operatorId;

    /**
     * 任务类型, cutout/mark-label/lora
     */
    private String taskType;

    /**
     * 任务状态, QUEUED/RUNNING/COMPLETED/FAILED/UNKNOWN/NONE
     */
    private String taskStatus;

    /**
     * 自定义请求参数
     */
    private String reqParams;

    /**
     * ComfyUI返回的唯一标识
     */
    private String promptId;

    /**
     * 扩展
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * comfyui api请求报文
     */
    private String comfyuiRequest;

    /**
     * 结果详情
     */
    private String retDetail;

    private static final long serialVersionUID = 1L;
}