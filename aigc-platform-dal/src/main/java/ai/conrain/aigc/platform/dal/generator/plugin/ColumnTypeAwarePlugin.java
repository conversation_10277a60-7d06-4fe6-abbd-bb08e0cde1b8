package ai.conrain.aigc.platform.dal.generator.plugin;

import org.mybatis.generator.api.IntrospectedColumn;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.FullyQualifiedJavaType;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class ColumnTypeAwarePlugin extends PluginAdapter {

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public void initialized(IntrospectedTable introspectedTable) {
        String tableName = introspectedTable.getFullyQualifiedTable().getIntrospectedTableName();
        try (Connection conn = getConnection()) {
            DatabaseMetaData meta = conn.getMetaData();
            ResultSet columns = meta.getColumns(null, null, tableName, null);
            while (columns.next()) {
                String columnName = columns.getString("COLUMN_NAME");
                String typeName = columns.getString("TYPE_NAME");

                for (IntrospectedColumn col : introspectedTable.getAllColumns()) {
                    if (col.getActualColumnName().equalsIgnoreCase(columnName)) {
                        if ("vector".equalsIgnoreCase(typeName)) {
                            col.setFullyQualifiedJavaType(new FullyQualifiedJavaType("com.pgvector.PGvector"));
                        } else if ("jsonb".equalsIgnoreCase(typeName)) {
                            col.setFullyQualifiedJavaType(new FullyQualifiedJavaType("java.lang.String"));
                        } else if ("json".equalsIgnoreCase(typeName)) {
                            col.setFullyQualifiedJavaType(new FullyQualifiedJavaType("java.lang.String"));
                        }
                    }
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private Connection getConnection() throws SQLException {
        String url = properties.getProperty("jdbc.url");
        String user = properties.getProperty("jdbc.username");
        String password = properties.getProperty("jdbc.password");
        return DriverManager.getConnection(url, user, password);
    }
}