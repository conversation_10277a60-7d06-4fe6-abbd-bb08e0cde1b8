package ai.conrain.aigc.platform.dal.generator.plugin.pgsql;

import ai.conrain.aigc.platform.dal.generator.plugin.mysql.GenBizJavaFilesPlugin;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * mybatis generator插件，自动生成vo/query/converter/service/service impl/controller
 */
public class GenBizJavaFilesPlugin4PgSQL extends GenBizJavaFilesPlugin {
    public Properties getProjectProperties() {
        InputStream inputStream = GenBizJavaFilesPlugin.class.getClassLoader().getResourceAsStream(
                "generator/pgsql/generator-config.properties");
        try {
            properties.load(inputStream);
            return properties;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
