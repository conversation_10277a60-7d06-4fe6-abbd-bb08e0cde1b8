package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class StyleImageRecommendation implements Serializable {
    //流派 @see ClothShootGenreEnum
    private ClothShootGenreEnum clothShootGenreEnum;
    private Integer imageId;
    private String imageUrl;
    private String imageShowUrl;

    private Integer imageCaptionId;

    //背景组，一次推荐结果中，背景聚类序列
    private String bgClusterId;
    //款式相似度
    private double styleSimilarity;
    //匹配得分
    private double matchScore;
}
