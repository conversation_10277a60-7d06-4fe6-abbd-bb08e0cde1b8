package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsWarningInfoDO;
import ai.conrain.aigc.platform.dal.example.StatsWarningInfoExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsWarningInfoDAO {
    long countByExample(StatsWarningInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsWarningInfoDO record);

    int insertSelective(StatsWarningInfoDO record);

    List<StatsWarningInfoDO> selectByExampleWithBLOBs(StatsWarningInfoExample example);

    List<StatsWarningInfoDO> selectByExample(StatsWarningInfoExample example);

    StatsWarningInfoDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsWarningInfoDO record, @Param("example") StatsWarningInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsWarningInfoDO record, @Param("example") StatsWarningInfoExample example);

    int updateByExample(@Param("record") StatsWarningInfoDO record, @Param("example") StatsWarningInfoExample example);

    int updateByPrimaryKeySelective(StatsWarningInfoDO record);

    int updateByPrimaryKeyWithBLOBs(StatsWarningInfoDO record);

    int updateByPrimaryKey(StatsWarningInfoDO record);

    /**
     * 批量插入或更新
     *
     * @param statsWarningInfoDOList 数据集合
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<StatsWarningInfoDO> statsWarningInfoDOList);

    /**
     * 查询指定时间段内指定统计类型的数据
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param statsType 统计类型
     * @return 统计数据列表
     */
    List<StatsWarningInfoDO> selectStatsInfoByDateAndPeriod(@Param("startDate") String startDate,
                                                            @Param("endDate") String endDate,
                                                            @Param("statsType") String statsType);
}