package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * UserProfile控制器
 *
 * <AUTHOR>
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/userProfile")
public class UserProfileController {

    /** userProfileService */
    @Autowired
    private UserProfileService userProfileService;

    @GetMapping("/getById/{id}")
    public Result<UserProfileVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(userProfileService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Boolean> create(@Valid @RequestBody UserProfileVO userProfile) {
        try {
            UserProfileVO data = userProfileService.insertOrUpdate(userProfile);
            if (data != null) {
                return Result.success(true);
            }
        } catch (Exception e) {
            log.error("添加用户属性失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户属性失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        userProfileService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody UserProfileVO userProfile) {
        userProfileService.updateByIdSelective(userProfile);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<UserProfileVO>> queryUserProfileList(@Valid @RequestBody UserProfileQuery query) {
        return Result.success(userProfileService.queryUserProfileList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<UserProfileVO>> getUserProfileByPage(@Valid @RequestBody UserProfileQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(userProfileService.queryUserProfileByPage(query));
    }

    @PostMapping("/queryUserProfileByKey")
    public Result<UserProfileVO> queryUserProfileByKey(@JsonArg @NotBlank String key) {
        return Result.success(
            userProfileService.selectByUidAndProfileKey(OperationContextHolder.getOperatorUserId(), key));
    }

    @PostMapping("/setUserProfileByKey")
    public Result<?> setUserProfileByKey(@JsonArg @NotBlank String key, @JsonArg @NotBlank String value) {

        UserProfileVO exist = userProfileService.selectByUidAndProfileKey(OperationContextHolder.getOperatorUserId(),
            key);

        if (exist == null) {
            UserProfileVO userProfile = new UserProfileVO();
            userProfile.setUid(OperationContextHolder.getOperatorUserId());
            userProfile.setProfileKey(key);
            userProfile.setProfileVal(value);
            userProfileService.insert(userProfile);

        } else {
            UserProfileVO target = new UserProfileVO();
            target.setId(exist.getId());
            target.setProfileVal(value);

            userProfileService.updateByIdSelective(target);
        }

        return Result.success();
    }

    @PostMapping("/queryByUidAndKey")
    public Result<UserProfileVO> queryByUidAndKey(@JsonArg @NotNull Integer userId, @JsonArg @NotBlank String key) {
        return Result.success(userProfileService.selectByUidAndProfileKey(userId, key));
    }

    @PostMapping("/refreshByUidAndKey")
    public Result<?> refreshByUidAndKey(@JsonArg @NotNull Integer userId, @JsonArg @NotBlank String key,
                                        @JsonArg @NotBlank String value) {

        UserProfileVO exist = userProfileService.selectByUidAndProfileKey(userId, key);

        if (exist == null) {
            UserProfileVO userProfile = new UserProfileVO();
            userProfile.setUid(userId);
            userProfile.setProfileKey(key);
            userProfile.setProfileVal(value);
            userProfileService.insert(userProfile);

        } else {
            UserProfileVO target = new UserProfileVO();
            target.setId(exist.getId());
            target.setProfileVal(value);

            userProfileService.updateByIdSelective(target);
        }

        return Result.success();
    }
}
