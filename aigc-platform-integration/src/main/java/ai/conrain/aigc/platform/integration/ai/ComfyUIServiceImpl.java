/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai;

import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.PromptResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.BytePoolUtils;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.integration.utils.ServiceUtils;
import ai.conrain.aigc.platform.integration.wechat.model.ImageQualityVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 调用comfyUI服务实现
 *
 * <AUTHOR>
 * @version : ComfyUIServiceImpl.java, v 0.1 2024/5/9 22:02 renxiao.wu Exp $
 */
@Slf4j
@Component
public class ComfyUIServiceImpl implements ComfyUIService {

    @Autowired
    @Qualifier("restTemplate")
    private RestTemplate restTemplate;

    @Autowired
    @Qualifier("normalRestTemplate")
    private RestTemplate normalRestTemplate;

    @Autowired
    @Qualifier("longRestTemplate")
    private RestTemplate longRestTemplate;

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate extraLongRestTemplate;

    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate superLongRestTemplate;

    @Value("${comfyui.service.url}")
    private String comfyuiUrl;

    @Value("${comfyui.file.4090url}")
    private String comfyuiFileUrl;

    @Value("${comfyui.file.a800url}")
    private String comfyuiFileUrlA800;

    @Value("${comfyui.output.path}")
    private String comfyuiOutputPath;

    @Value("${comfyui.input.path}")
    private String comfyuiInputPath;

    @Autowired
    private OssService ossService;

    @Override
    public PromptResult prompt(String request, String serverUrl) {
        HttpHeaders headers = buildTraceHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(request, headers);
        PromptResult result = new PromptResult();

        String url = serverUrl + "/prompt";

        try {
            ResponseEntity<String> httpRes = null;
            try {
                httpRes = longRestTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            } catch (Exception e) {
                log.error("调用comfyui的prompt接口异常,request={}", request);
                log.error("调用comfyui的prompt接口异常", e);
                result.setError("SYS_ERROR");
                return result;
            }

            if (httpRes.getStatusCode() != HttpStatus.OK) {
                log.error("调用comfyui的prompt接口异常,code={}", httpRes.getStatusCode());
                log.error("调用comfyui的prompt接口异常,request={}", request);
                return result;
            }

            String content = httpRes.getBody();
            if (StringUtils.isBlank(content)) {
                log.warn("调用comfyui的prompt接口异常,返回结果无法解析json,body={}", content);
                return result;
            }
            log.info("调用comfyui的prompt接口,返回结果={}", content);

            JSONObject res = JSONObject.parseObject(content);

            JSONObject nodeErrors = res.getJSONObject("node_errors");
            if (nodeErrors != null && !nodeErrors.isEmpty()) {
                result.setError(nodeErrors.toJSONString());
                log.error("调用comfyui的prompt接口失败,request={}", request);
                log.warn("调用comfyui的prompt接口失败，error={}", result.getError());
            } else {
                result.setSuccess(true);
                result.setPromptId(res.getString("prompt_id"));
                result.setNumber(res.getInteger("number"));
            }

            return result;

        } catch (Throwable t) {
            log.error("调用comfyui的prompt接口异常", t);

            result.setSuccess(false);
            return result;
        }
    }

    @Override
    public Map<String, QueueResult> queryStatusByQueue(List<String> promptIds, String url) {
        HttpEntity<String> entity = new HttpEntity<>(null, buildTraceHeaders());

        ResponseEntity<String> httpRes = restTemplate.exchange(url + "/queue", HttpMethod.GET, entity, String.class);

        //初始化结果对象
        Map<String, QueueResult> result = new HashMap<>();

        promptIds.forEach(promptId -> result.put(promptId, new QueueResult()));

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用comfyui的queue接口异常,code={}", httpRes.getStatusCode());
            return result;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.warn("调用comfyui的queue接口异常,返回结果无法解析json,body={}", content);
            return result;
        }

        result.forEach((promptId, queueResult) -> {
            queueResult.setCode(QueueCodeEnum.NONE);
        });

        JSONObject res = JSONObject.parseObject(content);

        //空队列情况：{"queue_running": [], "queue_pending": []}
        //没有完成的，调用接口时，返回空
        JSONArray running = res.getJSONArray("queue_running");
        JSONArray pending = res.getJSONArray("queue_pending");

        //在queue_running中存在(running的只会有1个)，则返回RUNNING
        if (!running.isEmpty()) {
            result.forEach((promptId, queueResult) -> {
                if (((JSONArray)running.get(0)).contains(promptId)) {
                    queueResult.setCode(QueueCodeEnum.RUNNING);
                }
            });
        }

        //在queue_pending中存在，则返回QUEUED，同时返回队列的进度
        if (!pending.isEmpty()) {
            for (int i = 0; i < pending.size(); i++) {
                JSONArray array = (JSONArray)pending.get(i);

                int finalI = i;
                result.forEach((promptId, queueResult) -> {
                    if (array.contains(promptId)) {
                        queueResult.setCode(QueueCodeEnum.QUEUED);
                        queueResult.setQueueSize(finalI + 1);
                    }
                });
            }
        }

        log.info("调用comfyui的queue接口,批量结果解析后={}", result);

        return result;
    }

    @Override
    public QueueResult testByQueue(String cfgUrl, String port) {
        HttpEntity<String> entity = new HttpEntity<>(null, buildTraceHeaders());

        ResponseEntity<String> httpRes = restTemplate.exchange(ServiceUtils.transServicePort(cfgUrl, port) + "/queue",
            HttpMethod.GET, entity, String.class);

        QueueResult result = new QueueResult();
        result.setQueueSize(0);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用comfyui的queue接口异常,code={}", httpRes.getStatusCode());
            return result;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.warn("调用comfyui的queue接口异常,返回结果无法解析json,body={}", content);
            return result;
        }

        JSONObject res = JSONObject.parseObject(content);

        //空队列情况：{"queue_running": [], "queue_pending": []}
        //没有完成的，调用接口时，返回空
        JSONArray running = res.getJSONArray("queue_running");
        JSONArray pending = res.getJSONArray("queue_pending");

        //在queue_running中存在(running的只会有1个)，则返回RUNNING
        if (!running.isEmpty()) {
            result.setCode(QueueCodeEnum.QUEUED);
            result.setQueueSize(result.getQueueSize() + 1);
        }

        //在queue_pending中存在，则返回QUEUED，同时返回队列的进度
        if (!pending.isEmpty()) {
            result.setCode(QueueCodeEnum.QUEUED);
            result.setQueueSize(result.getQueueSize() + pending.size());
        }

        if (result.getQueueSize() <= 0) {
            result.setCode(QueueCodeEnum.NONE);
        }

        log.info("调用comfyui的queue接口,结果解析后={}", result);

        return result;
    }

    @Override
    public QueueResult queryStatusByHistory(String promptId, String url) {
        QueueResult result = new QueueResult();

        HttpEntity<String> entity = new HttpEntity<>(null, buildTraceHeaders());
        ResponseEntity<String> httpRes = restTemplate.exchange(url + "/history/" + promptId, HttpMethod.GET, entity,
            String.class);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用comfyui的history接口异常,code={},res={}", httpRes.getStatusCode(), httpRes);
            return new QueueResult();
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.warn("调用comfyui的history接口异常,返回结果无法解析json,body={}", content);
            return result;
        }

        JSONObject res = JSONObject.parseObject(content);

        JSONObject nodeErrors = res.getJSONObject("node_errors");
        if (nodeErrors != null && !nodeErrors.isEmpty()) {
            result.setCode(QueueCodeEnum.UNKNOWN);
            log.error("调用comfyui的history接口失败，error={}", nodeErrors);
            return result;
        }

        if (res.isEmpty()) {
            log.warn("调用comfyui的history接口，返回空结果，说明可能未执行");
            return new QueueResult(QueueCodeEnum.NONE);
        }

        //没有完成的，调用接口时，返回空
        JSONObject status = res.getJSONObject("status");
        QueueResult statusResult = processStatus(status, result);
        if (statusResult != null) {
            log.info("从root下解析到status结果");
            return statusResult;
        }

        if (res.getJSONObject(promptId) != null) {
            status = res.getJSONObject(promptId).getJSONObject("status");
            log.info("从promptId的json下解析到status");
            statusResult = processStatus(status, result);
            if (statusResult != null) {
                return statusResult;
            }
        }

        log.error("未知的返回结果,status={},res={}", status, res);
        result.setCode(QueueCodeEnum.UNKNOWN);
        return result;
    }

    @Override
    public boolean clearHistory(String url) {
        try {
            HttpHeaders headers = buildTraceHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> request = new HashMap<>();
            request.put("clear", true);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

            ResponseEntity<String> httpRes = restTemplate.exchange(url + "/history", HttpMethod.POST, entity,
                String.class);

            if (httpRes.getStatusCode() != HttpStatus.OK) {
                log.error("调用comfyui的清空history接口异常,url={},code={}", url, httpRes.getStatusCode());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("调用comfyui的清空history接口异常,url=" + url, e);
            return false;
        }
    }

    @Override
    public Integer queryOutputImageCnt(String path, String fileNamePrefix, String url) {
        return this.queryImageCnt(comfyuiOutputPath + path, fileNamePrefix, url);
    }

    @Override
    public Integer queryImageCnt(String fullPath, String fileNamePrefix, String url) {
        String serverUrl = url + "/count/images?path=" + fullPath;

        if (StringUtils.isNotBlank(fileNamePrefix)) {
            serverUrl += "&prefix=" + fileNamePrefix;
        }

        HttpEntity<String> entity = new HttpEntity<>(null, buildTraceHeaders());
        ResponseEntity<String> httpRes = restTemplate.exchange(serverUrl, HttpMethod.GET, entity, String.class);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用图片数量查询接口异常,code={}", httpRes.getStatusCode());
            return null;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.error("调用图片数量查询接口失败,返回的结果为空");
            return null;
        }
        log.info("调用图片数量查询接口成功，结果={}", content);

        try {
            return JSONObject.parseObject(content).getInteger("count");
        } catch (Exception e) {
            log.error("调用图片数量查询接口失败,解析结果失败,content={}", content, e);
            try {
                return Integer.parseInt(StringUtils.strip(content, "\""));
            } catch (Exception t) {
                log.error("调用图片数量查询接口失败,parseInt解析结果失败,content={}", content, t);
                return null;
            }
        }
    }

    @Override
    public byte[] batchFetchImageFile(String path, String fileNamePrefix, String fileUrl) {
        String fullPath = comfyuiOutputPath + path;

        // 构建下载图片的URL
        String url = fileUrl + "/download/output?path=" + fullPath;
        // 若文件前缀不为空，则添加前缀参数
        if (StringUtils.isNoneBlank(fileNamePrefix)) {
            url = url + "&prefix=" + fileNamePrefix;
        }

        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            ResponseExtractor<Void> responseExtractor = response -> {
                // Stream the response to a file
                byte[] buffer = null;
                try (InputStream in = response.getBody();) {
                    int bytesRead;
                    buffer = BytePoolUtils.borrow();
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                } catch (Exception e) {
                    log.error("读取图片字节流异常" + path, e);
                    throw new RuntimeException(e);
                } finally {
                    BytePoolUtils.returnArray(buffer);
                }
                return null;
            };

            longRestTemplate.execute(url, HttpMethod.GET, buildTraceRequestCallback(), responseExtractor);
            byte[] bytes = out.toByteArray();

            log.info("批量下载文件成功:{},size={}", fullPath, ArrayUtils.getLength(bytes));

            return bytes;
        } catch (Exception e) {
            log.error("批量下载文件异常,path=" + fullPath, e);
            throw e;
        }
    }

    @Override
    public ByteArrayOutputStream downloadFile(String fullPath, String serverUrl) {
        String url = serverUrl + "/download/file?path=" + fullPath;
        ByteArrayOutputStream result = null;
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            ResponseExtractor<Void> responseExtractor = response -> {
                // Stream the response to a file
                try (InputStream in = response.getBody();) {
                    int bytesRead;
                    byte[] buffer = new byte[4096];
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
                return null;
            };

            extraLongRestTemplate.execute(url, HttpMethod.GET, buildTraceRequestCallback(), responseExtractor);

            result = out;

            log.info("下载文件成功:{}", fullPath);
        } catch (Exception e) {
            log.error("下载文件异常,path=" + fullPath, e);
            return null;
        }

        return result;
    }

    @Override
    public Map<String, byte[]> downloadImage(String path, String fileNamePrefix, String fileServerUrl) {
        return downloadFile(path, fileNamePrefix, fileServerUrl + "/download/image");
    }

    @Override
    public Map<String, byte[]> downloadVideo(String path, String fileNamePrefix, String fileServerUrl) {
        return downloadFile(path, fileNamePrefix, fileServerUrl + "/download/video");
    }

    @Override
    public boolean removeImage(String path, String fileNamePrefix, String fileServerUrl) {
        String fullPath = comfyuiOutputPath + path;
        return this.removeImageOrTxtFile(fullPath, fileNamePrefix, fileServerUrl);
    }

    @Override
    public boolean removeImageOrTxtFile(String dir, String fileNamePrefix, String serverUrl) {
        if (StringUtils.isBlank(dir) || StringUtils.isBlank(fileNamePrefix)) {
            throw new RuntimeException("dir和fileNamePrefix不能为空");
        }

        String url = serverUrl + "/remove/image?path=" + dir;

        if (StringUtils.isNotBlank(fileNamePrefix)) {
            url += "&prefix=" + fileNamePrefix;
        }

        HttpEntity<String> entity = new HttpEntity<>(null, buildTraceHeaders());
        ResponseEntity<String> httpRes = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用图片删除接口异常,code={}", httpRes.getStatusCode());
            return false;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.error("调用图片删除接口失败,返回的结果为空");
            return false;
        }
        log.info("调用图片删除接口成功，结果={}", content);
        if (IntegrationUtils.isValidJsonObject(content)) {
            JSONObject json = JSONObject.parseObject(content);
            //这里增加=0的处理，是为了解决异步和同步请求同时调度到的问题
            return json.getInteger("remove") >= 0;

            //["Path is invalid or empty",400]
        } else {
            log.warn("调用图片删除接口返回不是json，可能是重复删除请求，忽略，content={}", content);
            return true;
        }
    }

    @Override
    public void uploadInputImage(String fileName, InputStream inputStream, String serverUrl) throws IOException {
        log.info("上传文件,fileName={}", fileName);

        // 防止输入文件路径包含comfyuiInputPath
        if (StringUtils.startsWith(fileName, comfyuiInputPath)) {
            fileName = fileName.substring(comfyuiInputPath.length());
        }

        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        byte[] byteArray = buffer.toByteArray();

        HttpHeaders headers = buildTraceHeaders();
        headers.set("Content-Type", "application/octet-stream");
        headers.set("XXX-IMAGE-FILE-NAME", URLEncoder.encode(fileName, "UTF-8"));

        ResponseEntity<String> res = extraLongRestTemplate.exchange(serverUrl + "/image/input", HttpMethod.POST,
            new org.springframework.http.HttpEntity<>(byteArray, headers), String.class);

        if (res.getStatusCode() != HttpStatus.OK) {
            log.warn("调用comfyui接口上传文件异常,code={},res={}", res.getStatusCode(), res);
            throw new RuntimeException("调用comfyui接口上传文件异常");
        }

        log.info("上传文件成功,fileName={},code={}", fileName, res.getStatusCode());
    }

    @Override
    public String fetchFileContent(String directory, String fileNamePrefix, String serverUrl) {
        return fetchFileContent(directory, fileNamePrefix, null, serverUrl);
    }

    @Override
    public String fetchFileContent(String directory, String fileNamePrefix, String fileNameSuffix, String serverUrl) {
        String apiUrl = serverUrl + "/file/readContent?path=" + directory + "&fileNamePrefix=" + fileNamePrefix;
        if (StringUtils.isNotBlank(fileNameSuffix)) {
            apiUrl += "&fileNameSuffix=" + fileNameSuffix;
        }

        try {
            ResponseEntity<Map> response = restTemplate.getForEntity(apiUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody.containsKey("content")) {
                    return (String)responseBody.get("content");
                }
            }

            log.info("Error: Unable to fetch file content, Status code: " + response.getStatusCode());

        } catch (Throwable e) {
            log.error("fetchFileContent error", e);
        }
        return null;
    }

    @Override
    public boolean fileSync(String path, String targetUrl, String originUrl) {
        Map<String, Object> request = new HashMap<>();
        request.put("filePath", path);
        request.put("remoteAddr", originUrl);

        return callOneWay(targetUrl + "/file/sync", request, null);
    }

    @Override
    public boolean fileSyncByOss(String path, String ossUrl, String serverUrl, String md5) {
        Map<String, Object> request = new HashMap<>();
        request.put("ossUrl", ossUrl);
        request.put("storePath", path);
        if (StringUtils.isNotBlank(md5)) {
            request.put("md5", md5);
        }

        return callOneWay(serverUrl + "/file/sync/oss", request, null);
    }

    @Override
    public boolean fileCopy(String fromFilePath, String toFilePath, String fileServerUrl) {
        Map<String, Object> request = new HashMap<>();
        request.put("fromFilePath", fromFilePath);
        request.put("toFilePath", toFilePath);

        return callOneWay(fileServerUrl + "/file/copy", request, null);
    }

    @Override
    public boolean remoteFileSync(String originalServer, String originalPath, String targetServer, String targetPath) {
        // 校验参数
        if (StringUtils.isBlank(targetServer) || StringUtils.isBlank(targetPath) || StringUtils.isBlank(originalServer)
            || StringUtils.isBlank(originalPath)) {
            return false;
        }

        // 拼装请求参数
        Map<String, Object> request = new HashMap<>();
        request.put("originalServer", originalServer);
        request.put("originalPath", originalPath);
        request.put("targetServer", targetServer);
        request.put("targetPath", targetPath);

        // 执行远程文件同步
        return callOneWay(targetServer + "/file/remote/sync", request, null);
    }

    @Override
    public boolean remoteFileSync(String thirdPartFile, String targetServer, String targetPath) {
        // 校验参数
        if (StringUtils.isBlank(targetServer) || StringUtils.isBlank(targetPath) || StringUtils.isBlank(targetServer)) {
            return false;
        }

        // 拼装请求参数
        Map<String, Object> request = new HashMap<>();
        request.put("thirdPartFile", thirdPartFile);
        request.put("targetServer", targetServer);
        request.put("targetPath", targetPath);

        // 执行远程文件同步
        return callOneWay(targetServer + "/file/remote/sync", request, null);
    }

    @Override
    public boolean folderSync(String path, String targetUrl, String originUrl, String md5) {
        Map<String, Object> request = new HashMap<>();
        request.put("folderPath", path);
        request.put("remoteAddr", originUrl);
        if (StringUtils.isNotBlank(md5)) {
            request.put("folderMd5", md5);
        }

        return callOneWay(targetUrl + "/folder/sync", request, null);
    }

    @Override
    public boolean folderCopy(String path, String newPath, String fileServerUrl) {
        Map<String, Object> request = new HashMap<>();
        request.put("folderPath", path);
        request.put("newPath", newPath);

        return callOneWay(fileServerUrl + "/folder/copy", request, longRestTemplate);
    }

    @Override
    public boolean checkFileExists(String filePath, String serverUrl) {

        try {
            String encodedPath = URLEncoder.encode(filePath, StandardCharsets.UTF_8.toString());
            String apiUrl = serverUrl + "/file/exists?path=" + encodedPath;

            ResponseEntity<Map> response = restTemplate.getForEntity(apiUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Boolean exists = (Boolean)response.getBody().get("exists");
                return exists != null && exists;
            } else {
                log.error("Error: Unable to check file existence, Status code: " + response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("checkFileExists error", e);
            return false;
        }
    }

    @Override
    public boolean checkFolderExists(String path, String serverUrl) {
        try {
            String encodedPath = URLEncoder.encode(path, StandardCharsets.UTF_8.toString());
            String apiUrl = serverUrl + "/folder/exists?path=" + encodedPath;
            ResponseEntity<Map> response = restTemplate.getForEntity(apiUrl, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Boolean exists = (Boolean)response.getBody().get("exists");
                return exists != null && exists;
            } else {
                log.error("Error: Unable to check file existence, Status code: " + response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("checkFileExists error", e);
            return false;
        }
    }

    @Override
    public List<FileVO> viewFiles(String directory, String[] fileTypes, String serverUrl) {
        if (StringUtils.isBlank(directory) || ArrayUtils.isEmpty(fileTypes)) {
            throw new IllegalArgumentException("directory/fileTypes不可为空");
        }

        String apiUrl = serverUrl + "/viewFiles";

        // Set headers
        HttpHeaders headers = buildTraceHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Create JSON payload
        Map<String, Object> requestPayload = new HashMap<>();
        requestPayload.put("directory", directory);
        requestPayload.put("file_types", fileTypes);

        // Create HttpEntity
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);

        // Send POST request
        try {
            ResponseEntity<String> response = superLongRestTemplate.exchange(apiUrl, HttpMethod.POST, requestEntity,
                String.class);

            List<FileVO> list = Lists.newArrayList();
            if (response.getStatusCode() == HttpStatus.OK && IntegrationUtils.isValidJsonArray(response.getBody())) {
                JSONArray body = JSON.parseArray(response.getBody());
                if (body != null) {
                    for (int i = 0; i < body.size(); ++i) {
                        JSONObject f = body.getJSONObject(i);

                        FileVO file = new FileVO();
                        file.setFileName(f.getString("filename"));
                        file.setType(f.getString("type"));
                        file.setFileDir(f.getString("folderName"));
                        if (f.containsKey("textContent")) {
                            file.setTextContent(f.getString("textContent"));
                        }
                        if (f.containsKey("imgByteArrayStream") && StringUtils.isNotBlank(
                            f.getString("imgByteArrayStream"))) {
                            // 解码Base64字符串为字节数组
                            byte[] decodedBytes = Base64.getDecoder().decode(f.getString("imgByteArrayStream"));
                            // 将字节数组转换为InputStream
                            InputStream inputStream = new ByteArrayInputStream(decodedBytes);
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                            String fileName = sdf.format(new Date()) + "/view/" + RandomStringUtils.randomAlphabetic(3)
                                              + file.getFileName();
                            String url = ossService.upload(fileName, inputStream);
                            log.info("upload to oss:{},{}", file.getFileName(), url);

                            file.setImgUrl(url);
                        }

                        list.add(file);
                    }
                }
            } else {
                log.error("遍历目录失败:{},{}", directory, fileTypes);
            }

            return list;
        } catch (Throwable t) {
            log.error("viewFiles遍历目录失败:" + directory + "," + fileTypes, t);
            return null;
        }
    }

    @Override
    public String calcDirMd5(String directory, String serverUrl) {
        try {
            String apiUrl = serverUrl + "/calculateDirectoryMd5";

            // Set headers
            HttpHeaders headers = buildTraceHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Create JSON payload
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("directory", directory);

            // Create HttpEntity
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);

            // Send POST request
            ResponseEntity<String> response = longRestTemplate.exchange(apiUrl, HttpMethod.POST, requestEntity,
                String.class);

            if (response.getStatusCode() == HttpStatus.OK && StringUtils.isNotBlank(response.getBody())) {
                JSONObject body = JSONObject.parseObject(response.getBody());
                if (body != null && body.containsKey("directory_md5")) {
                    return body.getString("directory_md5");
                }
            }
        } catch (Throwable t) {
            log.error("计算目录md5失败:" + directory, t);
        }

        return null;
    }

    /**
     * 更新远程文本文件内容
     *
     * @param filePath
     * @param content
     */
    @Override
    public void updateTextFileContent(String filePath, String content, String serverUrl) {

        try {
            HttpHeaders headers = buildTraceHeaders();
            headers.set("Content-Type", "application/json");

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("filePath", filePath);
            requestBody.put("fileContent", content);

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(serverUrl + "/update/fileContent", HttpMethod.POST,
                entity, String.class);

            JSONObject body = JSONObject.parseObject(response.getBody());
            if (body != null) {body.getBoolean("success");}

        } catch (Exception e) {

            log.error("更新远程文本文件内容失败:{}", e.getMessage());
        }
    }

    @Override
    public byte[] removeBg(byte[] byteArray, String serverUrl) {
        return removeBg(byteArray, "Inspyrenet", serverUrl);
    }

    @Override
    public byte[] removeBg(byte[] byteArray, String modelType, String serverUrl) {
        HttpHeaders headers = buildTraceHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        Resource fileResource = new ByteArrayResource(byteArray) {
            @Override
            public String getFilename() {
                return "temp.png";
            }
        };

        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", fileResource);
        //body.add("model_type", modelType);

        //可选Inspyrenet、RMBG-1.4
        ResponseEntity<byte[]> res = extraLongRestTemplate.exchange(serverUrl + "/remove_bg?model_type=" + modelType,
            HttpMethod.POST, new org.springframework.http.HttpEntity<>(body, headers), byte[].class);

        if (res.getStatusCode() != HttpStatus.OK) {
            log.warn("调用comfyui接口去除背景异常,code={},res={}", res.getStatusCode(), res);
            throw new RuntimeException("调用comfyui接口去除背景异常");
        }

        log.info("去除背景成功,code={}", res.getStatusCode());

        return res.getBody();
    }

    @Override
    public byte[] callLamaRemoverApi(String originImagePath, String originImagePrefix, byte[] maskBytes,
                                     String resultImagePath, String resultImagePrefix, String resultFormat,
                                     String serverUrl) throws IOException {

        try {
            log.info("开始调用 lama_remover API - originImagePath: {}, resultImagePath: {}, maskSize: {} bytes",
                originImagePath, resultImagePath, maskBytes.length);

            // 构建 multipart/form-data 请求体
            MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
            formData.add("input_path", originImagePath);
            formData.add("input_file_prefix", originImagePrefix);
            formData.add("output_path", resultImagePath);
            formData.add("output_file_prefix", resultImagePrefix);
            formData.add("output_format", resultFormat);

            // 创建文件资源
            ByteArrayResource maskResource = new ByteArrayResource(maskBytes) {
                @Override
                public String getFilename() {
                    return "temp.jpg"; // 指定文件名
                }
            };
            formData.add("mask_file", maskResource);

            // 创建HTTP实体 - 让Spring自动设置multipart/form-data的Content-Type和boundary
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData);

            // 发送请求并接收响应
            ResponseEntity<byte[]> response = extraLongRestTemplate.exchange(serverUrl + "/lama_remover",
                HttpMethod.POST, requestEntity, byte[].class);

            // 处理响应
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] result = response.getBody();
                log.info("lama_remover API 调用成功，返回数据大小: {}", result.length);
                return result;
            } else {
                String errorMsg = "API调用失败: " + response.getStatusCode();
                log.error("lama_remover API 调用失败: {}", errorMsg);
                throw new IOException(errorMsg);
            }

        } catch (Exception e) {
            if (e instanceof IOException) {
                throw (IOException)e;
            }
            log.error("lama_remover API 调用过程中发生异常: {}", e.getMessage(), e);
            throw new IOException("API调用异常: " + e.getMessage(), e);
        }
    }

    @Override
    public JSONObject transClothCollocation(String clothCollocation, String serverUrl) {
        JSONObject json = callJson(serverUrl + "/translate_wearing_recommendation", clothCollocation, HttpMethod.POST,
            longRestTemplate);
        return json != null ? json.getJSONObject("data") : null;
    }

    @Override
    public JSONObject transCustomScene(String customScene, String serverUrl) {
        JSONObject json = callJson(serverUrl + "/translate_custom_scene", customScene, HttpMethod.POST,
            longRestTemplate);
        if (json == null || !json.containsKey("data")) {
            log.warn("json转换失败，请求结果不合法,result={}", json);
            return null;
        }
        JSONObject data = null;
        try {
            String dataStr = formatData(json.getString("data"));
            data = JSONObject.parseObject(dataStr);
        } catch (Exception e) {
            log.error("json转换异常,尝试使用jsonArray解析", e);

            try {
                JSONArray array = json.getJSONArray("data");
                data = array.getJSONObject(0);
            } catch (Exception e1) {
                log.error("json转换异常,直接返回null", e1);
            }
        }
        return data;
    }

    @Override
    public JSONObject generalTranslate(String redrawDesc, String fileServerUrl) {
        JSONObject origin = new JSONObject();
        origin.put("text", redrawDesc);
        JSONObject json = callJson(fileServerUrl + "/general_translate", origin.toJSONString(), HttpMethod.POST,
            longRestTemplate);
        if (json == null) {
            return null;
        }
        String data = formatData(json.getString("data"));
        return JSONObject.parseObject(data);
    }

    @Override
    public JSONObject extractClothCollocation(String clothCollocation, String serverUrl) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("input", clothCollocation);
        JSONObject json = callJson(serverUrl + "/extract_wearing_recommendation", body, HttpMethod.POST, null);
        if (json == null) {
            return null;
        }
        String data = formatData(json.getString("data"));
        return JSONObject.parseObject(data);
    }

    @Override
    public ImageQualityVO isBadImage(String imagePath, String imageUrl, boolean isPureBG, String serverUrl) {
        String imageFullPath = comfyuiOutputPath + imagePath;

        Map<String, Object> body = new HashMap<>();
        body.put("image_path", imageFullPath);
        body.put("threshold", 0.2);
        body.put("is_pure_bg", isPureBG);
        body.put("image_url", imageUrl);

        JSONObject json = null;
        try {
            // 调用接口
            json = callJson(serverUrl + "/is_bad_image", body, HttpMethod.POST, longRestTemplate);

            // 将JSON对象转换为ImageQualityVO对象
            return JSONObject.parseObject(String.valueOf(json), ImageQualityVO.class);
        } catch (Exception e) {
            log.error("调用" + serverUrl + "/is_bad_image接口异常,body=" + body, e);
        }

        return null;
    }

    /**
     * 在指定目录下查找未完整的图片，返回不完整图片名列表
     *
     * @param folderPath
     * @param serverUrl
     * @return
     */
    @Override
    public List<String> findInCompleteImgs(String folderPath, String serverUrl) {

        try {
            JSONObject req = new JSONObject();
            JSONObject jsonObject = callJson(serverUrl + "/findCorruptedImages?folder_path=" + folderPath, req,
                HttpMethod.POST, extraLongRestTemplate);
            JSONArray corruptedImagesArray = jsonObject.getJSONArray("corruptedImages");
            return corruptedImagesArray.toJavaList(String.class);

        } catch (Exception e) {
            log.error("findCorruptedImages failed", e);
        }

        return null;
    }

    @Override
    public boolean rename(String oldPath, String newPath, String serverUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("originPath", oldPath);
        body.put("targetPath", newPath);

        return callOneWay(serverUrl + "/rename", body, null);
    }

    @Override
    public String callLLM(String prompt, String imagePath, String serverUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("prompt", prompt);
        body.put("imagePath", imagePath);

        JSONObject jsonObject = callJson(serverUrl + "/query_gpt", body, HttpMethod.POST, superLongRestTemplate);
        if (jsonObject != null) {
            return jsonObject.getString("data");
        }
        return null;
    }

    @Override
    public String fetchRunLog(String type, Integer port, Integer lines, String serverUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("type", type);
        if (port != null) {
            body.put("port", port);
        }
        body.put("lines", lines);

        JSONObject jsonObject = callJson(serverUrl + "/sys/fetchRunLog", body, HttpMethod.POST, longRestTemplate);
        if (jsonObject != null) {
            return jsonObject.getString("data");
        }
        return null;
    }

    @Override
    public boolean restartPort(Integer port, String fileServerUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("port", port);

        return callOneWay(fileServerUrl + "/sys/restart/port", body, longRestTemplate);
    }

    @Override
    public boolean restartServer(String type, String fileServerUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("type", type);

        return callOneWay(fileServerUrl + "/sys/restart/server", body, superLongRestTemplate);
    }

    @Override
    public boolean updateCreativeNode(String fileServerUrl) {
        Map<String, Object> body = new HashMap<>();

        return callOneWay(fileServerUrl + "/sys/updateCreativeNode", body, HttpMethod.GET, superLongRestTemplate);
    }

    @Override
    public JSONObject uploadToOss(String filePath, String fileName, String fileServerUrl) {
        Map<String, Object> body = new HashMap<>();
        body.put("path", filePath);
        body.put("fileName", fileName);

        return callJson(fileServerUrl + "/upload/oss", body, HttpMethod.POST, extraLongRestTemplate);
    }

    private JSONObject callJson(String serverUrl, Object params, HttpMethod method, RestTemplate restTemplate) {

        if (Objects.isNull(restTemplate)) {
            restTemplate = normalRestTemplate;
        }
        HttpHeaders headers = buildTraceHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> entity = new HttpEntity<>(params, headers);
        ResponseEntity<String> httpRes = restTemplate.exchange(serverUrl, method, entity, String.class);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用{}接口异常,code={}", serverUrl, httpRes.getStatusCode());
            return null;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.error("调用{}接口失败,返回的结果为空", serverUrl);
            return null;
        }
        log.info("调用{}接口成功，结果={}", serverUrl, content);

        try {
            return JSONObject.parseObject(content);
        } catch (Exception e) {
            log.error("调用" + serverUrl + "接口失败,解析结果失败,content=" + content, e);
            return null;
        }
    }

    private boolean callOneWay(String serverUrl, Map<String, Object> request, RestTemplate restTemplate) {
        return callOneWay(serverUrl, request, HttpMethod.POST, restTemplate);
    }

    private boolean callOneWay(String serverUrl, Map<String, Object> request, HttpMethod method,
                               RestTemplate restTemplate) {
        HttpHeaders headers = buildTraceHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);

        try {
            RestTemplate template = restTemplate != null ? restTemplate : this.restTemplate;
            ResponseEntity<String> httpRes = template.exchange(serverUrl, method, entity, String.class);

            if (httpRes.getStatusCode() != HttpStatus.OK) {
                log.error("调用{}接口异常,code={}", serverUrl, httpRes.getStatusCode());
                return false;
            }

            String content = httpRes.getBody();
            if (StringUtils.isBlank(content)) {
                log.warn("调用{}接口异常,返回结果无法解析json,body={}", serverUrl, content);
                return false;
            }
            log.info("调用{}接口,返回结果={}", serverUrl, content);

            JSONObject res = JSONObject.parseObject(content);

            Boolean success = res.getBoolean("success");

            if (success == null || !success) {
                log.warn("调用{}接口失败，error={}", serverUrl, res.getString("message"));
            }

            return success != null && success;

        } catch (Exception e) {
            log.error("调用接口异常:" + serverUrl, e);
            return false;
        }

    }

    /**
     * 构建带traceId的http头
     *
     * @return http头
     */
    private HttpHeaders buildTraceHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("traceId", MDC.get("traceId"));
        return headers;
    }

    /**
     * 下载图片/视频文件
     *
     * @param path           output下的相对路径
     * @param fileNamePrefix 文件名前缀
     * @param serverUrl      服务+方法地址
     * @return 文件内容
     */
    private Map<String, byte[]> downloadFile(String path, String fileNamePrefix, String serverUrl) {
        String fullPath = comfyuiOutputPath + path;
        String url = serverUrl + "?path=" + fullPath + "&prefix=" + fileNamePrefix;

        Map<String, byte[]> result = new HashMap<>(1);
        ByteArrayOutputStream bos = null;
        AtomicReference<String> fileName = new AtomicReference<>();
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();

            ResponseExtractor<Void> responseExtractor = response -> {
                // Stream the response to a file
                byte[] buffer = null;
                try (InputStream in = response.getBody();) {
                    int bytesRead;
                    buffer = BytePoolUtils.borrow();
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                } catch (Exception e) {
                    log.error("读取文件字节流异常" + path, e);
                    throw new RuntimeException(e);
                } finally {
                    BytePoolUtils.returnArray(buffer);
                }
                fileName.set(response.getHeaders().getFirst("XXX-IMAGE-FILE-NAME"));
                return null;
            };

            restTemplate.execute(url, HttpMethod.GET, buildTraceRequestCallback(), responseExtractor);

            bos = out;

            log.info("文件下载成功:{}/{}...", fullPath, fileNamePrefix);
        } catch (Exception e) {
            log.error("下载文件异常,path=" + fullPath + "/" + fileNamePrefix + "...");
            throw e;
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
            } catch (IOException e) {
                log.error("关闭bos异常", e);
            }
        }

        result.put(fileName.get(), bos.toByteArray());

        return result;
    }

    /**
     * 处理结果
     *
     * @param status 状态
     * @param result 结果
     * @return 处理结果
     */
    @Nullable
    private static QueueResult processStatus(JSONObject status, QueueResult result) {
        if (status != null && StringUtils.equals(status.getString("status_str"), "error")) {
            log.error("调用comfyui的history接口，返回的结果是失败，status={}", status);
            result.setCode(QueueCodeEnum.FAILED);
            return result;
        }

        if (status != null && status.getBoolean("completed")) {
            log.info("调用comfyui的history接口，返回的成功，status={}", status);
            result.setCode(QueueCodeEnum.COMPLETED);
            return result;
        }
        return null;
    }

    private static RequestCallback buildTraceRequestCallback() {
        // 定义请求回调以添加请求头
        return request -> {
            HttpHeaders headers = request.getHeaders();
            String traceId = MDC.get("traceId");
            headers.add("traceId", StringUtils.isNotBlank(traceId) ? traceId : "unknown traceId");
        };
    }

    private static String formatData(String data) {
        if (StringUtils.contains(data, "{{") || StringUtils.contains(data, "}}")) {
            data = data.replaceAll("\\{\\{", "{");
            data = data.replaceAll("\\}\\}", "}");
        }
        return data;
    }
}
