package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserPointDO;
import ai.conrain.aigc.platform.dal.example.UserPointExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserPointDAO {
    long countByExample(UserPointExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(UserPointDO record);

    int insertSelective(UserPointDO record);

    List<UserPointDO> selectByExample(UserPointExample example);

    UserPointDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") UserPointDO record, @Param("example") UserPointExample example);

    int updateByExample(@Param("record") UserPointDO record, @Param("example") UserPointExample example);

    int updateByPrimaryKeySelective(UserPointDO record);

    int updateByPrimaryKey(UserPointDO record);

    UserPointDO lockByUserId(Integer userId);

    /**
     * 查询用户累计充值金额
     * @param userIdList 用户id列表
     * @return 累计充值金额
     */
    @MapKey("user_id")
    Map<Integer, Integer> queryUserAccumulatedRechargeByUserIdList(List<Integer> userIdList);
}