<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupCaptionDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="image_group_id" jdbcType="INTEGER" property="imageGroupId" />
    <result column="image_caption_ids" jdbcType="OTHER" property="imageCaptionIds" />
    <result column="result" jdbcType="OTHER" property="result" />
    <result column="caption" jdbcType="OTHER" property="caption" />
    <result column="caption_log" jdbcType="OTHER" property="captionLog" />
    <result column="caption_version" jdbcType="VARCHAR" property="captionVersion" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, image_group_id, image_caption_ids, result, caption, caption_log, caption_version, 
    create_time, modify_time, deleted
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageGroupCaptionExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from image_group_caption
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image_group_caption
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image_group_caption
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from image_group_caption
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO" useGeneratedKeys="true">
    insert into image_group_caption (image_group_id, image_caption_ids, result, 
      caption, caption_log, caption_version, 
      create_time, modify_time, deleted
      )
    values (#{imageGroupId,jdbcType=INTEGER}, #{imageCaptionIds,jdbcType=OTHER}, #{result,jdbcType=OTHER}, 
      #{caption,jdbcType=OTHER}, #{captionLog,jdbcType=OTHER}, #{captionVersion,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO" useGeneratedKeys="true">
    insert into image_group_caption
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageGroupId != null">
        image_group_id,
      </if>
      <if test="imageCaptionIds != null">
        image_caption_ids,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="caption != null">
        caption,
      </if>
      <if test="captionLog != null">
        caption_log,
      </if>
      <if test="captionVersion != null">
        caption_version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageGroupId != null">
        #{imageGroupId,jdbcType=INTEGER},
      </if>
      <if test="imageCaptionIds != null">
        #{imageCaptionIds,jdbcType=OTHER},
      </if>
      <if test="result != null">
        #{result,jdbcType=OTHER},
      </if>
      <if test="caption != null">
        #{caption,jdbcType=OTHER},
      </if>
      <if test="captionLog != null">
        #{captionLog,jdbcType=OTHER},
      </if>
      <if test="captionVersion != null">
        #{captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageGroupCaptionExample" resultType="java.lang.Long">
    select count(*) from image_group_caption
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update image_group_caption
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.imageGroupId != null">
        image_group_id = #{record.imageGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.imageCaptionIds != null">
        image_caption_ids = #{record.imageCaptionIds,jdbcType=OTHER},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=OTHER},
      </if>
      <if test="record.caption != null">
        caption = #{record.caption,jdbcType=OTHER},
      </if>
      <if test="record.captionLog != null">
        caption_log = #{record.captionLog,jdbcType=OTHER},
      </if>
      <if test="record.captionVersion != null">
        caption_version = #{record.captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update image_group_caption
    set id = #{record.id,jdbcType=INTEGER},
      image_group_id = #{record.imageGroupId,jdbcType=INTEGER},
      image_caption_ids = #{record.imageCaptionIds,jdbcType=OTHER},
      result = #{record.result,jdbcType=OTHER},
      caption = #{record.caption,jdbcType=OTHER},
      caption_log = #{record.captionLog,jdbcType=OTHER},
      caption_version = #{record.captionVersion,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO">
    update image_group_caption
    <set>
      <if test="imageGroupId != null">
        image_group_id = #{imageGroupId,jdbcType=INTEGER},
      </if>
      <if test="imageCaptionIds != null">
        image_caption_ids = #{imageCaptionIds,jdbcType=OTHER},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=OTHER},
      </if>
      <if test="caption != null">
        caption = #{caption,jdbcType=OTHER},
      </if>
      <if test="captionLog != null">
        caption_log = #{captionLog,jdbcType=OTHER},
      </if>
      <if test="captionVersion != null">
        caption_version = #{captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO">
    update image_group_caption
    set image_group_id = #{imageGroupId,jdbcType=INTEGER},
      image_caption_ids = #{imageCaptionIds,jdbcType=OTHER},
      result = #{result,jdbcType=OTHER},
      caption = #{caption,jdbcType=OTHER},
      caption_log = #{captionLog,jdbcType=OTHER},
      caption_version = #{captionVersion,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update image_group_caption set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update image_group_caption set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 公共CTE查询片段：用户标注组 -->
  <sql id="User_Caption_Groups_CTE">
    user_caption_groups AS (
      -- 查询有指定数量用户标注的图片组
      SELECT
        igcu.image_group_id,
        COUNT(DISTINCT igcu.user_id) as user_count,
        MAX(igcu.modify_time) as latest_user_modify_time
      FROM image_group_caption_user igcu
      WHERE igcu.deleted = false
        AND igcu.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
          #{userId}
        </foreach>
      GROUP BY igcu.image_group_id
      HAVING COUNT(DISTINCT igcu.user_id) = #{requiredUserCount}
    )
  </sql>

  <!-- 公共CTE查询片段：最终标注组 -->
  <sql id="Final_Caption_Groups_CTE">
    final_caption_groups AS (
      -- 查询已有最终结果的图片组
      SELECT
        igc.image_group_id,
        igc.modify_time as final_modify_time
      FROM image_group_caption igc
      WHERE igc.deleted = false
    )
  </sql>

  <!-- 公共WHERE条件片段 -->
  <sql id="Update_Filter_Condition">
    <choose>
      <when test="forceUpdate != null and forceUpdate == true">
        -- 强制更新模式：返回所有有足够用户标注的图片组
        1 = 1
      </when>
      <otherwise>
        -- 正常模式：只返回需要更新的图片组
        (fcg.image_group_id IS NULL OR ucg.latest_user_modify_time > fcg.final_modify_time)
      </otherwise>
    </choose>
  </sql>

  <!-- 查询需要更新的图片组ID列表 -->
  <select id="selectImageGroupIdsForUpdate" resultType="java.lang.Integer">
    WITH
    <include refid="User_Caption_Groups_CTE" />,
    <include refid="Final_Caption_Groups_CTE" />
    SELECT ucg.image_group_id
    FROM user_caption_groups ucg
    LEFT JOIN final_caption_groups fcg ON ucg.image_group_id = fcg.image_group_id
    WHERE <include refid="Update_Filter_Condition" />
    <if test="limit != null and limit > 0">
      LIMIT #{limit}
    </if>
    <if test="offset != null and offset >= 0">
      OFFSET #{offset}
    </if>
  </select>

  <!-- 查询需要更新的图片组ID总数 -->
  <select id="countImageGroupIdsForUpdate" resultType="java.lang.Long">
    WITH
    <include refid="User_Caption_Groups_CTE" />,
    <include refid="Final_Caption_Groups_CTE" />
    SELECT COUNT(*)
    FROM user_caption_groups ucg
    LEFT JOIN final_caption_groups fcg ON ucg.image_group_id = fcg.image_group_id
    WHERE <include refid="Update_Filter_Condition" />
  </select>

</mapper>