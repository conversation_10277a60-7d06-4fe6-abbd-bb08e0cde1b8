package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 修改记录表
 * 对应数据表：prompt_modification_log
 */
@Data
public class PromptModificationLogDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 模块代码，如A、B、C
     */
    private String moduleCode;

    /**
     * 模块主类型，如服装类型、人脸类型、场景类型
     */
    private String moduleType;

    /**
     * 父级元素id
     */
    private Integer parentElementId;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 旧数据（修改前的数据 json 格式）
     */
    private String oldFieldValue;

    /**
     * 新数据（本次修改的数据 json 格式）
     */
    private String newFieldValue;

    /**
     * 固定属性JSON，记录固定属性值，如{demo:value}
     */
    private String fixedAttrs;

    /**
     * 扩展信息，存储额外的JSON格式数据
     */
    private String extInfo;

    /**
     * 备注信息
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}