package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.SalesSuccessStoriesService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.SalesSuccessStoriesQuery;
import ai.conrain.aigc.platform.service.model.vo.SalesSuccessStoriesVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;

/**
 * SalesSuccessStories控制器
 *
 * <AUTHOR>
 * @version SalesSuccessStoriesService.java v 0.1 2025-06-26 05:49:50
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/salesSuccessStories")
public class SalesSuccessStoriesController {

	/** salesSuccessStoriesService */
	@Autowired
	private SalesSuccessStoriesService salesSuccessStoriesService;

	@Roles({ADMIN, DISTRIBUTOR})
	@GetMapping("/getById/{id}")
	public Result<SalesSuccessStoriesVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(salesSuccessStoriesService.selectById(id));
	}

	@Roles({ADMIN, DISTRIBUTOR})
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody SalesSuccessStoriesVO salesSuccessStories){
		try {
			SalesSuccessStoriesVO data = salesSuccessStoriesService.insert(salesSuccessStories);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加销售成功案例失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建销售成功案例失败");
	}

	@Roles({ADMIN, DISTRIBUTOR})
	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		salesSuccessStoriesService.deleteById(id);
		return Result.success();
	}

	@Roles({ADMIN, DISTRIBUTOR})
	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody SalesSuccessStoriesVO salesSuccessStories){
		salesSuccessStoriesService.updateByIdSelective(salesSuccessStories);
		return Result.success();
	}

	@Roles({ADMIN, DISTRIBUTOR})
	@PostMapping("/queryList")
	public Result<List<SalesSuccessStoriesVO>> querySalesSuccessStoriesList(@Valid @RequestBody SalesSuccessStoriesQuery query){
		return Result.success(salesSuccessStoriesService.querySalesSuccessStoriesList(query));
	}

	@Roles({ADMIN, DISTRIBUTOR})
	@PostMapping("/queryByPage")
	public Result<PageInfo<SalesSuccessStoriesVO>> getSalesSuccessStoriesByPage(@Valid @RequestBody SalesSuccessStoriesQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(salesSuccessStoriesService.querySalesSuccessStoriesByPage(query));
	}
}
