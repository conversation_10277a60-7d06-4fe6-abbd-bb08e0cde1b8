package ai.conrain.aigc.platform.integration.taobao.huiwa;

import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaModelQueryReq;
import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaTaskImageCreativeRequest;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.LianfanHuiwaModelGetRequest;
import com.taobao.api.request.LianfanHuiwaModelSceneSubmitRequest;
import com.taobao.api.request.LianfanHuiwaTaskImageGetRequest;
import com.taobao.api.request.LianfanHuiwaTaskImageSubmitRequest;
import com.taobao.api.response.LianfanHuiwaModelGetResponse;
import com.taobao.api.response.LianfanHuiwaModelSceneSubmitResponse;
import com.taobao.api.response.LianfanHuiwaTaskImageGetResponse;
import com.taobao.api.response.LianfanHuiwaTaskImageSubmitResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class HuiWaServiceImpl implements HuiWaService {

    @Value("${taobao.openapi.url}")
    private String taobaoOpenApiUrl;

    @Value("${taobao.huiwa.app-key}")
    private String appKey;

    @Value("${taobao.huiwa.app-secret}")
    private String appSecret;

    @Override
    public Long uploadPreferenceImage(String referenceImage) {
        // 1、参数校验
        if (StringUtils.isEmpty(referenceImage)) {
            return null;
        }

        // 2、初始化 Client
        TaobaoClient client = initTaobaoClient();

        // 3、构建请求入参
        LianfanHuiwaModelSceneSubmitRequest req = buildSceneSubmitRequest(referenceImage);

        // 4、执行上传操作
        try {
            LianfanHuiwaModelSceneSubmitResponse rsp = client.execute(req);
            if (rsp.isSuccess()) {
                // 返回响应结果中的参考图 id
                return rsp.getModel().getModelId();
            } else {
                log.error(
                    "【绘蛙】【上传参考图】HuiWaServiceImpl::uploadPreferenceImage::参考图上传异常，错误码：{},错误信息：{}",
                    rsp.getErrorCode(), rsp.getSubMsg());
            }

        } catch (ApiException e) {
            log.error("【绘蛙】【上传参考图】HuiWaServiceImpl::uploadPreferenceImage::参考图上传异常，错误信息：{}",
                e.getMessage());
        }

        return null;
    }

    @Override
    public List<LianfanHuiwaModelGetResponse.ModelDTO> queryModelStatus(HuiwaModelQueryReq huiwaModelQueryReq) {
        // 1、参数校验
        if (huiwaModelQueryReq == null || huiwaModelQueryReq.getModelIds().isEmpty()) {
            return null;
        }

        // 2、初始化 Client
        TaobaoClient client = initTaobaoClient();

        // 3、构建参数
        LianfanHuiwaModelGetRequest.ModelPageQuery modelPageQuery = new LianfanHuiwaModelGetRequest.ModelPageQuery();
        // 设置模型 id
        modelPageQuery.setModelIds(huiwaModelQueryReq.getModelIds());
        // 设置模型来源
        modelPageQuery.setSource(huiwaModelQueryReq.getSource());
        // 设置模型类型
        modelPageQuery.setType(huiwaModelQueryReq.getType());
        // 设置当前页
        modelPageQuery.setPageIndex(huiwaModelQueryReq.getPageNow());
        // 设置当前页数
        modelPageQuery.setPageSize(huiwaModelQueryReq.getPageSize());
        LianfanHuiwaModelGetRequest.ModelPageTopQuery modelPageTopQuery
            = new LianfanHuiwaModelGetRequest.ModelPageTopQuery();
        modelPageTopQuery.setModelPageQuery(modelPageQuery);
        LianfanHuiwaModelGetRequest req = new LianfanHuiwaModelGetRequest();
        req.setModelPageTopQuery(modelPageTopQuery);

        // 4、执行上传操作
        try {
            LianfanHuiwaModelGetResponse rsp = client.execute(req);
            if (rsp.isSuccess()) {
                // 返回响应结果中的模型类别信息
                return rsp.getModel();
            } else {
                log.error(
                    "【绘蛙】【查询参考图状态】HuiWaServiceImpl::queryModelStatus::模型状态查询失败，错误码：{},错误信息：{}",
                    rsp.getErrorCode(), rsp.getSubMsg());
            }

        } catch (ApiException e) {
            log.error("【绘蛙】【查询参考图状态】HuiWaServiceImpl::queryModelStatus::模型状态查询失败，错误信息：{}",
                e.getMessage());
        }

        return null;
    }

    @Override
    public Long createImageTask(HuiwaTaskImageCreativeRequest request) {
        // 1、参数校验
        validParams(request);

        // 2、初始化 Client
        TaobaoClient client = initTaobaoClient();

        // 3、构建出图任务请求入参
        LianfanHuiwaTaskImageSubmitRequest req = buildImageSubmitRequest(request.getModelId(), request);

        // 4、执行出图任务
        try {
            LianfanHuiwaTaskImageSubmitResponse rsp = client.execute(req);
            if (rsp.isSuccess()) {
                return rsp.getModel().get(0).getTaskId();
            } else {
                log.error(
                    "【绘蛙】【创建出图任务】HuiWaServiceImpl::createImageTask::创建出图任务失败，错误码：{},错误信息:{}",
                    rsp.getErrorCode(), rsp.getSubMsg());
            }
        } catch (ApiException e) {
            log.error("【绘蛙】【创建出图任务】HuiWaServiceImpl::createImageTask::创建出图任务发生API异常，错误信息：{}",
                e.getMessage(), e);
        }

        return null;
    }

    @Override
    public LianfanHuiwaTaskImageGetResponse.BaseImageTaskDTO queryTaskStatus(Integer taskId) {
        // 1、参数非空校验
        if (taskId == null) {
            log.error("【绘蛙】【查询创作任务】HuiWaServiceImpl::queryTaskStatus:taskId为空,查询结束...");
            return null;
        }

        // 2、初始化 Client
        TaobaoClient client = initTaobaoClient();

        // 3、构建参数
        LianfanHuiwaTaskImageGetRequest req = buildTaskImageGetRequest(taskId);

        // 4、查询任务状态
        try {
            LianfanHuiwaTaskImageGetResponse rsp = client.execute(req);
            if (rsp.isSuccess()) {
                List<LianfanHuiwaTaskImageGetResponse.BaseImageTaskDTO> modelList = rsp.getModel();
                if (modelList == null || modelList.isEmpty()) {
                    log.error(
                        "【绘蛙】【查询创作任务】HuiWaServiceImpl::createImageTask::查询创建出图任务信息，任务不存在,绘蛙 TaskID：{}",
                        taskId);
                    return null;
                }

                // 只取第一个
                return modelList.get(0);
            } else {
                log.error(
                    "【绘蛙】【查询创作任务】HuiWaServiceImpl::createImageTask::创建出图任务失败，错误码：{},错误信息:{}",
                    rsp.getErrorCode(), rsp.getSubMsg());
            }
        } catch (ApiException e) {
            log.error("【绘蛙】【查询创作任务】HuiWaServiceImpl::createImageTask::查询创建出图任务失败,错误信息:{}",
                e.getMessage(), e);
        }

        return null;
    }

    /**
     * 构建上传参考图请求入参
     *
     * @param referenceImage 参考图
     * @return 请求入参
     */
    private LianfanHuiwaModelSceneSubmitRequest buildSceneSubmitRequest(String referenceImage) {
        // 1、初始化sceneTrainRequest
        LianfanHuiwaModelSceneSubmitRequest.SceneTrainRequest sceneTrainRequest
            = new LianfanHuiwaModelSceneSubmitRequest.SceneTrainRequest();
        // 生成随机 uuid 作为参考图名称
        sceneTrainRequest.setModelName(UUID.randomUUID().toString());
        // 设置参考图
        sceneTrainRequest.setImageUrl(referenceImage);

        // 2、初始化baseModelTrainTopRequest
        LianfanHuiwaModelSceneSubmitRequest.BaseModelTrainTopRequest baseModelTrainTopRequest
            = new LianfanHuiwaModelSceneSubmitRequest.BaseModelTrainTopRequest();
        baseModelTrainTopRequest.setBaseModelTrainRequest(sceneTrainRequest);

        // 3、初始化req
        LianfanHuiwaModelSceneSubmitRequest req = new LianfanHuiwaModelSceneSubmitRequest();
        req.setBaseModelTrainTopRequest(baseModelTrainTopRequest);

        // 返回参数
        return req;
    }

    /**
     * 构建出图任务请求入参
     *
     * @param referenceImageId 参考图ID
     * @param request          请求入参
     * @return 构建完成的请求入参
     */
    private LianfanHuiwaTaskImageSubmitRequest buildImageSubmitRequest(Long referenceImageId,
                                                                       HuiwaTaskImageCreativeRequest request) {
        // 1、初始化genImageRequest
        LianfanHuiwaTaskImageSubmitRequest.GenImageRequest genImageRequest
            = new LianfanHuiwaTaskImageSubmitRequest.GenImageRequest();
        // 设置出图质量
        genImageRequest.setTaskQuality(request.getTaskQuality());
        // 设置SceneGroup参数信息
        genImageRequest.setSceneGroup(getGenImageSceneGroupReq(referenceImageId));
        // 设置ItemImage参数信息
        genImageRequest.setItemImage(getItemImage(request));

        // 2、初始化batchGenImageRequest
        LianfanHuiwaTaskImageSubmitRequest.BatchGenImageRequest batchGenImageRequest
            = new LianfanHuiwaTaskImageSubmitRequest.BatchGenImageRequest();
        batchGenImageRequest.setGenImageRequests(Collections.singletonList(genImageRequest));
        // 目前固定为绘蛙的“服装上身”功能
        batchGenImageRequest.setTaskBizType(10L);

        // 3、初始化batchGenImgTopReq
        LianfanHuiwaTaskImageSubmitRequest.BatchGenImgTopReq batchGenImgTopReq
            = new LianfanHuiwaTaskImageSubmitRequest.BatchGenImgTopReq();
        batchGenImgTopReq.setBatchGenImageRequest(batchGenImageRequest);

        // 初始化 req
        LianfanHuiwaTaskImageSubmitRequest req = new LianfanHuiwaTaskImageSubmitRequest();
        req.setBatchGenImgTopReq(batchGenImgTopReq);
        return req;
    }

    /**
     * 构建GenImageSceneGroupReq
     *
     * @param referenceImageId 参考图ID
     * @return GenImageSceneGroupReq
     */
    private static LianfanHuiwaTaskImageSubmitRequest.GenImageSceneGroupReq getGenImageSceneGroupReq(
        Long referenceImageId) {
        LianfanHuiwaTaskImageSubmitRequest.GenImageSceneGroupReq genImageSceneGroupReq
            = new LianfanHuiwaTaskImageSubmitRequest.GenImageSceneGroupReq();
        // 默认一张
        genImageSceneGroupReq.setImageCount(1L);

        // 1-1-1、初始化具体参考图对象
        LianfanHuiwaTaskImageSubmitRequest.GenImageSceneReq genImageSceneReq
            = new LianfanHuiwaTaskImageSubmitRequest.GenImageSceneReq();
        // 设置参考图 ID
        genImageSceneReq.setSceneId(String.valueOf(referenceImageId));

        // 添加参数至genImageSceneGroupReq中
        genImageSceneGroupReq.setScenes(Collections.singletonList(genImageSceneReq));
        return genImageSceneGroupReq;
    }

    /**
     * 构建AigcImageMattingImgDTO
     *
     * @param request 请求入参
     * @return AigcImageMattingImgDTO
     */
    private LianfanHuiwaTaskImageSubmitRequest.AigcImageMattingImgDTO getItemImage(
        HuiwaTaskImageCreativeRequest request) {
        // 1、初始化inputImageMattingDTO
        LianfanHuiwaTaskImageSubmitRequest.InputImageMattingDTO inputImageMattingDTO
            = new LianfanHuiwaTaskImageSubmitRequest.InputImageMattingDTO();
        // 设置服装类型
        inputImageMattingDTO.setImageType(getClothType(request.getClothType()));
        // 设置原始服装图片
        inputImageMattingDTO.setOriginalImageUrl(request.getClothImage());
        // 设置用户上传服装原图，用于原分辨率生成使用
        inputImageMattingDTO.setUserUploadImageUrl(request.getClothImage());

        // 2、初始化aigcImageMattingImgDTO
        LianfanHuiwaTaskImageSubmitRequest.AigcImageMattingImgDTO aigcImageMattingImgDTO
            = new LianfanHuiwaTaskImageSubmitRequest.AigcImageMattingImgDTO();
        aigcImageMattingImgDTO.setCompositeImages(Collections.singletonList(inputImageMattingDTO));
        return aigcImageMattingImgDTO;
    }

    /**
     * 构建参数LianfanHuiwaTaskImageGetRequest
     *
     * @param taskId 任务 id
     * @return LianfanHuiwaTaskImageGetRequest
     */
    private LianfanHuiwaTaskImageGetRequest buildTaskImageGetRequest(Integer taskId) {
        // 1、初始化taskQuery
        LianfanHuiwaTaskImageGetRequest.TaskQuery taskQuery = new LianfanHuiwaTaskImageGetRequest.TaskQuery();
        taskQuery.setMainTaskId(Long.valueOf(taskId));
        taskQuery.setPageIndex(1L);
        taskQuery.setPageSize(10L);

        // 2、初始化taskTopQuery
        LianfanHuiwaTaskImageGetRequest.TaskTopQuery taskTopQuery = new LianfanHuiwaTaskImageGetRequest.TaskTopQuery();
        taskTopQuery.setTaskQuery(taskQuery);

        // 3、初始化 req
        LianfanHuiwaTaskImageGetRequest req = new LianfanHuiwaTaskImageGetRequest();
        req.setTaskTopQuery(taskTopQuery);
        return req;
    }

    /**
     * 初始化 taobaoClient
     *
     * @return taobaoClient
     */
    public TaobaoClient initTaobaoClient() {
        return new DefaultTaobaoClient(taobaoOpenApiUrl, appKey, appSecret);
    }

    /**
     * 参数校验
     *
     * @param request 请求入参
     */
    private void validParams(HuiwaTaskImageCreativeRequest request) {
        if (StringUtils.isBlank(request.getClothImage()) ||
            StringUtils.isEmpty(request.getClothType())) {
            throw new IllegalArgumentException("getClothImage or ClothType is null");
        }
    }

    /**
     * 获取服装类型
     *
     * @return 1表示上装, 2表示下装, 3表示连衣裙
     */
    public Long getClothType(String clothType) {
        switch (clothType.toLowerCase()) {
            case "upper garment":
                return 1L;
            case "lower garment":
                return 2L;
            case "outfit":
                return 3L;
            default:
                log.error(
                    "【绘蛙】【获取服装类型】HuiWaServiceImpl::getClothType::上传服装类型不存在，服装类型:{}，默认为上装...",
                    clothType);
                return 1L;
        }
    }
}
