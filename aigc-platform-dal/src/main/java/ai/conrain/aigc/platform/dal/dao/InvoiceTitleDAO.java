package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO;
import ai.conrain.aigc.platform.dal.example.InvoiceTitleExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InvoiceTitleDAO {
    long countByExample(InvoiceTitleExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(InvoiceTitleDO record);

    int insertSelective(InvoiceTitleDO record);

    List<InvoiceTitleDO> selectByExample(InvoiceTitleExample example);

    InvoiceTitleDO selectByPrimaryKey(Integer id);

    InvoiceTitleDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") InvoiceTitleDO record, @Param("example") InvoiceTitleExample example);

    int updateByExample(@Param("record") InvoiceTitleDO record, @Param("example") InvoiceTitleExample example);

    int updateByPrimaryKeySelective(InvoiceTitleDO record);

    int updateByPrimaryKey(InvoiceTitleDO record);

    int logicalDeleteByExample(@Param("example") InvoiceTitleExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}