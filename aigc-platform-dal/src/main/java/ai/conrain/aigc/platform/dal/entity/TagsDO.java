package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 标签配置表
 * 对应数据表：tags
 */
@Data
public class TagsDO implements Serializable {
    /**
     * 标签id
     */
    private Integer id;

    /**
     * 标签类型
     */
    private String type;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 是否默认选中，0不选中、1默认选中
     */
    private Boolean defChecked;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 标签详情
     */
    private String detail;

    private static final long serialVersionUID = 1L;
}