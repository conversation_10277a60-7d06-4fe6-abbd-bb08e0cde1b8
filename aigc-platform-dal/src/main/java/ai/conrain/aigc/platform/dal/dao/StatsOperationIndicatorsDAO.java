package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsOperationIndicatorsDO;
import ai.conrain.aigc.platform.dal.example.StatsOperationIndicatorsExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsOperationIndicatorsDAO {
    long countByExample(StatsOperationIndicatorsExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsOperationIndicatorsDO record);

    int insertSelective(StatsOperationIndicatorsDO record);

    List<StatsOperationIndicatorsDO> selectByExampleWithBLOBs(StatsOperationIndicatorsExample example);

    List<StatsOperationIndicatorsDO> selectByExample(StatsOperationIndicatorsExample example);

    StatsOperationIndicatorsDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsOperationIndicatorsDO record, @Param("example") StatsOperationIndicatorsExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsOperationIndicatorsDO record, @Param("example") StatsOperationIndicatorsExample example);

    int updateByExample(@Param("record") StatsOperationIndicatorsDO record, @Param("example") StatsOperationIndicatorsExample example);

    int updateByPrimaryKeySelective(StatsOperationIndicatorsDO record);

    int updateByPrimaryKeyWithBLOBs(StatsOperationIndicatorsDO record);

    int updateByPrimaryKey(StatsOperationIndicatorsDO record);

    /**
     * 批量插入或更新
     *
     * @param statsSaleIndicatorsDOList 数据集合
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<StatsOperationIndicatorsDO> statsSaleIndicatorsDOList);

    /**
     * 查询指定时间段内指定统计类型的数据
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param statsType 统计类型
     * @return 统计数据列表
     */
    List<StatsOperationIndicatorsDO> selectStatsInfoByDateAndPeriod(@Param("startDate") String startDate,
                                                                    @Param("endDate") String endDate,
                                                                    @Param("statsType") String statsType);

    List<StatsOperationIndicatorsDO> selectStatsInfoByDateAndCondition(@Param("startDate") String startDate,
                                                                       @Param("endDate") String endDate,
                                                                       @Param("statsType") String statsType,
                                                                       @Param("userId") Integer userId,
                                                                       @Param("nickName") String nickName);
}