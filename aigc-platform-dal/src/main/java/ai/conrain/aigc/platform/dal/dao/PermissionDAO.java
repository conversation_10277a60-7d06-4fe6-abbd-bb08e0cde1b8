package ai.conrain.aigc.platform.dal.dao;

import java.util.List;

import ai.conrain.aigc.platform.dal.entity.PermissionDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PermissionDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(PermissionDO record);

    PermissionDO selectByPrimaryKey(Integer id);

    List<PermissionDO> selectAll();

    int updateByPrimaryKey(PermissionDO record);

    int batchInitPermissions(List<PermissionDO> list);
}