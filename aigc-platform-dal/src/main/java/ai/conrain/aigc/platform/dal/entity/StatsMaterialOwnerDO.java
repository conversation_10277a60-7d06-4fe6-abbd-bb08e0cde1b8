package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 服装负责人表
 * 对应数据表：stats_material_owner
 */
@Data
public class StatsMaterialOwnerDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * 用户 id（为 0 时则是汇总）
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 交付数量
     */
    private Integer deliveryCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}