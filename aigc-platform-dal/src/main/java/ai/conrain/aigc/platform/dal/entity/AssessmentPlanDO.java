package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 销售考核计划
 * 对应数据表：assessment_plan
 */
@Data
public class AssessmentPlanDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 结算主体类型
     */
    private String principalType;

    /**
     * 结算主体 id
     */
    private Integer principalId;

    /**
     * 考核类型
     */
    private String type;

    /**
     * 考核任务状态
     */
    private String status;

    /**
     * 考核指标
     */
    private String kpiTarget;

    /**
     * 实际完成情况
     */
    private String kpiActual;

    /**
     * 考核计划开始日期
     */
    private Date planFromDate;

    /**
     * 考核计划结束日期
     */
    private Date planEndDate;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 创建人用户id
     */
    private Integer creatorUserId;

    /**
     * 修改人用户id
     */
    private Integer modifyUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}