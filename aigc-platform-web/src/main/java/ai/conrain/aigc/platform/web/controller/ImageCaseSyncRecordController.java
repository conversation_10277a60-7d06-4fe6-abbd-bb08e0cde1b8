package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;
import ai.conrain.aigc.platform.service.model.query.ImageCaseSyncRecordQuery;
import ai.conrain.aigc.platform.service.component.ImageCaseSyncRecordService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ImageCaseSyncRecord控制器
 *
 * <AUTHOR>
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageCaseSyncRecord")
public class ImageCaseSyncRecordController {

	/** imageCaseSyncRecordService */
	@Autowired
	private ImageCaseSyncRecordService imageCaseSyncRecordService;
	
	@GetMapping("/getById/{id}")
	public Result<ImageCaseSyncRecordVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageCaseSyncRecordService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageCaseSyncRecordVO imageCaseSyncRecord){
		try {
			ImageCaseSyncRecordVO data = imageCaseSyncRecordService.insert(imageCaseSyncRecord);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageCaseSyncRecordService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody ImageCaseSyncRecordVO imageCaseSyncRecord){
		imageCaseSyncRecordService.updateById(imageCaseSyncRecord);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<ImageCaseSyncRecordVO>> findAll(){
		return Result.success(imageCaseSyncRecordService.findAll());
	}

}
