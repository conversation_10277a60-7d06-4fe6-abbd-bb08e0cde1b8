package ai.conrain.aigc.platform.service.model.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * 搜索汇总信息
 *
 * <AUTHOR>
 * @version SearchSummaryInfo.java v 0.1 2025-08-19
 */
@Data
public class SearchSummaryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 请求的服装图片数量 */
    private Integer clothImgCount;

    /** 请求的参考图片数量 */
    private Integer refImgCount;

    /** 返回的流派组数 */
    private Integer genreGroupCount;

    /** 返回的候选图片总数 */
    private Integer totalCandidateCount;

    /** 总耗时（毫秒） */
    private Long totalDurationMs;

    /** 各环节耗时详情（毫秒） */
    private Map<String, Long> stepDurations;

    /** 每个流派的候选图片数量统计 */
    private Map<String, Integer> genreStats;

    /** 搜索参数摘要 */
    private String searchParamsSummary;

    /** 是否启用并发 */
    private Boolean enableConcurrency;

    /** 排除的批次数量 */
    private Integer excludeBatchCount;

    /** 指定的流派数量 */
    private Integer specifiedGenreCount;
}