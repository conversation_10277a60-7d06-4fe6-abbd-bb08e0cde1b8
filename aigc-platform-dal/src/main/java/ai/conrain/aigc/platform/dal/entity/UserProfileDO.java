package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户属性表
 * 对应数据表：user_profile
 */
@Data
public class UserProfileDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 关联的用户id
     */
    private Integer uid;

    /**
     * 属性key
     */
    private String profileKey;

    /**
     * 属性val
     */
    private String profileVal;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}