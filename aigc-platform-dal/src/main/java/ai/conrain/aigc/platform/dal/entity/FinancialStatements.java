package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 财务报表
 * 对应数据表：financial_statements
 */
@Data
public class FinancialStatements implements Serializable {
    /**
     * 日期
     */
    private String dt;

    /**
     * 商户id，如果是汇总则是total
     */
    private String merchantId;

    /**
     * 订单笔数
     */
    private Integer orderNum;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    private static final long serialVersionUID = 1L;
}