package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.train.ClothLoraTrainService;
import ai.conrain.aigc.platform.service.component.train.CommonLoraTrainService;
import ai.conrain.aigc.platform.service.component.train.MultiColorClothLoraTrainService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.MaterialInfoQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.AddClothMaterialRequest;
import ai.conrain.aigc.platform.service.model.request.AddCommonMaterialRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MaterialInfo控制器
 *
 * <AUTHOR>
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/materialInfo")
public class MaterialInfoController {
    @Autowired
    private MaterialInfoService materialInfoService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ClothLoraTrainService clothLoraTrainService;
    @Autowired
    private MultiColorClothLoraTrainService multiColorClothLoraTrainService;
    @Autowired
    private CommonLoraTrainService commonLoraTrainService;

    @Autowired
    private TairService tairService;

    private static final String UPLOAD_TAIR_PREFIX = "upload_";

    @GetMapping(value = "/clothExamCfg")
    public Result<JSONObject> getClothExamConfig() {
        String cfg = systemConfigService.queryValueByKey(SystemConstants.CLOTH_EXAM_CFG);
        if (StringUtil.isNotBlank(cfg) && CommonUtil.isValidJson(cfg)) {
            return Result.success(CommonUtil.parseObject(cfg));
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "素材页面示例配置为空");
    }

    @GetMapping("/getById/{id}")
    public Result<MaterialInfoVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(materialInfoService.selectById(id));
    }

    @PostMapping("/getMaxColorNumberCfg")
    public Result<Map<String, Integer>> getMaxColorNumberCfg() {

        String cfg = systemConfigService.queryValueByKey(SystemConstants.COLOR_NUMBER_CFG);
        if (cfg != null && CommonUtil.isValidJson(cfg)) {
            return Result.success(JSONObject.parseObject(cfg, new TypeReference<Map<String, Integer>>() {}));
        }

        return Result.success(new HashMap<>());
    }

    @PostMapping("/create")
    @Transactional(rollbackFor = Throwable.class)
    public Result<Integer> uploadCloth(@Valid @RequestBody AddClothMaterialRequest request) {

        checkDuplicateRequest(request.getName(), request.getMaterialType());

        if (request.getMaterialDetail() == null) {
            return Result.error("上传的素材不完整，materialDetail is null");
        }

        if (CollectionUtils.isEmpty(request.getMaterialDetail().getFullShotImgList()) || CollectionUtils.isEmpty(
            request.getMaterialDetail().getDetailShotImgList())) {
            log.error("上传的素材不完整，full image list / detail image list为空");
            return Result.error("上传的素材不完整");
        }

        if (CollectionUtils.isNotEmpty(request.getMaterialDetail().getFullShotImgList())) {
            for (ClothMaterialImg c : request.getMaterialDetail().getFullShotImgList()) {
                if (StringUtils.isBlank(c.getImgUrl()) || StringUtils.isBlank(c.getViewTags())
                    || c.getColorGroupNumber() == null) {
                    log.error("上传的素材不完整，字段值缺失:{}", c);
                    return Result.error("上传的素材不完整");
                }
            }
        }

        if (request.isAutoGen()) {
            if (!request.applyAutoGen()) {
                log.error("自动生成图片请求参数异常");
                return Result.error("自动生成图片请求参数异常");
            }
        }

        //去除字符串中的所有空白字符，包括空格、换行符、制表符等
        if (StringUtils.isNotBlank(request.getMatchPrefer())) {
            request.setMatchPrefer(request.getMatchPrefer().replaceAll("\\s+", ""));
        }

        MaterialModelVO model = null;
        if (request.isMultiColors()) {
            model = multiColorClothLoraTrainService.create(request);
        } else {
            model = clothLoraTrainService.create(request);
        }

        return Result.success(model.getId());
    }

    @PostMapping("/uploadCommonMaterialInfo")
    @Transactional(rollbackFor = Throwable.class)
    public Result<Integer> uploadElement(@Valid @RequestBody AddCommonMaterialRequest request) {

        checkDuplicateRequest(request.getName(), request.getMaterialType());

        if (request.getMaterialDetail() == null || CollectionUtils.isEmpty(request.getMaterialDetail().getImgUrls())) {
            return Result.error("上传的素材不完整");
        }

        MaterialModelVO model = commonLoraTrainService.create(request);

        return Result.success(model.getId());
    }

    //5秒内同一个用户不能重复上传相同名称的素材，防重复提交
    private void checkDuplicateRequest(String name, String materialType) {
        AssertUtil.assertNotBlank(name, "上传的素材不完整，name为空");
        AssertUtil.assertNotBlank(materialType, "上传的素材不完整，materialType为空");
        boolean lock = tairService.acquireLock(
            UPLOAD_TAIR_PREFIX + OperationContextHolder.getOperatorUserId().toString() + name, 5000);
        AssertUtil.assertTrue(lock, ResultCode.CANNOT_ACQUIRE_LOCK, "当前业务正在处理中，请稍候再试");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull Integer id) {
        materialInfoService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody MaterialInfoVO materialInfo) {
        materialInfoService.updateByIdSelective(materialInfo);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<MaterialInfoVO>> queryMaterialInfoList(@Valid @RequestBody MaterialInfoQuery query) {
        if (!OperationContextHolder.isAdmin()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        return Result.success(materialInfoService.queryMaterialInfoList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<MaterialInfoVO>> getMaterialInfoByPage(@Valid @RequestBody MaterialInfoQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        if (!OperationContextHolder.isAdmin()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        return Result.success(materialInfoService.queryMaterialInfoByPage(query));
    }

    @PostMapping("/checkMaterialNameExists")
    public Result<Boolean> checkMaterialNameExists(@JsonArg String name) {
        if (StringUtils.isBlank(name)) {
            return Result.success();
        }

        MaterialModelQuery query = new MaterialModelQuery();
        query.setName(name);
        query.setUserId(OperationContextHolder.getMasterUserId());

        return Result.success(materialModelService.queryMaterialModelCount(query) > 0);
    }
}
