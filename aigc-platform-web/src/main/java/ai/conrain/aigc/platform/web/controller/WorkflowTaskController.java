package ai.conrain.aigc.platform.web.controller;

import java.util.*;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.service.component.WorkflowTaskService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * WorkflowTask控制器
 *
 * <AUTHOR>
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/workflowTask")
public class WorkflowTaskController {

    /** workflowTaskService */
    @Autowired
    private WorkflowTaskService workflowTaskService;

    @GetMapping("/getById/{id}")
    public Result<WorkflowTaskVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(workflowTaskService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody WorkflowTaskVO workflowTask) {
        try {
            WorkflowTaskVO data = workflowTaskService.insert(workflowTask);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        workflowTaskService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody WorkflowTaskVO workflowTask) {
        workflowTaskService.updateByIdSelective(workflowTask);
        return Result.success();
    }

    @PostMapping("/updateByBizId")
    public Result<?> updateByBizIdSelective(@Valid @RequestBody WorkflowTaskVO workflowTask) {
        workflowTaskService.updateByBizIdSelective(workflowTask);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<WorkflowTaskVO>> queryWorkflowTaskList(@Valid @RequestBody WorkflowTaskQuery query) {
        return Result.success(workflowTaskService.queryWorkflowTaskList(query));
    }

    @PostMapping("/queryGroup")
    public Result<Map<Integer, List<WorkflowTaskVO>>> queryWorkflowTaskGroup(@Valid @RequestBody WorkflowTaskQuery query) {
        List<WorkflowTaskVO> tasks = workflowTaskService.queryWorkflowTaskList(query);
        Map<Integer, List<WorkflowTaskVO>> collect = tasks.stream()
            .collect(Collectors.groupingBy(WorkflowTaskVO::getBizId));
        return Result.success(collect);
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<WorkflowTaskVO>> getWorkflowTaskByPage(@Valid @RequestBody WorkflowTaskQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(workflowTaskService.queryWorkflowTaskByPage(query));
    }
}
