package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * AB测试结果表
 * 对应数据表：test_result
 */
@Data
public class TestResultDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 测试计划id
     */
    private Integer planId;

    /**
     * 测试项目id
     */
    private Integer itemId;

    /**
     * 分组id
     */
    private Integer groupId;

    /**
     * 轮次id
     */
    private Integer roundId;

    /**
     * 类型，TRAIN、CREATIVE
     */
    private String type;

    /**
     * 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED
     */
    private String status;

    /** 分组类型 */
    private String groupType;

    /**
     * 批次id
     */
    private Integer batchId;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 种子
     */
    private String seed;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 缩略图地址
     */
    private String miniImageUrl;

    /**
     * 得分,正负
     */
    private Boolean score;

    /**
     * 图库id
     */
    private Integer caseId;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 请求参数
     */
    private String requestParams;

    /** 扩展信息 */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}