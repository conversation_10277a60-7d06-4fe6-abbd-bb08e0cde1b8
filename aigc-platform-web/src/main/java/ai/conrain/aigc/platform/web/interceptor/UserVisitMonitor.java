package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;

@Slf4j
@Component
public class UserVisitMonitor implements HandlerInterceptor {

    @Autowired
    private TairService tairService;

    private static final String cacheKeyPre = "vc_";
    private static final Integer cacheTimeInSecs = 10 * 60;

    @Autowired
    private UserService userService;

    public static final String LAST_VISIT_DATE = "LVD_";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        try {
            if (OperationContextHolder.getContext() != null
                && OperationContextHolder.getContext().getOperationSession() != null
                && OperationContextHolder.getContext().getOperationSession().getLoginUser() != null) {

                //记录当前用户访问到user.last_visit_date
                //yyyyMMdd
                String date = DateUtils.formatShort(new Date());
                String visitTodayKey = LAST_VISIT_DATE + OperationContextHolder.getMasterUserId() + date;
                String visitToday = tairService.getString(visitTodayKey);
                if (visitToday == null) {
                    UserVO targetMaster = new UserVO();
                    targetMaster.setId(OperationContextHolder.getMasterUserId());
                    targetMaster.setLastVisitDate(date);
                    userService.updateByIdSelective(targetMaster);

                    //缓存一天，每天最多只记录一次用户当日访问
                    tairService.setString(visitTodayKey, CommonConstants.YES, 24 * 3600);
                }

                //投次体验账号来访，钉钉通知
                if (StringUtils.isNotBlank(OperationContextHolder.getContext().getOperationSession().getLoginUser().getMemo())
                    && OperationContextHolder.getContext().getOperationSession().getLoginUser().getMemo().contains("投资体验")) {

                    //记录当前用户访问到tair中
                    String cacheKey = cacheKeyPre + OperationContextHolder.getOperatorUserId();
                    String t = tairService.getString(cacheKey);
                    if (t == null) {
                        monitor(request, cacheKey);
                    } else {
                        //5分钟内只记一次
                        if (System.currentTimeMillis() - Long.parseLong(t) > 5 * 60 * 1000) {
                            monitor(request, cacheKey);
                        }
                    }
                }
            }
        } catch (Throwable e) {
            log.error("UserVisitMonitor error,ignore", e);
        }

        return true;
    }

    private void monitor(HttpServletRequest request, String cacheKey) {
        tairService.setString(cacheKey, String.valueOf(System.currentTimeMillis()), cacheTimeInSecs);
        DingTalkNoticeHelper.sendMsg2BizGroup("【投资体验用户】" + OperationContextHolder.getOperatorNick() + " 正在访问平台",
                Arrays.asList("13732280808"));
    }
}
