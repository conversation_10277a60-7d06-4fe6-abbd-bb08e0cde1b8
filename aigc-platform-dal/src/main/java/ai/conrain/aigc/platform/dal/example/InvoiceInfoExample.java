package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.InvoiceInfoDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InvoiceInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public InvoiceInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public InvoiceInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public InvoiceInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public InvoiceInfoExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIsNull() {
            addCriterion("master_user_id is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIsNotNull() {
            addCriterion("master_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdEqualTo(Integer value) {
            addCriterion("master_user_id =", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotEqualTo(Integer value) {
            addCriterion("master_user_id <>", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdGreaterThan(Integer value) {
            addCriterion("master_user_id >", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("master_user_id >=", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdLessThan(Integer value) {
            addCriterion("master_user_id <", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("master_user_id <=", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIn(List<Integer> values) {
            addCriterion("master_user_id in", values, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotIn(List<Integer> values) {
            addCriterion("master_user_id not in", values, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdBetween(Integer value1, Integer value2) {
            addCriterion("master_user_id between", value1, value2, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("master_user_id not between", value1, value2, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIsNull() {
            addCriterion("master_user_nick is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIsNotNull() {
            addCriterion("master_user_nick is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickEqualTo(String value) {
            addCriterion("master_user_nick =", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotEqualTo(String value) {
            addCriterion("master_user_nick <>", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickGreaterThan(String value) {
            addCriterion("master_user_nick >", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickGreaterThanOrEqualTo(String value) {
            addCriterion("master_user_nick >=", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLessThan(String value) {
            addCriterion("master_user_nick <", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLessThanOrEqualTo(String value) {
            addCriterion("master_user_nick <=", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLike(String value) {
            addCriterion("master_user_nick like", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotLike(String value) {
            addCriterion("master_user_nick not like", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIn(List<String> values) {
            addCriterion("master_user_nick in", values, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotIn(List<String> values) {
            addCriterion("master_user_nick not in", values, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickBetween(String value1, String value2) {
            addCriterion("master_user_nick between", value1, value2, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotBetween(String value1, String value2) {
            addCriterion("master_user_nick not between", value1, value2, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIsNull() {
            addCriterion("master_user_login_id is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIsNotNull() {
            addCriterion("master_user_login_id is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdEqualTo(String value) {
            addCriterion("master_user_login_id =", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotEqualTo(String value) {
            addCriterion("master_user_login_id <>", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdGreaterThan(String value) {
            addCriterion("master_user_login_id >", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdGreaterThanOrEqualTo(String value) {
            addCriterion("master_user_login_id >=", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLessThan(String value) {
            addCriterion("master_user_login_id <", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLessThanOrEqualTo(String value) {
            addCriterion("master_user_login_id <=", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLike(String value) {
            addCriterion("master_user_login_id like", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotLike(String value) {
            addCriterion("master_user_login_id not like", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIn(List<String> values) {
            addCriterion("master_user_login_id in", values, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotIn(List<String> values) {
            addCriterion("master_user_login_id not in", values, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdBetween(String value1, String value2) {
            addCriterion("master_user_login_id between", value1, value2, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotBetween(String value1, String value2) {
            addCriterion("master_user_login_id not between", value1, value2, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIsNull() {
            addCriterion("operator_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIsNotNull() {
            addCriterion("operator_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdEqualTo(Integer value) {
            addCriterion("operator_user_id =", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotEqualTo(Integer value) {
            addCriterion("operator_user_id <>", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdGreaterThan(Integer value) {
            addCriterion("operator_user_id >", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_user_id >=", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdLessThan(Integer value) {
            addCriterion("operator_user_id <", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_user_id <=", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIn(List<Integer> values) {
            addCriterion("operator_user_id in", values, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotIn(List<Integer> values) {
            addCriterion("operator_user_id not in", values, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_user_id between", value1, value2, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_user_id not between", value1, value2, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIsNull() {
            addCriterion("operator_user_nick is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIsNotNull() {
            addCriterion("operator_user_nick is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickEqualTo(String value) {
            addCriterion("operator_user_nick =", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotEqualTo(String value) {
            addCriterion("operator_user_nick <>", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickGreaterThan(String value) {
            addCriterion("operator_user_nick >", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickGreaterThanOrEqualTo(String value) {
            addCriterion("operator_user_nick >=", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLessThan(String value) {
            addCriterion("operator_user_nick <", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLessThanOrEqualTo(String value) {
            addCriterion("operator_user_nick <=", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLike(String value) {
            addCriterion("operator_user_nick like", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotLike(String value) {
            addCriterion("operator_user_nick not like", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIn(List<String> values) {
            addCriterion("operator_user_nick in", values, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotIn(List<String> values) {
            addCriterion("operator_user_nick not in", values, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickBetween(String value1, String value2) {
            addCriterion("operator_user_nick between", value1, value2, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotBetween(String value1, String value2) {
            addCriterion("operator_user_nick not between", value1, value2, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIsNull() {
            addCriterion("operator_user_login_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIsNotNull() {
            addCriterion("operator_user_login_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdEqualTo(String value) {
            addCriterion("operator_user_login_id =", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotEqualTo(String value) {
            addCriterion("operator_user_login_id <>", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdGreaterThan(String value) {
            addCriterion("operator_user_login_id >", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdGreaterThanOrEqualTo(String value) {
            addCriterion("operator_user_login_id >=", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLessThan(String value) {
            addCriterion("operator_user_login_id <", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLessThanOrEqualTo(String value) {
            addCriterion("operator_user_login_id <=", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLike(String value) {
            addCriterion("operator_user_login_id like", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotLike(String value) {
            addCriterion("operator_user_login_id not like", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIn(List<String> values) {
            addCriterion("operator_user_login_id in", values, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotIn(List<String> values) {
            addCriterion("operator_user_login_id not in", values, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdBetween(String value1, String value2) {
            addCriterion("operator_user_login_id between", value1, value2, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotBetween(String value1, String value2) {
            addCriterion("operator_user_login_id not between", value1, value2, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("invoice_type is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("invoice_type is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(String value) {
            addCriterion("invoice_type =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(String value) {
            addCriterion("invoice_type <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(String value) {
            addCriterion("invoice_type >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_type >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(String value) {
            addCriterion("invoice_type <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(String value) {
            addCriterion("invoice_type <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLike(String value) {
            addCriterion("invoice_type like", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotLike(String value) {
            addCriterion("invoice_type not like", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<String> values) {
            addCriterion("invoice_type in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<String> values) {
            addCriterion("invoice_type not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(String value1, String value2) {
            addCriterion("invoice_type between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(String value1, String value2) {
            addCriterion("invoice_type not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeIsNull() {
            addCriterion("subject_type is null");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeIsNotNull() {
            addCriterion("subject_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeEqualTo(String value) {
            addCriterion("subject_type =", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeNotEqualTo(String value) {
            addCriterion("subject_type <>", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeGreaterThan(String value) {
            addCriterion("subject_type >", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeGreaterThanOrEqualTo(String value) {
            addCriterion("subject_type >=", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeLessThan(String value) {
            addCriterion("subject_type <", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeLessThanOrEqualTo(String value) {
            addCriterion("subject_type <=", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeLike(String value) {
            addCriterion("subject_type like", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeNotLike(String value) {
            addCriterion("subject_type not like", value, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeIn(List<String> values) {
            addCriterion("subject_type in", values, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeNotIn(List<String> values) {
            addCriterion("subject_type not in", values, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeBetween(String value1, String value2) {
            addCriterion("subject_type between", value1, value2, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectTypeNotBetween(String value1, String value2) {
            addCriterion("subject_type not between", value1, value2, "subjectType");
            return (Criteria) this;
        }

        public Criteria andSubjectNameIsNull() {
            addCriterion("subject_name is null");
            return (Criteria) this;
        }

        public Criteria andSubjectNameIsNotNull() {
            addCriterion("subject_name is not null");
            return (Criteria) this;
        }

        public Criteria andSubjectNameEqualTo(String value) {
            addCriterion("subject_name =", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameNotEqualTo(String value) {
            addCriterion("subject_name <>", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameGreaterThan(String value) {
            addCriterion("subject_name >", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("subject_name >=", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameLessThan(String value) {
            addCriterion("subject_name <", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameLessThanOrEqualTo(String value) {
            addCriterion("subject_name <=", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameLike(String value) {
            addCriterion("subject_name like", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameNotLike(String value) {
            addCriterion("subject_name not like", value, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameIn(List<String> values) {
            addCriterion("subject_name in", values, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameNotIn(List<String> values) {
            addCriterion("subject_name not in", values, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameBetween(String value1, String value2) {
            addCriterion("subject_name between", value1, value2, "subjectName");
            return (Criteria) this;
        }

        public Criteria andSubjectNameNotBetween(String value1, String value2) {
            addCriterion("subject_name not between", value1, value2, "subjectName");
            return (Criteria) this;
        }

        public Criteria andCreditCodeIsNull() {
            addCriterion("credit_code is null");
            return (Criteria) this;
        }

        public Criteria andCreditCodeIsNotNull() {
            addCriterion("credit_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreditCodeEqualTo(String value) {
            addCriterion("credit_code =", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeNotEqualTo(String value) {
            addCriterion("credit_code <>", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeGreaterThan(String value) {
            addCriterion("credit_code >", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeGreaterThanOrEqualTo(String value) {
            addCriterion("credit_code >=", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeLessThan(String value) {
            addCriterion("credit_code <", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeLessThanOrEqualTo(String value) {
            addCriterion("credit_code <=", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeLike(String value) {
            addCriterion("credit_code like", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeNotLike(String value) {
            addCriterion("credit_code not like", value, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeIn(List<String> values) {
            addCriterion("credit_code in", values, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeNotIn(List<String> values) {
            addCriterion("credit_code not in", values, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeBetween(String value1, String value2) {
            addCriterion("credit_code between", value1, value2, "creditCode");
            return (Criteria) this;
        }

        public Criteria andCreditCodeNotBetween(String value1, String value2) {
            addCriterion("credit_code not between", value1, value2, "creditCode");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressIsNull() {
            addCriterion("business_address is null");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressIsNotNull() {
            addCriterion("business_address is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressEqualTo(String value) {
            addCriterion("business_address =", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressNotEqualTo(String value) {
            addCriterion("business_address <>", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressGreaterThan(String value) {
            addCriterion("business_address >", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressGreaterThanOrEqualTo(String value) {
            addCriterion("business_address >=", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressLessThan(String value) {
            addCriterion("business_address <", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressLessThanOrEqualTo(String value) {
            addCriterion("business_address <=", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressLike(String value) {
            addCriterion("business_address like", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressNotLike(String value) {
            addCriterion("business_address not like", value, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressIn(List<String> values) {
            addCriterion("business_address in", values, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressNotIn(List<String> values) {
            addCriterion("business_address not in", values, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressBetween(String value1, String value2) {
            addCriterion("business_address between", value1, value2, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessAddressNotBetween(String value1, String value2) {
            addCriterion("business_address not between", value1, value2, "businessAddress");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneIsNull() {
            addCriterion("business_phone is null");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneIsNotNull() {
            addCriterion("business_phone is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneEqualTo(String value) {
            addCriterion("business_phone =", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneNotEqualTo(String value) {
            addCriterion("business_phone <>", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneGreaterThan(String value) {
            addCriterion("business_phone >", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("business_phone >=", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneLessThan(String value) {
            addCriterion("business_phone <", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneLessThanOrEqualTo(String value) {
            addCriterion("business_phone <=", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneLike(String value) {
            addCriterion("business_phone like", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneNotLike(String value) {
            addCriterion("business_phone not like", value, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneIn(List<String> values) {
            addCriterion("business_phone in", values, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneNotIn(List<String> values) {
            addCriterion("business_phone not in", values, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneBetween(String value1, String value2) {
            addCriterion("business_phone between", value1, value2, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBusinessPhoneNotBetween(String value1, String value2) {
            addCriterion("business_phone not between", value1, value2, "businessPhone");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNull() {
            addCriterion("bank_name is null");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNotNull() {
            addCriterion("bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andBankNameEqualTo(String value) {
            addCriterion("bank_name =", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotEqualTo(String value) {
            addCriterion("bank_name <>", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThan(String value) {
            addCriterion("bank_name >", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("bank_name >=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThan(String value) {
            addCriterion("bank_name <", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanOrEqualTo(String value) {
            addCriterion("bank_name <=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLike(String value) {
            addCriterion("bank_name like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotLike(String value) {
            addCriterion("bank_name not like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameIn(List<String> values) {
            addCriterion("bank_name in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotIn(List<String> values) {
            addCriterion("bank_name not in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameBetween(String value1, String value2) {
            addCriterion("bank_name between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotBetween(String value1, String value2) {
            addCriterion("bank_name not between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankAccountIsNull() {
            addCriterion("bank_account is null");
            return (Criteria) this;
        }

        public Criteria andBankAccountIsNotNull() {
            addCriterion("bank_account is not null");
            return (Criteria) this;
        }

        public Criteria andBankAccountEqualTo(String value) {
            addCriterion("bank_account =", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountNotEqualTo(String value) {
            addCriterion("bank_account <>", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountGreaterThan(String value) {
            addCriterion("bank_account >", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountGreaterThanOrEqualTo(String value) {
            addCriterion("bank_account >=", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountLessThan(String value) {
            addCriterion("bank_account <", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountLessThanOrEqualTo(String value) {
            addCriterion("bank_account <=", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountLike(String value) {
            addCriterion("bank_account like", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountNotLike(String value) {
            addCriterion("bank_account not like", value, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountIn(List<String> values) {
            addCriterion("bank_account in", values, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountNotIn(List<String> values) {
            addCriterion("bank_account not in", values, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountBetween(String value1, String value2) {
            addCriterion("bank_account between", value1, value2, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andBankAccountNotBetween(String value1, String value2) {
            addCriterion("bank_account not between", value1, value2, "bankAccount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIsNull() {
            addCriterion("apply_time is null");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIsNotNull() {
            addCriterion("apply_time is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTimeEqualTo(Date value) {
            addCriterion("apply_time =", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotEqualTo(Date value) {
            addCriterion("apply_time <>", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeGreaterThan(Date value) {
            addCriterion("apply_time >", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("apply_time >=", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeLessThan(Date value) {
            addCriterion("apply_time <", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeLessThanOrEqualTo(Date value) {
            addCriterion("apply_time <=", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIn(List<Date> values) {
            addCriterion("apply_time in", values, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotIn(List<Date> values) {
            addCriterion("apply_time not in", values, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeBetween(Date value1, Date value2) {
            addCriterion("apply_time between", value1, value2, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotBetween(Date value1, Date value2) {
            addCriterion("apply_time not between", value1, value2, "applyTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNull() {
            addCriterion("finish_time is null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNotNull() {
            addCriterion("finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeEqualTo(Date value) {
            addCriterion("finish_time =", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotEqualTo(Date value) {
            addCriterion("finish_time <>", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThan(Date value) {
            addCriterion("finish_time >", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("finish_time >=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThan(Date value) {
            addCriterion("finish_time <", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("finish_time <=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIn(List<Date> values) {
            addCriterion("finish_time in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotIn(List<Date> values) {
            addCriterion("finish_time not in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeBetween(Date value1, Date value2) {
            addCriterion("finish_time between", value1, value2, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("finish_time not between", value1, value2, "finishTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoIsNull() {
            addCriterion("invoice_no is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoIsNotNull() {
            addCriterion("invoice_no is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoEqualTo(String value) {
            addCriterion("invoice_no =", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInnerInvoiceNoEqualTo(String value) {
            addCriterion("inner_invoice_no =", value, "innerInvoiceNo");
            return (Criteria) this;
        }

        public Criteria andNegativeInvoiceNoEqualTo(String value) {
            addCriterion("negative_invoice_no =", value, "negativeInvoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoNotEqualTo(String value) {
            addCriterion("invoice_no <>", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoGreaterThan(String value) {
            addCriterion("invoice_no >", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_no >=", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoLessThan(String value) {
            addCriterion("invoice_no <", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoLessThanOrEqualTo(String value) {
            addCriterion("invoice_no <=", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoLike(String value) {
            addCriterion("invoice_no like", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoNotLike(String value) {
            addCriterion("invoice_no not like", value, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoIn(List<String> values) {
            addCriterion("invoice_no in", values, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoNotIn(List<String> values) {
            addCriterion("invoice_no not in", values, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoBetween(String value1, String value2) {
            addCriterion("invoice_no between", value1, value2, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andInvoiceNoNotBetween(String value1, String value2) {
            addCriterion("invoice_no not between", value1, value2, "invoiceNo");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxIsNull() {
            addCriterion("amount_no_tax is null");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxIsNotNull() {
            addCriterion("amount_no_tax is not null");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxEqualTo(BigDecimal value) {
            addCriterion("amount_no_tax =", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxNotEqualTo(BigDecimal value) {
            addCriterion("amount_no_tax <>", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxGreaterThan(BigDecimal value) {
            addCriterion("amount_no_tax >", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_no_tax >=", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxLessThan(BigDecimal value) {
            addCriterion("amount_no_tax <", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_no_tax <=", value, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxIn(List<BigDecimal> values) {
            addCriterion("amount_no_tax in", values, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxNotIn(List<BigDecimal> values) {
            addCriterion("amount_no_tax not in", values, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_no_tax between", value1, value2, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andAmountNoTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_no_tax not between", value1, value2, "amountNoTax");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIsNull() {
            addCriterion("tax_amount is null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIsNotNull() {
            addCriterion("tax_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTaxAmountEqualTo(BigDecimal value) {
            addCriterion("tax_amount =", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotEqualTo(BigDecimal value) {
            addCriterion("tax_amount <>", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountGreaterThan(BigDecimal value) {
            addCriterion("tax_amount >", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_amount >=", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountLessThan(BigDecimal value) {
            addCriterion("tax_amount <", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_amount <=", value, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountIn(List<BigDecimal> values) {
            addCriterion("tax_amount in", values, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotIn(List<BigDecimal> values) {
            addCriterion("tax_amount not in", values, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_amount between", value1, value2, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andTaxAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_amount not between", value1, value2, "taxAmount");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxIsNull() {
            addCriterion("amount_with_tax is null");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxIsNotNull() {
            addCriterion("amount_with_tax is not null");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxEqualTo(BigDecimal value) {
            addCriterion("amount_with_tax =", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxNotEqualTo(BigDecimal value) {
            addCriterion("amount_with_tax <>", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxGreaterThan(BigDecimal value) {
            addCriterion("amount_with_tax >", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_with_tax >=", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxLessThan(BigDecimal value) {
            addCriterion("amount_with_tax <", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount_with_tax <=", value, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxIn(List<BigDecimal> values) {
            addCriterion("amount_with_tax in", values, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxNotIn(List<BigDecimal> values) {
            addCriterion("amount_with_tax not in", values, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_with_tax between", value1, value2, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andAmountWithTaxNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount_with_tax not between", value1, value2, "amountWithTax");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformIsNull() {
            addCriterion("invoice_task_third_platform is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformIsNotNull() {
            addCriterion("invoice_task_third_platform is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformEqualTo(String value) {
            addCriterion("invoice_task_third_platform =", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformNotEqualTo(String value) {
            addCriterion("invoice_task_third_platform <>", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformGreaterThan(String value) {
            addCriterion("invoice_task_third_platform >", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_task_third_platform >=", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformLessThan(String value) {
            addCriterion("invoice_task_third_platform <", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformLessThanOrEqualTo(String value) {
            addCriterion("invoice_task_third_platform <=", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformLike(String value) {
            addCriterion("invoice_task_third_platform like", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformNotLike(String value) {
            addCriterion("invoice_task_third_platform not like", value, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformIn(List<String> values) {
            addCriterion("invoice_task_third_platform in", values, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformNotIn(List<String> values) {
            addCriterion("invoice_task_third_platform not in", values, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformBetween(String value1, String value2) {
            addCriterion("invoice_task_third_platform between", value1, value2, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdPlatformNotBetween(String value1, String value2) {
            addCriterion("invoice_task_third_platform not between", value1, value2, "invoiceTaskThirdPlatform");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdIsNull() {
            addCriterion("invoice_task_third_req_id is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdIsNotNull() {
            addCriterion("invoice_task_third_req_id is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdEqualTo(String value) {
            addCriterion("invoice_task_third_req_id =", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdNotEqualTo(String value) {
            addCriterion("invoice_task_third_req_id <>", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdGreaterThan(String value) {
            addCriterion("invoice_task_third_req_id >", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_task_third_req_id >=", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdLessThan(String value) {
            addCriterion("invoice_task_third_req_id <", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdLessThanOrEqualTo(String value) {
            addCriterion("invoice_task_third_req_id <=", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdLike(String value) {
            addCriterion("invoice_task_third_req_id like", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdNotLike(String value) {
            addCriterion("invoice_task_third_req_id not like", value, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdIn(List<String> values) {
            addCriterion("invoice_task_third_req_id in", values, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdNotIn(List<String> values) {
            addCriterion("invoice_task_third_req_id not in", values, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdBetween(String value1, String value2) {
            addCriterion("invoice_task_third_req_id between", value1, value2, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskThirdReqIdNotBetween(String value1, String value2) {
            addCriterion("invoice_task_third_req_id not between", value1, value2, "invoiceTaskThirdReqId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailIsNull() {
            addCriterion("invoice_task_detail is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailIsNotNull() {
            addCriterion("invoice_task_detail is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailEqualTo(String value) {
            addCriterion("invoice_task_detail =", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailNotEqualTo(String value) {
            addCriterion("invoice_task_detail <>", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailGreaterThan(String value) {
            addCriterion("invoice_task_detail >", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_task_detail >=", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailLessThan(String value) {
            addCriterion("invoice_task_detail <", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailLessThanOrEqualTo(String value) {
            addCriterion("invoice_task_detail <=", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailLike(String value) {
            addCriterion("invoice_task_detail like", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailNotLike(String value) {
            addCriterion("invoice_task_detail not like", value, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailIn(List<String> values) {
            addCriterion("invoice_task_detail in", values, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailNotIn(List<String> values) {
            addCriterion("invoice_task_detail not in", values, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailBetween(String value1, String value2) {
            addCriterion("invoice_task_detail between", value1, value2, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceTaskDetailNotBetween(String value1, String value2) {
            addCriterion("invoice_task_detail not between", value1, value2, "invoiceTaskDetail");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlIsNull() {
            addCriterion("invoice_download_url is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlIsNotNull() {
            addCriterion("invoice_download_url is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlEqualTo(String value) {
            addCriterion("invoice_download_url =", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlNotEqualTo(String value) {
            addCriterion("invoice_download_url <>", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlGreaterThan(String value) {
            addCriterion("invoice_download_url >", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_download_url >=", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlLessThan(String value) {
            addCriterion("invoice_download_url <", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlLessThanOrEqualTo(String value) {
            addCriterion("invoice_download_url <=", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlLike(String value) {
            addCriterion("invoice_download_url like", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlNotLike(String value) {
            addCriterion("invoice_download_url not like", value, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlIn(List<String> values) {
            addCriterion("invoice_download_url in", values, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlNotIn(List<String> values) {
            addCriterion("invoice_download_url not in", values, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlBetween(String value1, String value2) {
            addCriterion("invoice_download_url between", value1, value2, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andInvoiceDownloadUrlNotBetween(String value1, String value2) {
            addCriterion("invoice_download_url not between", value1, value2, "invoiceDownloadUrl");
            return (Criteria) this;
        }

        public Criteria andMemoIsNull() {
            addCriterion("memo is null");
            return (Criteria) this;
        }

        public Criteria andMemoIsNotNull() {
            addCriterion("memo is not null");
            return (Criteria) this;
        }

        public Criteria andMemoEqualTo(String value) {
            addCriterion("memo =", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotEqualTo(String value) {
            addCriterion("memo <>", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThan(String value) {
            addCriterion("memo >", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThanOrEqualTo(String value) {
            addCriterion("memo >=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThan(String value) {
            addCriterion("memo <", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThanOrEqualTo(String value) {
            addCriterion("memo <=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLike(String value) {
            addCriterion("memo like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotLike(String value) {
            addCriterion("memo not like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoIn(List<String> values) {
            addCriterion("memo in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotIn(List<String> values) {
            addCriterion("memo not in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoBetween(String value1, String value2) {
            addCriterion("memo between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotBetween(String value1, String value2) {
            addCriterion("memo not between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(InvoiceInfoDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(InvoiceInfoDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}