package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserOrganizationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserOrganizationDAO {
    int deleteByPrimaryKey(Integer id);

    int deleteByOrgId(Integer orgId);

    int insert(UserOrganizationDO record);

    UserOrganizationDO selectByPrimaryKey(Integer id);

    List<UserOrganizationDO> selectAll();

    void deleteByUserId(Integer userId);

    int updateByPrimaryKey(UserOrganizationDO record);

    List<UserOrganizationDO> selectByOrgIds(@Param("orgIds") List<Integer> orgIds);

    List<UserOrganizationDO> selectByUserIds(@Param("userIds") List<Integer> userIdList);

}