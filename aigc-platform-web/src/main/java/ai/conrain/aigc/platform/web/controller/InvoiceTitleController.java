package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.InvoiceTitleService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.InvoiceTitleQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * InvoiceTitle控制器
 *
 * <AUTHOR>
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/invoiceTitle")
public class InvoiceTitleController {

	/** invoiceTitleService */
	@Autowired
	private InvoiceTitleService invoiceTitleService;
	
	@GetMapping("/getById/{id}")
	public Result<InvoiceTitleVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(invoiceTitleService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody InvoiceTitleVO invoiceTitle){
		try {
			InvoiceTitleVO data = invoiceTitleService.insert(invoiceTitle);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加发票抬头失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建发票抬头失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		invoiceTitleService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody InvoiceTitleVO invoiceTitle){
		invoiceTitleService.updateByIdSelective(invoiceTitle);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<InvoiceTitleVO>> queryInvoiceTitleList(@Valid @RequestBody InvoiceTitleQuery query){
		return Result.success(invoiceTitleService.queryInvoiceTitleList(query));
	}

	@PostMapping("/queryMerchantInvoiceTitle")
	public Result<InvoiceTitleVO> queryMerchantInvoiceTitle(){
		InvoiceTitleQuery titleQuery = new InvoiceTitleQuery();
		titleQuery.setMasterUserId(OperationContextHolder.getMasterUserId());
		titleQuery.setOrderBy("id desc");

		List<InvoiceTitleVO> list = invoiceTitleService.queryInvoiceTitleList(titleQuery);
		if (CollectionUtils.isNotEmpty(list)) {
			return Result.success(list.get(0));
		} else {
			return Result.success();
		}
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<InvoiceTitleVO>> getInvoiceTitleByPage(@Valid @RequestBody InvoiceTitleQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(invoiceTitleService.queryInvoiceTitleByPage(query));
	}
}
