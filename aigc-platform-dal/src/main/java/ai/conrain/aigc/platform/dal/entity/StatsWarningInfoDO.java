package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 对应数据表：stats_warning_info
 */
@Data
public class StatsWarningInfoDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * 周内不消耗客户
     */
    private String weeklyNoConsumptionRate;

    /**
     * 月内不消耗客户
     */
    private String monthlyNoConsumptionRate;

    /**
     * 用户退款率大于百分之 x的客户数量
     */
    private Integer customerRefundRateCount;

    /**
     * 交付超过 20 小时的服装量
     */
    private Integer deliveryTimeoutCount;

    /**
     * 客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%）
     */
    private Integer customerBalanceAlertCount;

    /**
     * 客户入库时间超过 60 天未转化数量
     */
    private Integer customerNotConvertCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}