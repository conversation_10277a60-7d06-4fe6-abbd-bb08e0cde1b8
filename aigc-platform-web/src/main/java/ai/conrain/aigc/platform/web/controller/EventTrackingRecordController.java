package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.vo.EventTrackingRecordVO;
import ai.conrain.aigc.platform.service.component.EventTrackingRecordService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.web.helper.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * EventTrackingRecord控制器
 *
 * <AUTHOR>
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/eventTrackingRecord")
public class EventTrackingRecordController {

    /** eventTrackingRecordService */
    @Autowired
    private EventTrackingRecordService eventTrackingRecordService;

    @GetMapping("/getById/{id}")
    public Result<EventTrackingRecordVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(eventTrackingRecordService.selectById(id));
    }

    /**
     * 埋点记录（对埋点记录进行处理存储缓存中）
     *
     * @param eventTrackingRecord 埋点记录信息
     */
    @PostMapping("/record")
    public Result<?> record(@Valid @RequestBody EventTrackingRecordVO eventTrackingRecord) {
        try {
            // 获取上下文
            OperationContext context = OperationContextHolder.getContext();
            // 设置 sessionId
            eventTrackingRecord.setSessionId(context.getOperationSession().getSessionId());
            // 设置 ip 地址
            eventTrackingRecord.setIpAddress(IpUtils.handleIp(context.getIp()));

            // 记录埋点信息
            eventTrackingRecordService.record(eventTrackingRecord);
        } catch (Exception e) {
            log.error("埋点失败：", e);
        }

        // 埋点信息缓存无论成功还是失败均返回成功
        return Result.success();
    }

    /**
     * 新增
     *
     * @param eventTrackingRecord 埋点记录信息
     */
    @PostMapping("/create")
    public Result<?> create(@Valid @RequestBody EventTrackingRecordVO eventTrackingRecord) {
        try {
            // 获取上下文
            OperationContext context = OperationContextHolder.getContext();
            // 设置 sessionId
            eventTrackingRecord.setSessionId(context.getOperationSession().getSessionId());
            // 设置 ip 地址
            eventTrackingRecord.setIpAddress(IpUtils.handleIp(context.getIp()));

            eventTrackingRecordService.insert(eventTrackingRecord);
        } catch (Exception e) {
            log.error("添加失败：", e);
        }

        // 埋点信息添加无论成功还是失败均返回成功
        return Result.success();
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        eventTrackingRecordService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateById(@Valid @RequestBody EventTrackingRecordVO eventTrackingRecord) {
        try {
            // 获取上下文
            OperationContext context = OperationContextHolder.getContext();
            // 设置 sessionId
            eventTrackingRecord.setSessionId(context.getOperationSession().getSessionId());
            // 设置 ip 地址
            eventTrackingRecord.setIpAddress(IpUtils.handleIp(context.getIp()));

            eventTrackingRecordService.updateById(eventTrackingRecord);
        } catch (Exception e) {
            log.error("更新失败：", e);
        }

        // 埋点信息更新无论成功还是失败均返回成功
        return Result.success();
    }

}
