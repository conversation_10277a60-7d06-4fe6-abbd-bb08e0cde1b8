package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * comfyui模板激活版本表
 * 对应数据表：comfyui_workflow_template_active_version
 */
@Data
public class ComfyuiWorkflowTemplateActiveVersionDO implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 模板key
     */
    private String templateKey;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 模板版本，如20250610.1
     */
    private String activeVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建人id
     */
    private Integer createBy;

    /**
     * 修改人id
     */
    private Integer modifyBy;

    /**
     * 测试模板版本，如20250610.1
     */
    private String testVersion;

    /**
     * 测试模板开放范围
     */
    private String testOpenScope;

    private static final long serialVersionUID = 1L;
}