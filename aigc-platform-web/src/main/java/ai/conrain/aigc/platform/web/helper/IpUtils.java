/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.helper;

import jakarta.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * ip工具类
 *
 * <AUTHOR>
 * @version : IpUtils.java, v 0.1 2024/6/4 16:42 renxiao.wu Exp $
 */
public abstract class IpUtils {

    // 匹配 IPv4 地址的正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");

    // 内网IP范围
    private static final String[] PRIVATE_IP_BLOCKS = {"^10\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$", // 10.0.0.0/8
                                                       "^172\\.(1[6-9]|2[0-9]|3[0-1])\\.\\d{1,3}\\.\\d{1,3}$",
                                                       // **********/12
                                                       "^192\\.168\\.\\d{1,3}\\.\\d{1,3}$" // ***********/16
    };

    /**
     * 获取真实ip地址
     *
     * @param request http请求
     * @return ip地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(ip) || StringUtils.equalsIgnoreCase(ip, "unknown")) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || StringUtils.equalsIgnoreCase(ip, "unknown")) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || StringUtils.equalsIgnoreCase(ip, "unknown")) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 处理 ip 地址，将0:0:0:0:0:0:0:1 转换为本地
     */
    public static String handleIp(String ip) {
        if (ip.equals("0:0:0:0:0:0:0:1")) {
            return "127.0.0.1";
        }
        return ip;
    }

    /**
     * 对IP列表进行排序：
     * - 内网IP随机排序后排在前面
     * - 公网IP随机排序后排在后面
     */
    public List<String> sortIpList(List<String> ipList) {
        if (ipList == null || ipList.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> privateIps = new ArrayList<>();
        List<String> publicIps = new ArrayList<>();

        for (String ip : ipList) {
            if (isPrivateIp(ip)) {
                privateIps.add(ip);
            } else if (isValidIp(ip)) {
                publicIps.add(ip);
            }
        }

        // 随机打乱顺序
        Collections.shuffle(privateIps);
        Collections.shuffle(publicIps);

        // 合并：内网IP在前，公网IP在后
        List<String> sortedList = new ArrayList<>(privateIps);
        sortedList.addAll(publicIps);

        return sortedList;
    }

    /**
     * 判断是否为有效的IPv4地址
     */
    public static boolean isValidIp(String ip) {
        return IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isPrivateIp(String ip) {
        for (String pattern : PRIVATE_IP_BLOCKS) {
            if (Pattern.matches(pattern, ip)) {
                return true;
            }
        }
        return false;
    }
}
