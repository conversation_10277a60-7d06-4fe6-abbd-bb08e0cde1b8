package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO;
import ai.conrain.aigc.platform.dal.example.PrincipalInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PrincipalInfoDAO {
    long countByExample(PrincipalInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PrincipalInfoDO record);

    int insertSelective(PrincipalInfoDO record);

    List<PrincipalInfoDO> selectByExampleWithBLOBs(PrincipalInfoExample example);

    List<PrincipalInfoDO> selectByExample(PrincipalInfoExample example);

    PrincipalInfoDO selectByPrimaryKey(Integer id);

    PrincipalInfoDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") PrincipalInfoDO record, @Param("example") PrincipalInfoExample example);

    int updateByExample(@Param("record") PrincipalInfoDO record, @Param("example") PrincipalInfoExample example);

    int updateByPrimaryKeySelective(PrincipalInfoDO record);

    int updateByPrimaryKey(PrincipalInfoDO record);

    int logicalDeleteByExample(@Param("example") PrincipalInfoExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    int insertOrUpdate(PrincipalInfoDO record);

    List<PrincipalInfoDO> selectCommissionRelatePrincipal (Integer principalId);
}