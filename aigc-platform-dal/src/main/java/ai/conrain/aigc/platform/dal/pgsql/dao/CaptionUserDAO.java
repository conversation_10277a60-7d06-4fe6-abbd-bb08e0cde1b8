package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.CaptionUserExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionUserDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CaptionUserDAO {
    long countByExample(CaptionUserExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CaptionUserDO record);

    int insertSelective(CaptionUserDO record);

    List<CaptionUserDO> selectByExample(CaptionUserExample example);

    CaptionUserDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CaptionUserDO record, @Param("example") CaptionUserExample example);

    int updateByExample(@Param("record") CaptionUserDO record, @Param("example") CaptionUserExample example);

    int updateByPrimaryKeySelective(CaptionUserDO record);

    int updateByPrimaryKey(CaptionUserDO record);
}