package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.biz.DistributorEnhancedVO;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import ai.conrain.aigc.platform.service.model.query.AssessmentPlanQuery;
import ai.conrain.aigc.platform.service.component.AssessmentPlanService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * AssessmentPlan控制器
 *
 * <AUTHOR>
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/assessmentPlan")
public class AssessmentPlanController {

	/** assessmentPlanService */
	@Autowired
	private AssessmentPlanService assessmentPlanService;
	
	@GetMapping("/getById/{id}")
	public Result<AssessmentPlanVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(assessmentPlanService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody AssessmentPlanVO assessmentPlan){
		try {
			AssessmentPlanVO data = assessmentPlanService.insert(assessmentPlan);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加销售考核计划失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建销售考核计划失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		assessmentPlanService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody AssessmentPlanVO assessmentPlan){
		assessmentPlanService.updateByIdSelective(assessmentPlan);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<AssessmentPlanVO>> queryAssessmentPlanList(@Valid @RequestBody AssessmentPlanQuery query){
		return Result.success(assessmentPlanService.queryAssessmentPlanList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<AssessmentPlanVO>> getAssessmentPlanByPage(@Valid @RequestBody AssessmentPlanQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(assessmentPlanService.queryAssessmentPlanByPage(query));
	}

	@PostMapping("/modifyAssessmentPlan")
	public Result<?> modifyAssessmentPlan(@Valid @RequestBody AssessmentPlanVO assessmentPlan) {
		// 参数检查
		AssertUtil.assertNotNull(assessmentPlan, ResultCode.PARAM_INVALID, "[修改考核计划]考核计划不能为空");

		assessmentPlanService.modifyAssessmentPlan(assessmentPlan);
		return Result.success();
	}

	@GetMapping("/getAllDistributorAssessment")
	public Result<List<DistributorEnhancedVO>> getAllDistributorAssessment(@RequestParam(value = "showTest", required = false, defaultValue = "false") Boolean showTest) {
		return Result.success(assessmentPlanService.queryAllDistributorAssessment(showTest));
	}

	@PostMapping("/reviewAssessmentPlan")
	public Result<?> reviewAssessmentPlan(@RequestBody JSONObject request) {
		if (request == null || request.isEmpty() || !request.containsKey("planId")) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "请求参数不完整");
		}
		Integer planId = request.getInteger("planId");
		AssertUtil.assertNotNull(planId, ResultCode.PARAM_INVALID, "考核计划ID不能为空");
		SettleConfigModel newSettleConfig = request.getObject("newSettleConfig", SettleConfigModel.class);

		assessmentPlanService.reviewAssessmentPlan(planId, newSettleConfig);
		return Result.success();
	}

	@GetMapping("/manualRefresh")
	public Result<Boolean> manualRefresh() {
		assessmentPlanService.processAssessment();
		return Result.success(true);
	}
}
