/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * 异步上下文传递工具类
 * 用于在异步线程中传递traceId、env和登录态上下文
 */
public class AsyncContextHelper {

    /**
     * 上下文信息封装类
     */
    public static class ContextInfo {
        private final String traceId;
        private final String env;
        private final OperationContext operationContext;

        public ContextInfo(String traceId, String env, OperationContext operationContext) {
            this.traceId = traceId;
            this.env = env;
            this.operationContext = operationContext;
        }

        public String getTraceId() {
            return traceId;
        }

        public String getEnv() {
            return env;
        }

        public OperationContext getOperationContext() {
            return operationContext;
        }
    }

    /**
     * 从当前线程捕获上下文信息
     * 
     * @return 上下文信息
     */
    public static ContextInfo captureContext() {
        String traceId = MDC.get("traceId");
        String env = StringUtils.upperCase(EnvUtil.getEnv());
        OperationContext operationContext = OperationContextHolder.getContext();
        
        return new ContextInfo(traceId, env, operationContext);
    }

    /**
     * 在异步线程中设置上下文信息
     * 
     * @param contextInfo 上下文信息
     */
    public static void setContext(ContextInfo contextInfo) {
        if (contextInfo == null) {
            return;
        }
        
        if (StringUtils.isNotBlank(contextInfo.getTraceId())) {
            MDC.put("traceId", contextInfo.getTraceId());
        }
        
        if (StringUtils.isNotBlank(contextInfo.getEnv())) {
            MDC.put("env", contextInfo.getEnv());
        }
        
        if (contextInfo.getOperationContext() != null) {
            OperationContextHolder.setContext(contextInfo.getOperationContext());
        }
    }

    /**
     * 清理异步线程中的上下文信息
     */
    public static void clearContext() {
        OperationContextHolder.clean();
        MDC.remove("env");
        MDC.remove("traceId");
    }

    /**
     * 包装Supplier，自动处理上下文传递
     * 
     * @param supplier 原始Supplier
     * @param <T> 返回类型
     * @return 包装后的Supplier
     */
    public static <T> Supplier<T> wrapSupplier(Supplier<T> supplier) {
        ContextInfo contextInfo = captureContext();
        
        return () -> {
            try {
                setContext(contextInfo);
                return supplier.get();
            } finally {
                clearContext();
            }
        };
    }

    /**
     * 包装Callable，自动处理上下文传递
     * 
     * @param callable 原始Callable
     * @param <T> 返回类型
     * @return 包装后的Callable
     */
    public static <T> Callable<T> wrapCallable(Callable<T> callable) {
        ContextInfo contextInfo = captureContext();
        
        return () -> {
            try {
                setContext(contextInfo);
                return callable.call();
            } finally {
                clearContext();
            }
        };
    }

    /**
     * 包装Runnable，自动处理上下文传递
     * 
     * @param runnable 原始Runnable
     * @return 包装后的Runnable
     */
    public static Runnable wrapRunnable(Runnable runnable) {
        ContextInfo contextInfo = captureContext();
        
        return () -> {
            try {
                setContext(contextInfo);
                runnable.run();
            } finally {
                clearContext();
            }
        };
    }

    /**
     * 执行带上下文的异步任务
     * 
     * @param supplier 异步任务
     * @param <T> 返回类型
     * @return 执行结果
     */
    public static <T> T executeWithContext(Supplier<T> supplier) {
        return wrapSupplier(supplier).get();
    }

    /**
     * 执行带上下文的异步任务
     * 
     * @param callable 异步任务
     * @param <T> 返回类型
     * @return 执行结果
     * @throws Exception 执行异常
     */
    public static <T> T executeWithContext(Callable<T> callable) throws Exception {
        return wrapCallable(callable).call();
    }

    /**
     * 执行带上下文的异步任务
     * 
     * @param runnable 异步任务
     */
    public static void executeWithContext(Runnable runnable) {
        wrapRunnable(runnable).run();
    }
}