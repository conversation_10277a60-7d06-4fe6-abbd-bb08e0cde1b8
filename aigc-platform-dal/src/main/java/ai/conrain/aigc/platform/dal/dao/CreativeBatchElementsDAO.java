package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CreativeBatchElementsDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchElementsExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CreativeBatchElementsDAO {
    long countByExample(CreativeBatchElementsExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CreativeBatchElementsDO record);

    int insertSelective(CreativeBatchElementsDO record);

    List<CreativeBatchElementsDO> selectByExample(CreativeBatchElementsExample example);

    CreativeBatchElementsDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CreativeBatchElementsDO record, @Param("example") CreativeBatchElementsExample example);

    int updateByExample(@Param("record") CreativeBatchElementsDO record, @Param("example") CreativeBatchElementsExample example);

    int updateByPrimaryKeySelective(CreativeBatchElementsDO record);

    int updateByPrimaryKey(CreativeBatchElementsDO record);

    void batchInsert(List<CreativeBatchElementsDO> list);

    List<CreativeBatchElementsDO> selectRecentElementsByExample(CreativeBatchElementsExample example);

    List<CreativeBatchElementsDO> selectRecentFaceLoraById(Integer userId);

    List<Integer> queryHasShowImageChildrenIds(@Param("idList") List<Integer> idList);
}