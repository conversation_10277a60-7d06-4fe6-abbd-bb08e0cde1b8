package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.StatsWarningInfoDO;
import ai.conrain.aigc.platform.service.model.vo.StatsWarningInfoVO;
import ai.conrain.aigc.platform.service.model.query.StatsWarningInfoQuery;
import ai.conrain.aigc.platform.service.component.StatsWarningInfoService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsWarningInfo控制器
 *
 * <AUTHOR>
 * @version StatsWarningInfoService.java v 0.1 2025-05-19 04:36:33
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsWarningInfo")
public class StatsWarningInfoController {

	/** statsWarningInfoService */
	@Autowired
	private StatsWarningInfoService statsWarningInfoService;
	
	@GetMapping("/getById/{id}")
	public Result<StatsWarningInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsWarningInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody StatsWarningInfoVO statsWarningInfo){
		try {
			StatsWarningInfoVO data = statsWarningInfoService.insert(statsWarningInfo);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsWarningInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsWarningInfoVO statsWarningInfo){
		statsWarningInfoService.updateByIdSelective(statsWarningInfo);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<StatsWarningInfoVO>> queryStatsWarningInfoList(@Valid @RequestBody StatsWarningInfoQuery query){
		return Result.success(statsWarningInfoService.queryStatsWarningInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<StatsWarningInfoVO>> getStatsWarningInfoByPage(@Valid @RequestBody StatsWarningInfoQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsWarningInfoService.queryStatsWarningInfoByPage(query));
	}


	@GetMapping("/getWarningInfo/{warningId}")
	public Result<StatsWarningInfoVO> getWarningInfo(@PathVariable Integer warningId){
		if (warningId == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsWarningInfoService.getWarningInfo(warningId));
	}
}
