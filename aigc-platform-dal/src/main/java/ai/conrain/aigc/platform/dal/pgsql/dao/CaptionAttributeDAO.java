package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.CaptionAttributeExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionAttributeDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CaptionAttributeDAO {
    long countByExample(CaptionAttributeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CaptionAttributeDO record);

    int insertSelective(CaptionAttributeDO record);

    List<CaptionAttributeDO> selectByExample(CaptionAttributeExample example);

    CaptionAttributeDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CaptionAttributeDO record, @Param("example") CaptionAttributeExample example);

    int updateByExample(@Param("record") CaptionAttributeDO record, @Param("example") CaptionAttributeExample example);

    int updateByPrimaryKeySelective(CaptionAttributeDO record);

    int updateByPrimaryKey(CaptionAttributeDO record);
}