package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单结算表（流水）
 * 对应数据表：order_settlement
 */
@Data
public class OrderSettlementDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单结算类型
     */
    private String type;

    /**
     * 渠道商实体id
     */
    private Integer distributorCorpId;

    /**
     * 渠道商实体名称
     */
    private String distributorCorpName;

    /**
     * 结算主体类型
     */
    private String principalType;

    /**
     * 结算主体 id
     */
    private Integer principalId;

    /**
     * 状态，0初始化、1订单关闭、2待结算、3结算中、4结算成功
     */
    private Integer status;

    /**
     * 结算id
     */
    private String settleId;

    /**
     * 结算完成时间
     */
    private Date settleTime;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 渠道费率
     */
    private BigDecimal channelRate;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    //---------------------view columns------------------------//

    /**
     * 订单商家的master user id
     */
    private Integer merchantId;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 订单商家的corp id
     */
    private Integer merchantCorpId;

    /**
     * 商家公司的名称
     */
    private String merchantCorpName;

    /**
     * 订单完成时间
     */
    private Date orderFinishTime;

    private static final long serialVersionUID = 1L;
}