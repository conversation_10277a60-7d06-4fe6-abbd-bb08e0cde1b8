package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票记录
 * 对应数据表：invoice_info
 */
@Data
public class InvoiceInfoDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 关联的主用户id
     */
    private Integer masterUserId;

    /**
     * 关联的主用户昵称快照
     */
    private String masterUserNick;

    /**
     * 关联的主用户登录账号快照
     */
    private String masterUserLoginId;

    /**
     * 关联的操作员用户id
     */
    private Integer operatorUserId;

    /**
     * 关联的操作员用户昵称快照
     */
    private String operatorUserNick;

    /**
     * 关联的操作员用户登录账号快照
     */
    private String operatorUserLoginId;

    /**
     * 发票类型，普票｜专票
     */
    private String invoiceType;

    /**
     * 发票抬头类型，个人｜企业
     */
    private String subjectType;

    /**
     * 发票抬头
     */
    private String subjectName;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 办公地址
     */
    private String businessAddress;

    /**
     * 办公电话
     */
    private String businessPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 发票状态，未开票｜开票中｜已开票
     */
    private String status;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 发票号（税务局正式发票号码）
     */
    private String invoiceNo;

    /**
     * 不含税发票金额
     */
    private BigDecimal amountNoTax;

    /**
     * 税率，小数形式
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 含税发票金额
     */
    private BigDecimal amountWithTax;

    /**
     * 外部发票平台名称
     */
    private String invoiceTaskThirdPlatform;

    /**
     * 外部发票任务id
     */
    private String invoiceTaskThirdReqId;

    /**
     * 发票任务详情
     */
    private String invoiceTaskDetail;

    /**
     * 发票文件下载地址
     */
    private String invoiceDownloadUrl;

    /**
     * 备注
     */
    private String memo;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    //内部发票号码
    private String innerInvoiceNo;

    //税务局红冲票号码
    private String negativeInvoiceNo;

    //红冲发票详情
    private String negativeInvoiceDetail;


    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}