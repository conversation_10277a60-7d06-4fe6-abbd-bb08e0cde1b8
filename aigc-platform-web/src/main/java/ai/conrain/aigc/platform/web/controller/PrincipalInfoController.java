package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;
import ai.conrain.aigc.platform.service.model.query.PrincipalInfoQuery;
import ai.conrain.aigc.platform.service.component.PrincipalInfoService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * PrincipalInfo控制器
 *
 * <AUTHOR>
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/principalInfo")
public class PrincipalInfoController {

	/** principalInfoService */
	@Autowired
	private PrincipalInfoService principalInfoService;
	
	@GetMapping("/getById/{id}")
	public Result<PrincipalInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(principalInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody PrincipalInfoVO principalInfo){
		try {
			PrincipalInfoVO data = principalInfoService.insert(principalInfo);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加通用主体属性失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建通用主体属性失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		principalInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody PrincipalInfoVO principalInfo){
		principalInfoService.updateByIdSelective(principalInfo);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<PrincipalInfoVO>> queryPrincipalInfoList(@Valid @RequestBody PrincipalInfoQuery query){
		return Result.success(principalInfoService.queryPrincipalInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<PrincipalInfoVO>> getPrincipalInfoByPage(@Valid @RequestBody PrincipalInfoQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(principalInfoService.queryPrincipalInfoByPage(query));
	}

	@PostMapping("/modifyContractDate")
	public Result<Boolean> modifyContractDate(@RequestBody @Valid JSONObject req) {
		Integer userId = req.getInteger("userId");
		String contractDate = req.getString("contractDate");

		AssertUtil.assertTrue(userId != null && StringUtils.isNotBlank(contractDate), ResultCode.PARAM_INVALID, "参数错误");
		principalInfoService.modifyContractDate(userId, contractDate);
		return Result.success();
	}
}
