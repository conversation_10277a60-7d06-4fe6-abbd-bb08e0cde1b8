/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 打标用户类型
 */
@Getter
@AllArgsConstructor
public enum CaptionUserRoleEnum {
    ADMIN("admin", "管理员"),

    ANNOTATOR("annotator", "标注员"),

    LLM("llm", "大模型"),

    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static CaptionUserRoleEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (CaptionUserRoleEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

}
