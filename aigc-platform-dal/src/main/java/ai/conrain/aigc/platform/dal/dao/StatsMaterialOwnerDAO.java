package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsMaterialOwnerDO;
import ai.conrain.aigc.platform.dal.example.StatsMaterialOwnerExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsMaterialOwnerDAO {
    long countByExample(StatsMaterialOwnerExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsMaterialOwnerDO record);

    int insertSelective(StatsMaterialOwnerDO record);

    List<StatsMaterialOwnerDO> selectByExampleWithBLOBs(StatsMaterialOwnerExample example);

    List<StatsMaterialOwnerDO> selectByExample(StatsMaterialOwnerExample example);

    StatsMaterialOwnerDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsMaterialOwnerDO record, @Param("example") StatsMaterialOwnerExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsMaterialOwnerDO record, @Param("example") StatsMaterialOwnerExample example);

    int updateByExample(@Param("record") StatsMaterialOwnerDO record, @Param("example") StatsMaterialOwnerExample example);

    int updateByPrimaryKeySelective(StatsMaterialOwnerDO record);

    int updateByPrimaryKeyWithBLOBs(StatsMaterialOwnerDO record);

    int updateByPrimaryKey(StatsMaterialOwnerDO record);

    /**
     * 批量插入或更新服装负责人
     * @param statsMaterialOwnerDOList 服装负责人列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<StatsMaterialOwnerDO> statsMaterialOwnerDOList);

    /**
     * 根据日期和统计类型查询服装负责人信息
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param statsType 统计类型
     * @return 服装负责人列表
     */
    List<StatsMaterialOwnerDO> selectStatsInfoByDateAndPeriod(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("statsType") String statsType);

    /**
     * 根据用户 id 进行分组分页查询服装负责人信息
     * @param pageSize 每页大小
     * @param offset offset
     * @param userId 用户 id
     * @param nickname 昵称
     * @return 服装负责人列表
     */
    @MapKey("userId")
    List<Map<String, Object>> selectPageStatsMaterialOwner(@Param("pageSize") Integer pageSize, @Param("offset") Integer offset, @Param("userId") Integer userId, @Param("nickname") String nickname);

    /**
     * 统计符合条件的用户总数
     * @param userId 用户 id
     * @param nickname 昵称
     * @return 用户总数
     */
    Long countPageStatsMaterialOwner(@Param("userId") Integer userId, @Param("nickname") String nickname);


    /**
     * 根据用户 id 查询所有统计数据
     * @param userId 用户 id
     * @return 统计数据列表
     */
    List<StatsMaterialOwnerDO> selectAllByUserId(@Param("userId") Integer userId);
}