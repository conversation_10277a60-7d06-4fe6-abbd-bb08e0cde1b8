/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.dispatch.DispatchServiceFactory;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.component.dispatch.TaskDispatch;
import ai.conrain.aigc.platform.service.constants.EventConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper.WeakType;
import ai.conrain.aigc.platform.service.model.event.CreativeTriggerEvent;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * 创作任务状态同步定时任务
 *
 * <AUTHOR>
 * @version : CreativeStatusSyncJob.java, v 0.1 2024/5/10 18:22 renxiao.wu Exp $
 */
@Slf4j
@Component
@EnableScheduling
public class CreativeStatusSyncJob {
    private static final String KEY_LOCK = "_sync_batch_job_lock_";
    private static final int LOCK_EXPIRE_TIME = 30 * 1000;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private TaskDispatch creativeTaskDispatch;
    @Autowired
    private TairService tairService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Autowired
    private WeakLockHelper weakLockHelper;
    @Autowired
    private DispatchServiceFactory dispatchServiceFactory;
    @Autowired
    private CreativeTaskService creativeTaskService;

    @Scheduled(cron = "0,10,20,30,40,50 * * * * *")
    public void process() {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        boolean lock = tairService.acquireLock(KEY_LOCK, LOCK_EXPIRE_TIME);
        if (!lock) {
            log.info("【prompt状态同步调度任务】，未抢占到调度锁，直接跳过");
            return;
        }

        long start = System.currentTimeMillis();
        List<CreativeBatchVO> list = null;

        try {
            //再加一个轻量的锁，占用5秒钟，防止多机器并发
            if (!weakLockHelper.lock(WeakType.BATCH_JOB, 999)) {
                log.info("【prompt状态同步调度任务】,未抢占到轻量调度锁，直接跳过");
                return;
            }

            log.info("【prompt状态同步调度任务】,抢占到调度锁，开始执行调度");

            // 获取所有未处理的 出图批次列表
            List<CreativeBatchVO> creativeBatchVOList = queryBatchListByDispatchType(DispatchTypeEnum.CREATIVE_TASK);
            // 获取所有未处理的 抠图批次列表
            List<CreativeBatchVO> pictureMattingList = queryBatchListByDispatchType(DispatchTypeEnum.PICTURE_MATTING);
            // 获取所有未处理的外部调用批次列表
            List<CreativeBatchVO> externalBatchList = queryExternalBatchList();

            list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(creativeBatchVOList)) {
                list.addAll(creativeBatchVOList);
            }
            if (CollectionUtils.isNotEmpty(pictureMattingList)) {
                list.addAll(pictureMattingList);
            }
            if (CollectionUtils.isNotEmpty(externalBatchList)) {
                list.addAll(externalBatchList);
            }

            if (CollectionUtils.isEmpty(list)) {
                log.info("【prompt状态同步调度任务】执行结束: 当前待处理列表为空，运行结束");
                return;
            }

            for (CreativeBatchVO each : list) {
                CreativeTriggerEvent triggerEvent = new CreativeTriggerEvent();
                triggerEvent.setId(each.getId());
                String serverUrl = each.getExtValue(KEY_SERVER_URL, String.class);

                //本地直接开始调度
                if (EnvUtil.isLocalEnv()) {
                    creativeBatchService.syncStatus(each);
                } else {
                    //服务器上通过事件进行分发
                    rocketMQTemplate.send(EventConstants.TOPIC_CREATIVE_TRIGGER,
                        MessageBuilder.withPayload(triggerEvent).build());
                }

                log.info("【prompt状态同步调度任务】发送触发事件，batchId={},serverUrl={}", each.getId(), serverUrl);
            }

        } catch (Exception e) {
            log.error("【prompt状态同步调度任务】执行异常", e);
        } finally {
            log.info("【prompt状态同步调度任务】执行结束,耗时={}ms,共{}条数据", System.currentTimeMillis() - start,
                CollectionUtils.size(list));

            MDC.remove("env");
            MDC.remove("traceId");

            //释放锁
            tairService.releaseLock(KEY_LOCK);
        }
    }

    /**
     * 查询外部调用批次列表
     *
     * @return 外部调用批次列表
     */
    private List<CreativeBatchVO> queryExternalBatchList() {
        List<CreativeBatchVO> externalBatchList = creativeBatchService.queryUnCompletedExternal();
        if (CollectionUtils.isNotEmpty(externalBatchList)) {
            log.info("【prompt状态同步调度任务】查询到的未处理的外部调用批次数={}", externalBatchList.size());
        } else {
            log.info("【prompt状态同步调度任务】当前无未处理的外部调用任务");
        }
        return externalBatchList;
    }

    /**
     * 查询所有未处理的的批次列表
     *
     * @return 批处理列表
     */
    private List<CreativeBatchVO> queryBatchListByDispatchType(DispatchTypeEnum dispatchTypeEnum) {

        List<PipelineVO> list = dispatchServiceFactory.queryIdleServer(dispatchTypeEnum);
        if (CollectionUtils.isEmpty(list)) {
            log.info("【{}】【prompt状态同步调度任务】查询可用管道数,当前没有可用管道，跳过当前调度",
                dispatchTypeEnum.getCode());
            return null;
        }
        log.info("【{}】【prompt状态同步调度任务】查询可用管道数,当前可用管道数pipelineNum={}", dispatchTypeEnum.getCode(),
            list.size());

        List<CreativeBatchVO> result = new ArrayList<>();

        for (PipelineVO pipeline : list) {
            List<ServerVO> idleServers = pipeline.getIdleServers();
            int size = CollectionUtils.size(idleServers);

            // 获取需要排除的类型
            List<String> exceptTypeList = new ArrayList<>();
            // 获取需要包含的类型
            List<String> includeTypeList = new ArrayList<>();

            // 若为创作类型(排除 抠图 相关任务)
            if (dispatchTypeEnum.equals(DispatchTypeEnum.CREATIVE_TASK)) {
                exceptTypeList = CreativeTypeEnum.getPictureMattingTypeList();
            }

            // 若为抠图类型 (查询 抠图 相关任务)
            if (dispatchTypeEnum.equals(DispatchTypeEnum.PICTURE_MATTING)) {
                includeTypeList = CreativeTypeEnum.getPictureMattingTypeList();
            }

            List<CreativeBatchVO> listResult = creativeBatchService.queryUnCompletedTopUser(pipeline.getId(),
                exceptTypeList, includeTypeList, size + 3); //3个buffer

            // 进行非空判断
            if (CollectionUtils.isEmpty(listResult)) {
                log.warn("【{}】【prompt状态同步调度任务】,当前无处理中状态的任务，跳过当前管道,pipelineId={}",
                    dispatchTypeEnum.getCode(), pipeline.getId());
                continue;
            }

            List<ServerVO> remainServers = idleServers != null ? new CopyOnWriteArrayList<>(idleServers)
                : new CopyOnWriteArrayList<>();
            for (CreativeBatchVO data : listResult) {
                if (CollectionUtils.isEmpty(remainServers)) {
                    dispatchAndAdd(data, null, remainServers, result);
                    continue;
                }

                for (ServerVO server : remainServers) {
                    // 如果server的类型与data的类型匹配，则进行调度
                    boolean dispatched = dispatchAndAdd(data, server, remainServers, result);

                    // 如果调度成功，则跳出循环
                    if (dispatched) {
                        break;
                    }
                }
            }

            log.info(
                "【{}】【prompt状态同步调度任务】当前管道[{}]，查询到的未处理的批次数={}，命中服务后的批次数为={}，预期的空闲服务器数为={}",
                dispatchTypeEnum.getCode(), pipeline.getName(), CollectionUtils.size(listResult),
                CollectionUtils.size(result), CollectionUtils.size(pipeline.getIdleServers()));
        }

        return result;
    }

    /**
     * 路由分发并加入到结果集中
     *
     * @param data          批次数据
     * @param server        服务
     * @param remainServers 剩余可用的服务列表
     * @param result        结果集
     */
    private boolean dispatchAndAdd(CreativeBatchVO data, ServerVO server, List<ServerVO> remainServers,
                                   List<CreativeBatchVO> result) {
        //判断是否命中当前server，如果未命中，则继续遍历
        DispatchTypeEnum dispatchTypeEnum = DispatchTypeEnum.getByServerType(data.getType());
        String dispatch = dispatchServiceFactory.dispatch(dispatchTypeEnum, data, server);

        log.info("【{}】【prompt状态同步调度任务】当前批次[{}]，命中服务结果={}", dispatchTypeEnum.getCode(), data.getId(),
            dispatch);

        if (StringUtils.isBlank(dispatch)) {
            return false;
        }

        //命中情况下，更新状态
        data.setStatus(CreativeStatusEnum.PROCESSING);
        creativeBatchService.updateByIdSelective(data);

        if (CollectionUtils.isNotEmpty(remainServers) && StringUtils.equals(dispatch,
            serverHelper.getServerUrl(server))) {
            remainServers.remove(server);
        }

        //只有命中服务的，才进行调度
        result.add(data);

        return true;
    }

}
