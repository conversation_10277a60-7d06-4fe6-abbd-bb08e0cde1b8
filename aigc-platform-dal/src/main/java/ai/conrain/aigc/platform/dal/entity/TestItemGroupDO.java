package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * AB测试项目分组表
 * 对应数据表：test_item_group
 */
@Data
public class TestItemGroupDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 测试计划id
     */
    private Integer planId;

    /**
     * 测试项目id
     */
    private Integer itemId;

    /**
     * 类型，TRAIN、CREATIVE
     */
    private String type;

    /**
     * 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED
     */
    private String status;

    /** 分组类型 */
    private String groupType;

    /**
     * 轮数
     */
    private Integer roundsNum;

    /** 创作批次id */
    private Integer batchId;

    /**
     * 正向数量
     */
    private Integer positiveNum;

    /**
     * 负向数量
     */
    private Integer negativeNum;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 对照参数信息
     */
    private String comparisonParams;

    /** 扩展信息 */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}