package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 图像分析结果模型
 * 简化版的ImageAnalysisCaptionModel
 */
@Data
public class ImageAnalysisResult {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态
     */
    private String status;

    /**
     * source
     */
    @JSONField(name = "source")
    private String source;

    /**
     * image_path
     */
    @JSONField(name = "image_path")
    private String imagePath;

    /**
     * 分析结果数据
     */
    @JSONField(name = "analysis")
    private ImageAnalysisCaption analysis;

    /**
     * 错误消息（如果有）
     */
    private String errorMessage;

    /**
     * 分析结果原始数据
     */
    private String raw;
}