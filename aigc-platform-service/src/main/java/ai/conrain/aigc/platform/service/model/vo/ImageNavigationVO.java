package ai.conrain.aigc.platform.service.model.vo;

import java.io.Serializable;
import lombok.Data;

/**
 * ImageNavigationVO
 */
@Data
public class ImageNavigationVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 图像ID，自增主键 */
	private Integer id;

	/** 图像类型，用于区分服装图、风格示意图 */
	private String type;


    private Integer prevId;

    private Integer nextId;
}
