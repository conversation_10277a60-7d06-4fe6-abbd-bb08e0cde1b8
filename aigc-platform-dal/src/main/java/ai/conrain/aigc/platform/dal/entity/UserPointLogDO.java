package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户算力流水表
 * 对应数据表：user_point_log
 */
@Data
public class UserPointLogDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 类型，充值单、服装建模、套餐外创、创作退回作等
     */
    private String type;

    /**
     * 关联id
     */
    private Integer relatedId;

    /**
     * 算力点
     */
    private Integer point;

    /**
     * 赠送点数
     */
    private Integer givePoint;

    /**
     * 体验算力点
     */
    private Integer experiencePoint;

    /**
     * 服装套餐内算力点
     */
    private Integer modelPoint;

    /**
     * 操作id
     */
    private Integer operatorId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 扩展信息
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}