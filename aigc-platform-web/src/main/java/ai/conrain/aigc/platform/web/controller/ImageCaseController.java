package ai.conrain.aigc.platform.web.controller;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.model.biz.BadCaseTag;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ImageCase控制器
 *
 * <AUTHOR>
 * @version ImageCaseService.java v 0.1 2024-12-09 06:41:17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageCase")
public class ImageCaseController {

    /** imageCaseService */
    @Autowired
    private ImageCaseService imageCaseService;

    @GetMapping("/getById/{id}")
    public Result<ImageCaseVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(imageCaseService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody ImageCaseVO imageCase) {
        try {
            ImageCaseVO data = imageCaseService.insert(imageCase);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加图片案例失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建图片案例失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        imageCaseService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateById(@Valid @RequestBody ImageCaseVO imageCase) {
        imageCaseService.updateByIdSelective(imageCase);
        return Result.success();
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<ImageCaseVO>> getImageCaseByPage(@Valid @RequestBody ImageCaseQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("a.id desc");
        }

        return Result.success(imageCaseService.queryImageCaseByPage(query));
    }

    @PostMapping("/badCaseTags")
    public Result<List<BadCaseTag>> queryBadCaseInfo(@JsonArg @NotBlank String imageUrl) {
        return Result.success(imageCaseService.queryBadCaseInfo(imageUrl));
    }

    /**
     * 添加badCase标签
     *
     * @param imageUrl 图片地址
     * @param taskId   taskId
     * @param tagId    tagId
     * @param isAdd    是否为添加
     * @param id       主键 id 【非必填】
     * @param type     操作类型（具体与哪张表做关联处理）【非必填】
     */
    @PostMapping("/addBadCaseTag")
    public Result<?> addBadCaseTag(@JsonArg @NotBlank String imageUrl, @JsonArg @NotNull Integer taskId,
                                   @JsonArg @NotNull Integer tagId, @JsonArg @NotNull Boolean isAdd,
                                   @JsonArg Integer id, @JsonArg String type) {
        imageCaseService.addBadCaseTag(imageUrl, taskId, tagId, isAdd, id, type);

        return Result.success();
    }
}
