package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionVO;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionQuery;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ImageGroupCaption控制器
 *
 * <AUTHOR>
 * @version ImageGroupCaptionService.java v 0.1 2025-08-14 11:09:51
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/imageGroupCaption")
public class ImageGroupCaptionController {

	/** imageGroupCaptionService */
	@Autowired
	private ImageGroupCaptionService imageGroupCaptionService;
	
	@GetMapping("/getById/{id}")
	public Result<ImageGroupCaptionVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageGroupCaptionService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageGroupCaptionVO imageGroupCaption){
		return Result.success(imageGroupCaptionService.insert(imageGroupCaption).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageGroupCaptionService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageGroupCaptionVO imageGroupCaption){
		imageGroupCaptionService.updateByIdSelective(imageGroupCaption);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<ImageGroupCaptionVO>> queryImageGroupCaptionList(@Valid @RequestBody ImageGroupCaptionQuery query){
		return Result.success(imageGroupCaptionService.queryImageGroupCaptionList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageGroupCaptionVO>> getImageGroupCaptionByPage(@Valid @RequestBody ImageGroupCaptionQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageGroupCaptionService.queryImageGroupCaptionByPage(query));
	}

	/**
	 * 聚合标注结果
	 */
	@PostMapping("/aggregateResults")
	public Result<?> aggregateResults(@JsonArg List<Integer> userIds, @JsonArg Boolean forceUpdate) {
		if (userIds == null || userIds.size() != 3) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户ID列表必须包含3个用户");
		}

		boolean force = forceUpdate != null && forceUpdate;

        imageGroupCaptionService.aggregateCaptionResult(userIds, force);
        return Result.success("聚合处理完成");
	}

	/**
	 * 处理指定图片组的标注聚合（测试用）
	 */
	@PostMapping("/processSpecific")
	public Result<?> processSpecificImageGroup(@JsonArg Integer imageGroupId, @JsonArg List<Integer> userIds) {
		if (imageGroupId == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "图片组ID不能为空");
		}
		if (userIds == null || userIds.size() != 3) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户ID列表必须包含3个用户");
		}

        imageGroupCaptionService.processSpecificImageGroupCaption(imageGroupId, userIds);
        return Result.success("处理完成");
	}

	/**
	 * 查询需要更新的图片组数量
	 */
	@PostMapping("/countForUpdate")
	public Result<Long> countForUpdate(@JsonArg List<Integer> userIds, @JsonArg Boolean forceUpdate) {
		if (userIds == null || userIds.size() != 3) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户ID列表必须包含3个用户");
		}

		boolean force = forceUpdate != null && forceUpdate;

		try {
			Long count = imageGroupCaptionService.countImageGroupsForUpdate(userIds, force);
			return Result.success(count);
		} catch (Exception e) {
			log.error("查询待更新图片组数量失败", e);
			return Result.failedWithMessage(ResultCode.BIZ_FAIL, "查询失败: " + e.getMessage());
		}
	}
}