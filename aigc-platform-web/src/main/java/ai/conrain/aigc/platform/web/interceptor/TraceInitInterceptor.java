package ai.conrain.aigc.platform.web.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
public class TraceInitInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        response.addHeader("traceId", MDC.get("traceId"));
        if (!StringUtils.endsWith(request.getServletPath(), "/error")) {
            log.info("服务端接收到请求:{}", request.getServletPath());
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
        throws Exception {
        MDC.remove("env");
        MDC.remove("traceId");
    }
}