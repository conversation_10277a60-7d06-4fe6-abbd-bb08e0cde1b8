package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO;
import ai.conrain.aigc.platform.dal.example.StatsUserOperateExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;

@Mapper
public interface StatsUserOperateDAO {
    long countByExample(StatsUserOperateExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsUserOperateDO record);

    int insertSelective(StatsUserOperateDO record);

    List<StatsUserOperateDO> selectByExampleWithBLOBs(StatsUserOperateExample example);

    List<StatsUserOperateDO> selectByExample(StatsUserOperateExample example);

    StatsUserOperateDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsUserOperateDO record, @Param("example") StatsUserOperateExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsUserOperateDO record, @Param("example") StatsUserOperateExample example);

    int updateByExample(@Param("record") StatsUserOperateDO record, @Param("example") StatsUserOperateExample example);

    int updateByPrimaryKeySelective(StatsUserOperateDO record);

    int updateByPrimaryKeyWithBLOBs(StatsUserOperateDO record);

    int updateByPrimaryKey(StatsUserOperateDO record);

    /**
     * 批量插入或更新   
     * @param statsDOList 数据
     * @return 新增/修改条数
     */
    int batchInsertOrUpdate(List<StatsUserOperateDO> statsDOList);

    /**
     * 查询用户使用了多少套服装
     * @param userIdList 用户id列表
     * @return 用户使用了多少套服装
     */
    Integer selectModelCount(@Param("userIdList") List<Integer> userIdList);

    /**
     * 查询用户累计出图多少次
     * @param userIdList 用户id列表
     * @return 用户累计出图多少次
     */
    Integer selectCreativeCount(@Param("userIdList") List<Integer> userIdList);

    /**
     * 查询用户使用最多的5套服装
     * @param userIdList 用户id列表
     * @param size 数量
     * @return 用户使用最多的5套服装
     */
    @MapKey("materialId")
    List<Map<String, Object>> selectTopMaterialsByUsage(@Param("userIdList") List<Integer> userIdList, @Param("size") int size);

    /**
     * 查询用户累计出图量（图片数量）
     * @param userIdList 用户id列表
     * @return 用户累计出图量（图片数量）
     */
    Integer selectImageCount(@Param("userIdList") List<Integer> userIdList);

    /**
     * 查询用户累计下载量（图片数量）
     * @param userIdList 用户id列表
     * @return 用户累计下载量（图片数量）
     */
    Integer selectDownloadCount(@Param("userIdList") List<Integer> userIdList);

    /**
     * 查询用户出图下载数量信息
     * @param userIdList 用户id列表
     * @return 用户出图下载数量信息
     */
    @MapKey("userId")
    List<Map<String, Object>> selectUserOperateData(@Param("userIdList") List<Integer> userIdList);
}