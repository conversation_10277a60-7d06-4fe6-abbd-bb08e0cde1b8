package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 组织
 * 对应数据表：organization
 */
@Data
public class OrganizationDO implements Serializable {
    /**
     * 组织ID
     */
    private Integer id;

    /**
     * 父级组织ID，根组织为0
     */
    private Integer parentId;

    /**
     * 是否根结点组织，0不是，1是
     */
    private Boolean root;

    /**
     * 根组织ID，根组织ID等于ID,根组织有且只有一个
     */
    private Integer rootId;

    /**
     * 是否叶子结点组织，0不是，1是
     */
    private Boolean leaf;

    /**
     * 组织类型，DISTRIBUTOR_CORP：渠道商企业｜DISTRIBUTOR_DEPT：渠道商部门
     */
    private String orgType;

    /**
     * 组织名称
     */
    private String name;

    /**
     * 组织标签，作为组织类型的补充，预留
     */
    private String tags;

    /**
     * 组织层级，根组织为0，根组织下有1级组织，1级组织下有2级组织，以此类推
     */
    private Integer orgLevel;

    /**
     * 创建者主账号id
     */
    private Integer creatorMasterUserId;

    /**
     * 创建人角色类型，DISTRIBUTOR：渠道商
     */
    private String creatorUserRoleType;

    /**
     * 创建人操作员账号id
     */
    private Integer creatorOperatorUserId;

    /**
     * 最近修改人的操作员账号id
     */
    private Integer modifierOperatorUserId;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    // ========= 额外字段 ========

    /**
     * 组织主账号 id
     */
    private Integer channelAdminId;

    /**
     * 组织主账号昵称
     */
    private String channelAdminNickName;



    private static final long serialVersionUID = 1L;
}