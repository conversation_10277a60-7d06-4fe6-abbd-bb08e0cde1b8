package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.CreativeTaskDO;
import ai.conrain.aigc.platform.dal.example.CreativeTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CreativeTaskDAO {
    long countByExample(CreativeTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CreativeTaskDO record);

    int insertSelective(CreativeTaskDO record);

    List<CreativeTaskDO> selectByExampleWithBLOBs(CreativeTaskExample example);

    List<CreativeTaskDO> selectByExample(CreativeTaskExample example);

    CreativeTaskDO selectByPrimaryKey(Integer id);

    CreativeTaskDO selectFullByPrimaryKey(Integer id);

    CreativeTaskDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") CreativeTaskDO record, @Param("example") CreativeTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") CreativeTaskDO record, @Param("example") CreativeTaskExample example);

    int updateByExample(@Param("record") CreativeTaskDO record, @Param("example") CreativeTaskExample example);

    int updateByPrimaryKeySelective(CreativeTaskDO record);

    int updateByPrimaryKeyWithBLOBs(CreativeTaskDO record);

    int updateByPrimaryKey(CreativeTaskDO record);

    int logicalDeleteByExample(@Param("example") CreativeTaskExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    List<CreativeTaskDO> selectByElements(List<Integer> elementIds, Boolean testFlag, Integer userId, Integer limit);

    int batchLogicalDeleteByPrimaryKeys(List<Integer> ids);

    void batchInsert(List<CreativeTaskDO> list);
}