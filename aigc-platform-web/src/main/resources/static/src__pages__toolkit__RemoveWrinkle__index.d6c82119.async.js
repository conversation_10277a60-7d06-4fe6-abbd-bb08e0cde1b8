"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[60],{97095:function(Pe,O,e){e.r(O);var Z=e(90228),m=e.n(Z),F=e(87999),P=e.n(F),$=e(48305),r=e.n($),z=e(95006),d=e(47624),G=e(93251),H=e(26817),w=e(29852),a=e(75271),g=e(87715),J=e(72684),Q=e(55057),X=e(90984),Y=e(9991),q=e(68632),ee=e(24573),t=e(52676),te=function(ae){var _e=ae.useSliderValue,ne=(0,a.useState)(""),I=r()(ne,2),o=I[0],S=I[1],se=(0,a.useState)(!1),M=r()(se,2),h=M[0],f=M[1],re=(0,a.useState)(null),D=r()(re,2),Ie=D[0],le=D[1],oe=(0,a.useState)(null),C=r()(oe,2),l=C[0],ue=C[1],ie=(0,a.useState)(!1),R=r()(ie,2),Se=R[0],T=R[1],me=(0,a.useState)(),U=r()(me,2),u=U[0],W=U[1],B=(0,a.useRef)(null),ce=(0,a.useRef)(null),de=(0,a.useState)(!1),K=r()(de,2),L=K[0],j=K[1],fe=(0,a.useState)(!1),A=r()(fe,2),c=A[0],p=A[1],pe=(0,a.useState)("upload"),y=r()(pe,2),k=y[0],N=y[1],V=r()(_e,2),x=V[0],Ee=V[1];(0,a.useEffect)(function(){var n=localStorage.getItem("imageUrl");n&&(S(n),N("history"));var s=localStorage.getItem("userInfo");if(s){var _=JSON.parse(s);le(_)}return b(),function(){localStorage.removeItem("imageUrl"),localStorage.removeItem("modelId")}},[]),(0,a.useEffect)(function(){j(!!o)},[o]),(0,a.useEffect)(function(){var n=null;return u&&u.id&&u.status!=="FINISHED"&&(n=setInterval(P()(m()().mark(function s(){var _;return m()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,(0,g.DI)(u.id);case 2:_=i.sent,_&&(W(_),_.status==="FINISHED"&&(clearInterval(n),T(!1)));case 4:case"end":return i.stop()}},s)})),2e3)),function(){n&&clearInterval(n)}},[u]);function b(){return E.apply(this,arguments)}function E(){return E=P()(m()().mark(function n(){return m()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:(0,Q.m8)("REMOVE_WRINKLE",1).then(function(v){v&&ue(v)});case 1:case"end":return _.stop()}},n)})),E.apply(this,arguments)}var ve=function(){if(!o){d.ZP.error("\u8BF7\u5148\u4E0A\u4F20\u56FE\u7247");return}if(k==="upload"&&l&&l.needTopup){f(!0);return}c||(p(!0),(0,g.QX)({originImage:o}).then(function(s){s&&(d.ZP.success("\u63D0\u4EA4\u6210\u529F"),setTimeout(function(){var _;T(!0),W(s),(_=B.current)===null||_===void 0||_.refresh()},0)),p(!1)}).catch(function(s){d.ZP.error(s.message||"\u56FE\u7247\u5904\u7406\u5931\u8D25"),p(!1)}))},Oe=function(s){S(s)};return(0,t.jsxs)(z._z,{children:[(0,t.jsxs)(G.Z,{className:"toolkit-row-container",children:[(0,t.jsxs)("div",{className:"toolkit-work-block",children:[(0,t.jsx)(q.Z,{className:"redraw-image-upload",title:"\u5F85\u4FEE\u590D\u56FE\u7247",image:o,setImageSource:N,onImageChange:Oe,historyFree:!0,uploadCost:.4}),(0,t.jsxs)("div",{className:"toolkit-number-input-container",children:[(0,t.jsx)("div",{className:"text16 font-pf color-n weight",children:"\u751F\u6210\u6570\u91CF"}),(0,t.jsx)(H.Z,{value:1,disabled:!0})]})]}),(0,t.jsx)("div",{ref:ce,className:"toolkit-output-block",style:{width:"calc( 58% * ".concat(x/12,")"),maxWidth:"100%"},children:(0,t.jsx)(Y.default,{sliderValue:x,changeSliderValue:Ee,types:["REMOVE_WRINKLE"],ref:B,pollingTimeout:2e3})})]}),(0,t.jsx)("footer",{className:"toolkit-footer",children:(0,t.jsxs)("div",{className:"toolkit-footer-content",children:[L&&k==="upload"&&(0,t.jsx)(J.Z,{creativeType:"REMOVE_WRINKLE",predictVO:l}),(0,t.jsxs)(w.ZP,{type:"primary",className:"create-btn",disabled:!L||c||(l==null?void 0:l.needTopup),icon:c?(0,t.jsx)(ee.Z,{style:{fontSize:16,color:"fff"}}):"",onClick:ve,children:[" ",l!=null&&l.needTopup?"\u4F59\u989D\u4E0D\u8DB3\uFF0C\u53BB\u5145\u503C":c?"\u751F\u6210\u4E2D":"\u5F00\u59CB\u53BB\u76B1"]})]})}),h&&(0,t.jsx)(X.default,{visible:h,onClose:function(){return f(!1)},onPaySuccess:function(){f(!1),b()}})]})};O.default=te}}]);
