package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TestItemGroupDO;
import ai.conrain.aigc.platform.dal.example.TestItemGroupExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestItemGroupDAO {
    long countByExample(TestItemGroupExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TestItemGroupDO record);

    int insertSelective(TestItemGroupDO record);

    List<TestItemGroupDO> selectByExampleWithBLOBs(TestItemGroupExample example);

    List<TestItemGroupDO> selectByExample(TestItemGroupExample example);

    TestItemGroupDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TestItemGroupDO record, @Param("example") TestItemGroupExample example);

    int updateByExampleWithBLOBs(@Param("record") TestItemGroupDO record, @Param("example") TestItemGroupExample example);

    int updateByExample(@Param("record") TestItemGroupDO record, @Param("example") TestItemGroupExample example);

    int updateByPrimaryKeySelective(TestItemGroupDO record);

    int updateByPrimaryKeyWithBLOBs(TestItemGroupDO record);

    int updateByPrimaryKey(TestItemGroupDO record);

    void refreshScore(Integer id);

    long deleteByTestItemId(@Param("testItemId") Integer testItemId);
}