package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 发票抬头
 * 对应数据表：invoice_title
 */
@Data
public class InvoiceTitleDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 关联的主用户id
     */
    private Integer masterUserId;

    /**
     * 关联的操作员用户id
     */
    private Integer operatorUserId;

    /**
     * 发票类型，普票｜专票
     */
    private String invoiceType;

    /**
     * 发票抬头类型，个人｜企业
     */
    private String subjectType;

    /**
     * 发票抬头
     */
    private String subjectName;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 办公地址
     */
    private String businessAddress;

    /**
     * 办公电话
     */
    private String businessPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}