package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.TagsDO;
import ai.conrain.aigc.platform.service.model.vo.TagsVO;
import ai.conrain.aigc.platform.service.model.query.TagsQuery;
import ai.conrain.aigc.platform.service.component.TagsService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Tags控制器
 *
 * <AUTHOR>
 * @version TagsService.java v 0.1 2024-05-22 08:28:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/tags")
public class TagsController {

	/** tagsService */
	@Autowired
	private TagsService tagsService;
	
	@GetMapping("/getById/{id}")
	public Result<TagsVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(tagsService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TagsVO tags){
		try {
			TagsVO data = tagsService.insert(tags);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加标签配置失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建标签配置失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		tagsService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody TagsVO tags){
		tagsService.updateByIdSelective(tags);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<TagsVO>> queryTagsList(@Valid @RequestBody TagsQuery query){
		return Result.success(tagsService.queryTagsList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<TagsVO>> getTagsByPage(@Valid @RequestBody TagsQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(tagsService.queryTagsByPage(query));
	}
}
