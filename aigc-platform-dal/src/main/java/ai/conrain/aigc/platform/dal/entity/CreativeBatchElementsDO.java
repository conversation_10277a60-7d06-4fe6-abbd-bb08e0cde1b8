package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 创作批次属性表
 * 对应数据表：creative_batch_elements
 */
@Data
public class CreativeBatchElementsDO implements Serializable {
    /**
     * 批次id
     */
    private Integer id;

    /**
     * 模型id
     */
    private Integer batchId;

    /**
     * 元素id
     */
    private Integer elementId;

    /** 元素关键字 */
    private String elementKey;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /** 操作人id */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}