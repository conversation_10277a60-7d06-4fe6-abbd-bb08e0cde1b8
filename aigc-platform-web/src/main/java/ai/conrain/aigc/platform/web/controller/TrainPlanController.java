package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.TrainPlanService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.TrainPlanVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * TrainPlan控制器
 *
 * <AUTHOR>
 * @version TrainPlanService.java v 0.1 2024-11-19 01:52:26
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/trainPlan")
public class TrainPlanController {

	/** trainPlanService */
	@Autowired
	private TrainPlanService trainPlanService;

	@GetMapping("/getById/{id}")
	public Result<TrainPlanVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(trainPlanService.selectById(id));
	}

	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody TrainPlanVO trainPlan){
		try {
			trainPlan.setCreatorUserId(OperationContextHolder.getOperatorUserId());
			trainPlan.setCreatorUserName(OperationContextHolder.getOperatorNick());

			TrainPlanVO data = trainPlanService.createPlan(trainPlan);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		trainPlanService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody TrainPlanVO trainPlan){
		trainPlanService.updateById(trainPlan);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<TrainPlanVO>> findAll(){
		return Result.success(trainPlanService.findAll());
	}

}
