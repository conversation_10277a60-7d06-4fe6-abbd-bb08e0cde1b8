package ai.conrain.aigc.platform.integration.interceptor;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

@Slf4j
public class RestTemplateInterceptor implements ClientHttpRequestInterceptor {

    private static final int MAX_INFO_LENGTH = 2000;

    @Override
    public ClientHttpResponse intercept(HttpRequest httpRequest, byte[] bytes,
                                        ClientHttpRequestExecution clientHttpRequestExecution) throws IOException {
        handlerRequest(httpRequest);

        StringBuilder sb = new StringBuilder();
        recordRequest(httpRequest, bytes, sb);
        ClientHttpResponse response = clientHttpRequestExecution.execute(httpRequest, bytes);
        recordResponse(response, sb, httpRequest);

        log.info(sb.toString());
        return response;
    }

    private void handlerRequest(HttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    }

    private void recordRequest(HttpRequest httpRequest, byte[] bytes, StringBuilder sb) {
        String info = "-";
        if (!noneedPrint(httpRequest.getURI().getPath())) {
            String content = new String(bytes, StandardCharsets.UTF_8);
            info = content.length() > MAX_INFO_LENGTH ?
                content.substring(0, MAX_INFO_LENGTH) + "... (length=" + content.length() + ")" :
                content;
        }
        sb.append(String.format("REST REQUEST:%s,%s\n", httpRequest.getURI(), info));
    }

    public void recordResponse(ClientHttpResponse response, StringBuilder sb, HttpRequest httpRequest)
        throws IOException {

        StringBuilder inputStringBuilder = new StringBuilder();

        //小程序码接口，不打印返回日志，二进制图片
        if (!noneedPrint(httpRequest.getURI().getPath())) {

            BufferedReader bufferedReader = new BufferedReader(
                new InputStreamReader(response.getBody(), StandardCharsets.UTF_8));

            String line = bufferedReader.readLine();
            while (line != null) {
                inputStringBuilder.append(line).append("\n");
                line = bufferedReader.readLine();
            }
        } else {
            inputStringBuilder.append("-");
        }

        sb.append(String.format("REST RESPONSE:%s,%s,%s", response.getStatusCode(), response.getStatusText(),
            inputStringBuilder));
    }

    private boolean noneedPrint(String path) {
        return path.contains("download/output") || path.contains("image/input") || path.contains("upload/lora")
               || path.contains("/download/image") || path.contains("/queue") || path.contains("/download/file")
               || path.contains("/viewFiles") || path.contains("/prompt") || path.contains("/remove_bg")
               || path.contains("/klingai") || path.contains("/history") || path.contains("/lama_remover");
    }
}