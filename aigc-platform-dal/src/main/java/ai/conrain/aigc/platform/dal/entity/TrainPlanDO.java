package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应数据表：train_plan
 */
public class TrainPlanDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 训练计划名称
     */
    private String planName;

    /**
     * 原始克隆服装ID
     */
    private Integer clothingId;

    /**
     * 原始克隆服装名称
     */
    private String clothingName;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 每组图片数量
     */
    private Integer imagesPerCombination;

    /**
     * 模特 ID 列表，以逗号分隔
     */
    private String faceModels;

    /**
     * 场景 ID 列表，以逗号分隔
     */
    private String scenes;

    /**
     * 出图尺寸列表，以逗号分隔
     */
    private String sizes;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建者用户ID
     */
    private Integer creatorUserId;

    /**
     * 创建者用户名
     */
    private String creatorUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName == null ? null : planName.trim();
    }

    public Integer getClothingId() {
        return clothingId;
    }

    public void setClothingId(Integer clothingId) {
        this.clothingId = clothingId;
    }

    public String getClothingName() {
        return clothingName;
    }

    public void setClothingName(String clothingName) {
        this.clothingName = clothingName == null ? null : clothingName.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public Integer getImagesPerCombination() {
        return imagesPerCombination;
    }

    public void setImagesPerCombination(Integer imagesPerCombination) {
        this.imagesPerCombination = imagesPerCombination;
    }

    public String getFaceModels() {
        return faceModels;
    }

    public void setFaceModels(String faceModels) {
        this.faceModels = faceModels == null ? null : faceModels.trim();
    }

    public String getScenes() {
        return scenes;
    }

    public void setScenes(String scenes) {
        this.scenes = scenes == null ? null : scenes.trim();
    }

    public String getSizes() {
        return sizes;
    }

    public void setSizes(String sizes) {
        this.sizes = sizes == null ? null : sizes.trim();
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public Integer getCreatorUserId() {
        return creatorUserId;
    }

    public void setCreatorUserId(Integer creatorUserId) {
        this.creatorUserId = creatorUserId;
    }

    public String getCreatorUserName() {
        return creatorUserName;
    }

    public void setCreatorUserName(String creatorUserName) {
        this.creatorUserName = creatorUserName == null ? null : creatorUserName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}