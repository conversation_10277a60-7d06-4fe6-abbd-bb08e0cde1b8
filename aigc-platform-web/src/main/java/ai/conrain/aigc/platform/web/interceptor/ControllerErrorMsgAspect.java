/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.web.helper.MessageHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 控制器异常信息切面
 *
 * <AUTHOR>
 * @version : ControllerErrorMsgAspect.java, v 0.1 2023/9/25 11:42 renxiao.wu Exp $
 */
@Slf4j
@Aspect
@Component
public class ControllerErrorMsgAspect {
    @Autowired
    private MessageHelper messageHelper;

    @Around("execution(* ai.conrain.aigc.platform.web.controller.*Controller.*(..)) ")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object object = proceedingJoinPoint.proceed();
        if (!(object instanceof Result)) {
            return object;
        }

        Result<?> result = (Result<?>)object;

        if (result.isSuccess() || result.getCode() == ResultCode.SUCCESS) {
            return object;
        }

        try {
            result.setMessage(messageHelper.fetchErrorMsg(result.getCode(), result.getMessage()));
        } catch (Throwable e) {
            log.error("统一异常信息处理异常,不影响业务", e);
        }

        return object;
    }
}
