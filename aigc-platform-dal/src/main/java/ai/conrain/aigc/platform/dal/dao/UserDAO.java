package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserDO;
import ai.conrain.aigc.platform.dal.example.UserExample;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserDAO {
    long countByExample(UserExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(UserDO record);

    int insertSelective(UserDO record);

    List<UserDO> selectByExample(UserExample example);

    List<UserDO> queryAllVipOrPaidMasterUsers();

    UserDO selectByPrimaryKey(Integer id);

    UserDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") UserDO record, @Param("example") UserExample example);

    int updateByExample(@Param("record") UserDO record, @Param("example") UserExample example);

    int updateByPrimaryKeySelective(UserDO record);

    int updateByPrimaryKey(UserDO record);

    int logicalDeleteByPrimaryKey(Integer id);

    List<UserDO> queryAllMasterMetaInfo(List<String> roleTypes);

    List<UserDO> queryAllByRoleTypes(List<String> roleTypes);

    List<Integer> selectIdByExample(UserExample example);

    boolean isVipOrPaidMasterUser(Integer id);

    boolean isCustomer(Integer userId);

    /**
     * 高付费用户(大于 3999)
     *
     * @return 用户 id 列表
     */
    List<Integer> selectHighPayUser();

    /**
     * 查询用户是否在60天之内未充值过
     *
     * @return 用户数量
     */
    @MapKey("sales_id")
    List<Map<String, Object>> queryBefore60Days(String endDate);

    /**
     * 查询活跃的用户
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 用户列表
     */
    @MapKey("user_id")
    List<Map<String, Object>> queryActiveUser(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取3999以上的用户id列表
     *
     * @param userId 销售id
     * @return 用户id列表
     */
    List<Integer> fetch3999UserIdList(@Param("userId") Integer userId);

    /**
     * 查询60天之内未充值的用户
     *
     * @param endDate 结束日期
     * @return 用户列表
     */
    @MapKey("user_id")
    List<Map<String, Object>> queryBefore60DaysByOperate(@Param("endDate") String endDate);

    /**
     * 查询3999以上的用户
     *
     * @return 用户列表
     */
    List<UserDO> queryAll3999VIPOrPaidCustomer();


    /**
     * 查询3999以上的用户
     *
     * @return 用户列表
     */
    List<UserDO> queryAll3999VIPOrPaidCustomerWithCreateTime(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<UserDO> batchSelectChannelAdminByOrgId(List<Integer> orgIds);

    List<UserDO> findDirectChannelAdmin(Integer userId);

    List<UserDO> selectByOrgId(Integer orgId);

    UserDO selectUserNotVipByPrimaryKey(Integer userId);
}