package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 创作元素表
 * 对应数据表：creative_element
 */
@Data
public class CreativeElementDO implements Serializable {
    /**
     * 元素id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 配置关键字，同一级配置key一致，一级要求不一致,如FACE、SCENE
     */
    private String configKey;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 图片url
     */
    private String showImage;

    /** 状态 */
    private String status;

    /**
     * 排序
     */
    private Integer order;

    /**
     * 备注
     */
    private String memo;

    /**
     * 业务类型，居家生活、户外旅游、城市街拍等
     */
    private String type;

    /** 归属类型 */
    private String belong;

    /** 归属主账户 */
    private Integer userId;

    /** 操作员id */
    private Integer operatorId;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 标签列表
     */
    private String tags;

    /**
     * 扩展标签列表
     */
    private String extTags;

    /**
     * 是否为上新模特，1是，0否，默认为 0
     */
    private Boolean isNew;

    /**
     * 扩展信息
     */
    private String extInfo;

    private Integer loraModelId;

    private Integer privatelyOpen2UserId;
    private String privatelyOpen2UserNick;
    private String privatelyOpen2UserRoleType;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey == null ? null : configKey.trim();
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getShowImage() {
        return showImage;
    }

    public void setShowImage(String showImage) {
        this.showImage = showImage == null ? null : showImage.trim();
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public String getExtTags() {
        return extTags;
    }

    public void setExtTags(String extTags) {
        this.extTags = extTags == null ? null : extTags.trim();
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public Boolean getIsNew() {
        return isNew;
    }

    public void setIsNew(Boolean isNew) {
        this.isNew = isNew;
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}