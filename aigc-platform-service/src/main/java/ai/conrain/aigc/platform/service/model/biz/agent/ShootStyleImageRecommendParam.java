package ai.conrain.aigc.platform.service.model.biz.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ShootStyleImageRecommendParam implements Serializable {

    // 衣服图片信息
    private ClothImgItem clothImgItem;

    // 用户上传的拍摄风格参考图片
    private List<UserUploadStyleImg> userUploadStyleImgs;

    //排除当前对话所有已经返回的批次，用于完全重新出一批
    private List<String> excludeRetBatch;

    //指定的流派，用于搜索更多指定流派的图
    private List<String> specifiedGenres;

    //指定的风格参考图Id，用于基于单张图搜索更多类似
    private Integer specifiedRetImgId;

    // 是否启用流派并发查询模式
    private Boolean enableGenreConcurrency = true;

    // 风格相似度阈值
    private Double styleSimilarityThreshold = 0.5;

    // 匹配的图片数量
    private Integer matchImgCount = 2000;

    //mmr 参数
    private Double mmrLambda = 0.5;

    // mmr 窗口大小
    private Integer mmrWindowSize = 10;

    // 背景簇数量
    private Integer bgGroupCountLimit = 5;
    private Integer bgSzCountLimit = 3;
    private Integer bgSzShow = 2;
    private Integer minBgClusterSz = 5;
}
