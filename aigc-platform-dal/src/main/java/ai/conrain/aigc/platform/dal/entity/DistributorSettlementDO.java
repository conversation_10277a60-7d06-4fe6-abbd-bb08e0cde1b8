package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 渠道商结算明细表
 * 对应数据表：distributor_settlement
 */
@Data
public class DistributorSettlementDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 结算主体类型
     */
    private String principalType;

    /**
     * 结算主体 id
     */
    private Integer principalId;

    /**
     * 结算id，前8位是YYYYMMDD
     */
    private String settleId;

    /**
     * 状态，0未结算、1已结算
     */
    private Integer status;

    /**
     * 结算类型，1系统结算，1手工结算
     */
    private Integer settleType;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 结算金额，结算给渠道商的总金额
     */
    private BigDecimal settleAmount;

    /**
     * 结算订单笔数
     */
    private Integer orderNum;

    /**
     * 外部业务单号，如银行流水号
     */
    private String outBizNo;

    /**
     * 渠道商实体id
     */
    private Integer distributorCorpId;

    /**
     * 渠道商实体名称
     */
    private String distributorCorpName;

    /**
     * 结算日期
     */
    private Date settleTime;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}