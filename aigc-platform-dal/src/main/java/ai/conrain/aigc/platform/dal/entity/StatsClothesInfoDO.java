package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 服装信息统计表
 * 对应数据表：stats_clothes_info
 */
@Data
public class StatsClothesInfoDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * vip 用户上传服装 套数
     */
    private Integer vipClothesCount = 0;

    /**
     * 自动训练服装 套数
     */
    private Integer autoTrainCount = 0;

    /**
     * 人工交付服装 套数
     */
    private Integer manualDeliveryCount = 0;

    /**
     * 自动训练+交付 套数
     */
    private Integer autoTrainAndDeliveryCount = 0;

    /**
     * 二次抠图（手动上传图片+系统级抠图） 套数
     */
    private Integer retryMattingCount = 0;

    /**
     * 更新提示词 套数
     */
    private Integer updatePromptCount = 0;

    /**
     * 克隆服装 套数
     */
    private Integer copyCount = 0;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;
 

    private static final long serialVersionUID = 1L;
}