package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupDAO;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.agent.SearchUtil;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.MapUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AnalysisTaskJob extends JavaProcessor {

    /**
     * 自定义线程池，使用指定的阻塞队列管理任务
     * 核心线程数: 10, 最大线程数: 20, 队列容量: 1000
     */
    private static final LinkedBlockingQueue<Runnable> TASK_QUEUE = new LinkedBlockingQueue<>(1000);
    private static final ThreadPoolExecutor VIRTUAL_THREAD_POOL = new ThreadPoolExecutor(
            10, // 核心线程数
            20, // 最大线程数
            60L, // 空闲线程存活时间
            TimeUnit.SECONDS,
            TASK_QUEUE, // 使用我们的阻塞队列
            Thread.ofVirtual().name("image-processing-", 0).factory(),
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
    );

    @Autowired
    private ImageGroupDAO imageGroupDAO;

    @Autowired
    private ImageService imageService;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private CaptionUserService captionUserService;

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        log.info("[AnalysisTaskJob] start, params: {}, jobId: {}", params, context.getJobId());

        CaptionUserVO llm = captionUserService.getOrCreateByUsername("gemini-flash");

        try {
            // 获取所有未标注的图像ID
            List<Integer> imageIds = imageGroupDAO.selectAllImageIdsNotCaptionByUser(llm.getId());
            if (CollectionUtils.isNotEmpty(params.getImageIds())) {
                imageIds = params.getImageIds();
            }
            log.info("[AnalysisTaskJob] Found {} images not captioned by llm {}", imageIds.size(), llm.getId());

            // 直接提交任务到线程池，让线程池管理队列和并发
            int submittedCount = submitTasksToThreadPool(imageIds, llm, context.getJobId());
            log.info("[AnalysisTaskJob] Submitted {} tasks to thread pool, active threads: {}, queue size: {}",
                    submittedCount, VIRTUAL_THREAD_POOL.getActiveCount(), TASK_QUEUE.size());

        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }

    /**
     * 提交任务到线程池处理
     *
     * @param imageIds 图像ID列表
     * @param user 用户信息
     * @param jobId 任务ID
     * @return 实际提交的任务数量
     */
    private int submitTasksToThreadPool(List<Integer> imageIds, CaptionUserVO user, Long jobId) {
        if (imageIds == null || imageIds.isEmpty()) {
            return 0;
        }

        int submittedCount = 0;
        log.info("[AnalysisTaskJob] Starting to submit {} tasks to thread pool", imageIds.size());

        // 直接提交任务到线程池，让线程池自己管理队列和并发
        for (Integer imageId : imageIds) {
            try {
                // 提交任务到线程池，线程池会自动管理队列和并发控制
                VIRTUAL_THREAD_POOL.submit(() -> {
                    try {
                        processImage(imageId, user, jobId);
                        log.info("[AnalysisTaskJob] Successfully processed image {} in thread pool", imageId);
                    } catch (Exception e) {
                        log.error("[AnalysisTaskJob] Failed to process image {} in thread pool", imageId, e);
                    }
                });
                submittedCount++;

                log.debug("[AnalysisTaskJob] Submitted image {} to thread pool, jobId: {}", imageId, jobId);

            } catch (Exception e) {
                log.error("[AnalysisTaskJob] Failed to submit image {} to thread pool", imageId, e);
                // 继续处理其他任务，不中断整个流程
            }
        }

        log.info("[AnalysisTaskJob] Submitted {} tasks to thread pool, active threads: {}, queue size: {}",
                submittedCount, VIRTUAL_THREAD_POOL.getActiveCount(), TASK_QUEUE.size());

        return submittedCount;
    }

    /**
     * 处理单个图像
     *
     * @param imageId 图像ID
     * @param llm 用户信息
     * @param jobId 任务ID
     */
    private void processImage(Integer imageId, CaptionUserVO llm, Long jobId) throws InterruptedException {
        // 1. 调用AI服务进行图像分析
        ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
        imageCaptionUserQuery.setImageId(imageId);
        imageCaptionUserQuery.setUserId(llm.getId());
        List<ImageCaptionUserVO> list = imageCaptionUserService.queryImageCaptionUserList(imageCaptionUserQuery);
        if (!list.isEmpty()) {
            log.info("[AnalysisTaskJob] Image {} already captioned by user {}, skip", imageId, llm.getId());
            return;
        }
        ImageVO imageVO = imageService.selectById(imageId);
        if (imageVO == null) {
            log.info("[AnalysisTaskJob] Image {} not found, skip", imageId);
            return;
        }
        // 2. 保存标注结果
        String cacheKey = DigestUtils.md5Hex(imageVO.getUrl());
        ImageAnalysisResult result = tairService.getObject(cacheKey, ImageAnalysisResult.class);

        if (result == null || result.getAnalysis() == null
            || !SearchUtil.isValidImageAnalysisCloth(result.getAnalysis())) {

            ImageAnalysisTaskResponse task = imageAnalysisService.createAnalysisTask(
                imageVO.getUrl(),
                llm.getUsername(),
                ImageTypeEnum.CLOTH.name().equals(imageVO.getType()),
                true,
                imageVO.getMetadata().getString("clothType"));

            for (;;) {
                result = imageAnalysisService.getAnalysisResult(task.getTaskId());
                if (result != null && result.getAnalysis() != null) {
                    tairService.setObject(cacheKey, result, 60 * 60 * 24);
                    break;
                } else {
                    Thread.sleep(10 * 1000);
                }
            }
        }
        // 3. 更新数据库状态
        Map<String, Object> flattenedCaption = MapUtil.flatten(JSONObject.parseObject(result.getRaw()));
        ImageCaptionUserVO imageCaptionUserVO = new ImageCaptionUserVO();
        imageCaptionUserVO.setImageId(imageId);
        imageCaptionUserVO.setUserId(llm.getId());
        imageCaptionUserVO.setCaption(new com.alibaba.fastjson2.JSONObject(flattenedCaption));
        imageCaptionUserService.insert(imageCaptionUserVO);

        log.info("[AnalysisTaskJob] Processing image {} for user {} in job {}", imageId, llm.getId(), jobId);
    }

    /**
     * 关闭线程池（应用关闭时调用）
     */
    public static void shutdown() {
        VIRTUAL_THREAD_POOL.shutdown();
        try {
            if (!VIRTUAL_THREAD_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                VIRTUAL_THREAD_POOL.shutdownNow();
            }
        } catch (InterruptedException e) {
            VIRTUAL_THREAD_POOL.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("[AnalysisTaskJob] Virtual thread pool shutdown completed");
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 图像ids
         */
        private List<Integer> imageIds;
    }
}
