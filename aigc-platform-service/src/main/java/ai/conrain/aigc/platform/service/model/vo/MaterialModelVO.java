package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.IdModel;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.IScalableClz;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.resolver.ModelVersionSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * MaterialModelVO
 *
 * @version MaterialModelService.java v 0.1 2024-05-09 06:10:02
 */
@Slf4j
@Data
public class MaterialModelVO implements IScalableClz, IdModel, Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 名称 */
    private String name;

    /** 类型，SYSTEM、CUSTOM */
    private ModelTypeEnum type;

    /** 归属主账号id */
    private Integer userId;

    /** 展示图url */
    private String showImage;

    /** lora名称 */
    private String loraName;

    /** 状态，IN_TRAINING、ENABLED、DISABLED */
    private String status;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 标签列表 */
    private String tags;

    private JSONObject extInfo;

    /** 主子类型，NORMAL/MAIN/SUB */
    private MainTypeEnum mainType;

    /** 主模型id */
    private Integer mainId;

    //操作者昵称
    private String operatorNick;
    /** 主账户昵称 */
    private String userNick;
    /** 主账户公司名称 */
    private String userCorpName;
    /** 主账号角色 */
    private RoleTypeEnum userRole;
    /** prompt 工程师昵称 */
    private String promptEngineerNick;

    /** 套餐内免费额度 */
    private Integer modelPoint;

    private LoraTrainDetail clothLoraTrainDetail;
    private Integer materialInfoId;

    /** 素材类型，cloth/face/scene */
    private MaterialType materialType;

    private List<String> exampleImages;

    private List<WorkflowTaskVO> tasks;

    /** 是否下身服装 */
    private boolean lowerBody = false;
    /** 半身配置 */
    private String halfBody;

    //关联的渠道商
    private String relatedDistributorCorpName;

    //是否付费客户的服装
    private boolean paidCustomer;

    //是否vip客户，默认为false，当user.memo里标签包含vip则是true
    private boolean vipCustomer;

    /** 是否有背面图片 */
    private boolean hasBackView;

    /** 服装款式个性化配置 */
    private List<ClothTypeConfig> clothTypeConfigs;

    /** 用户在上传素材时填写的服装配饰 */
    private ClothCollocationModel clothCollocation;

    //用户选择的服装分类（上装/下装等）
    //@see ai.conrain.aigc.platform.service.enums.ClothTypeEnum
    private String clothType;

    //用户选择的服装分类（上装/下装等）
    private String clothTypeDesc;

    //模型版本
    @JsonSerialize(using = ModelVersionSerializer.class)
    private ModelVersionEnum version = ModelVersionEnum.SDXL;

    /** 操作版本 */
    private Integer opVersion;

    /** 商家偏好 */
    private MerchantPreferenceDetail merchantPreference;

    /** 服装颜色明细 */
    private List<ClothColorDetail> colorList;

    /** 是否可审核的 */
    private boolean reviewable;

    //模型关联的元素视图，用于前台用户的「全部资产」页面展示
    private CreativeElementVO relatedElementVO;

    /** 子模型需要确认的数量 */
    private Integer subNeedConfirmCnt;

    /** 子模型审核中数量 */
    private Integer subTestingCnt;

    /** 子模型总数 */
    private Integer subTotal;

    /** 超时时间 */
    private String timeoutHours;

    //是否代传
    public boolean isAgentUploadModel() {
        return this.getExtInfo() != null && this.getExtInfo().containsKey(CommonConstants.uploadByAgentId)
               && this.getExtInfo().getInteger(CommonConstants.uploadByAgentId) != null;
    }

    //是否代熨烫
    public boolean isIroningCloth() {
        return this.extInfo != null && "Y".equals(this.extInfo.getString(CommonConstants.KEY_IRONING_CLOTH));
    }

    public Object getExtInfo(String key) {
        if (extInfo == null) {
            return null;
        }

        return extInfo.get(key);
    }

    public <T> T getExtInfo(String key, Class<T> clz) {
        if (extInfo == null) {
            return null;
        }

        return extInfo.getObject(key, clz);
    }

    public void addExtInfo(String key, Object value) {
        if (extInfo == null) {
            extInfo = new JSONObject();
        }

        extInfo.put(key, value);
    }

    /**
     * 获取关联操作员ID。
     * 如果extInfo中存在relatedOperator字段，则返回该字段值；
     * 否则返回operatorId。
     * 
     * @return 关联操作员ID
     */
    public Integer getRelatedOperatorId() {
        if (extInfo != null && extInfo.containsKey("relatedOperator")) {
            Integer relatedOperator = extInfo.getInteger("relatedOperator");
            if (relatedOperator != null) {
                return relatedOperator;
            }
        }
        return operatorId;
    }
}
