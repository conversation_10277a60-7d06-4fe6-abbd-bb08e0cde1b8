package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class GenreRecognizeService {

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    public String recognizeGenre(String imageUrl, String taskId) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Key", "sajlkbdjlkasbhk");

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("task_id", taskId);
            requestBody.put("url", imageUrl);

            // 创建HttpEntity
            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity("http://127.0.0.1:8823/predict", entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                //{"result":"Internet_style_photo","task_id":"1"}
                JSONObject ret = JSONObject.parseObject(response.getBody());
                if (ret != null && ret.containsKey("result")) {
                    return ret.getString("result");
                }
            }
        } catch (Exception e) {
            log.error("识别图像的拍摄流派失败：" + e.getMessage(), e);
        }

        throw new RuntimeException("识别图像的拍摄流派失败");
    }
}
