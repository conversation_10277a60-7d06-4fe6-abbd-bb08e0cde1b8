package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.OrderInfoDO;
import ai.conrain.aigc.platform.dal.example.OrderInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderInfoDAO {
    long countByExample(OrderInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(OrderInfoDO record);

    int insertSelective(OrderInfoDO record);

    List<OrderInfoDO> selectByExample(OrderInfoExample example);

    OrderInfoDO selectByPrimaryKey(Integer id);

    OrderInfoDO lockById(Integer id);

    OrderInfoDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") OrderInfoDO record, @Param("example") OrderInfoExample example);

    int updateByExample(@Param("record") OrderInfoDO record, @Param("example") OrderInfoExample example);

    int updateByPrimaryKeySelective(OrderInfoDO record);

    int updateByPrimaryKey(OrderInfoDO record);

    int logicalDeleteByExample(@Param("example") OrderInfoExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    List<OrderInfoDO> queryOrdersByMonth(String month);

    /**
     * 查询用户充值金额小于3999的用户
     *
     * @param userIdList  用户id列表
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param greaterThan 是否大于
     * @return 用户 id 列表
     */
    List<Integer> findUsersWithMaxRechargeCompare3999(@Param("greaterThan") Boolean greaterThan,
                                                      @Param("list") List<Integer> userIdList,
                                                      @Param("startDate") String startDate,
                                                      @Param("endDate") String endDate
    );

    /**
     * 查询没有订单记录或者含有订单记录但是小于 3999 的用户
     *
     * @param yesterdayDate 昨天时间
     * @param userIdList    用户id列表
     * @param isCheckAmount 是否检测充值金额小于 3999
     * @return 订单列表
     */
    List<Integer> selectNoOrderOrLessThan3999(@Param("yesterdayDate") String yesterdayDate,
                                              @Param("list") List<Integer> userIdList,
                                              @Param("isCheckAmount") Boolean isCheckAmount);


}