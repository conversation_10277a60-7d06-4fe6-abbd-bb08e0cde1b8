package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.UserOpLogDO;
import ai.conrain.aigc.platform.service.model.vo.UserOpLogVO;
import ai.conrain.aigc.platform.service.model.query.UserOpLogQuery;
import ai.conrain.aigc.platform.service.component.UserOpLogService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * UserOpLog控制器
 *
 * <AUTHOR>
 * @version UserOpLogService.java v 0.1 2024-01-25 09:31:00
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/userOpLog")
public class UserOpLogController {

	/** userOpLogService */
	@Autowired
	private UserOpLogService userOpLogService;
	
	@GetMapping("/getUserOpLogById/{id}")
	public Result<UserOpLogVO> getUserOpLogById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(userOpLogService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody UserOpLogVO userOpLog){
		try {
			UserOpLogVO data = userOpLogService.insert(userOpLog);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加用户操作记录失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户操作记录失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull Integer id) {
		userOpLogService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody UserOpLogVO userOpLog){
		userOpLogService.updateByIdSelective(userOpLog);
		return Result.success();
	}

	@PostMapping("/queryUserOpLogList")
	public Result<List<UserOpLogVO>> queryUserOpLogList(@Valid @RequestBody UserOpLogQuery query){
		return Result.success(userOpLogService.queryUserOpLogList(query));
	}
	
	@PostMapping("/getPageUserOpLog")
	public Result<PageInfo<UserOpLogVO>> getUserOpLogByPage(@Valid @RequestBody UserOpLogQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(userOpLogService.queryUserOpLogByPage(query));
	}
}
