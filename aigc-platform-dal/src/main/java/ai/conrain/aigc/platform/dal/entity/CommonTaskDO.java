package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通用任务表
 * 对应数据表：common_task
 */
@Data
public class CommonTaskDO implements Serializable {
    /**
     * 任务id
     */
    private Integer id;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 操作员/子账号id
     */
    private Integer operatorId;

    /**
     * 任务类型, video_generation/extend_video/lip_sync等
     */
    private String taskType;

    /**
     * 任务状态, INIT/PENDING/RUNNING/COMPLETED/FAILED/CANCELED
     */
    private String taskStatus;

    /**
     * 第三方平台名称
     */
    private String outTaskPlatform;

    /**
     * 第三方平台任务id
     */
    private String outTaskId;

    /**
     * 任务状态
     */
    private String outTaskStatus;

    /**
     * 自定义请求参数
     */
    private String reqBizParams;

    /**
     * 关联业务类型
     */
    private String relatedBizType;

    /**
     * 关联业务id
     */
    private String relatedBizId;

    /**
     * 扩展
     */
    private String extInfo;

    /**
     * 任务开始时间
     */
    private Date taskStartTime;

    /**
     * 任务结束时间
     */
    private Date taskEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 完整请求报文，主要用于请求外部http或comfyui服务的重试
     */
    private String completeRequest;

    /**
     * 结果详情
     */
    private String retDetail;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}