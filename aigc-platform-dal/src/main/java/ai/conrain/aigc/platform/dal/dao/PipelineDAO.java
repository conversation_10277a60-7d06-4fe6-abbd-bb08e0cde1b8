package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.PipelineDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PipelineDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(PipelineDO record);

    PipelineDO selectByPrimaryKey(Integer id);

    List<PipelineDO> selectAll();

    int updateByPrimaryKey(PipelineDO record);

    Integer selectByUserId(Integer userId);

    List<Integer> selectRelatedUsers(Integer id);
}