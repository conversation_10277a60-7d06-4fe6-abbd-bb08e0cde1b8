package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsClothesInfoDAO {
    long countByExample(StatsClothesInfoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsClothesInfoDO record);

    int insertSelective(StatsClothesInfoDO record);

    List<StatsClothesInfoDO> selectByExampleWithBLOBs(StatsClothesInfoExample example);

    List<StatsClothesInfoDO> selectByExample(StatsClothesInfoExample example);

    StatsClothesInfoDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsClothesInfoDO record, @Param("example") StatsClothesInfoExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsClothesInfoDO record, @Param("example") StatsClothesInfoExample example);

    int updateByExample(@Param("record") StatsClothesInfoDO record, @Param("example") StatsClothesInfoExample example);

    int updateByPrimaryKeySelective(StatsClothesInfoDO record);

    int updateByPrimaryKeyWithBLOBs(StatsClothesInfoDO record);

    int updateByPrimaryKey(StatsClothesInfoDO record);

    /**
     * 批量插入或更新
     * @param statsList 数据
     * @return 新增/修改条数
     */
    int batchInsertOrUpdateSelective(List<StatsClothesInfoDO> statsList);

    /**
     * 根据开始日期和结束日期获取对应一周的数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 数据列表
     */
    List<StatsClothesInfoDO> selectDailyStatsByWeek(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 根据开始日期和结束日期获取对应一个月的数据
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 数据列表
     */
    List<StatsClothesInfoDO> selectDailyStatsByMonth(@Param("startDate") String startDate, @Param("endDate")     String endDate);

    /**
     * 根据日期和统计周期查询日统计数据
     * @param date 日期
     * @param statsType 统计周期
     * @return 统计数据对象
     */
    StatsClothesInfoDO selectStatsInfoByDateAndPeriod(@Param("date") String date, @Param("statsType") String statsType);
}