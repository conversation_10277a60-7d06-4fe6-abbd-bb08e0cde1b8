package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.SearchTaskExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SearchTaskDAO {
    long countByExample(SearchTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SearchTaskDO record);

    int insertSelective(SearchTaskDO record);

    List<SearchTaskDO> selectByExample(SearchTaskExample example);

    SearchTaskDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SearchTaskDO record, @Param("example") SearchTaskExample example);

    int updateByExample(@Param("record") SearchTaskDO record, @Param("example") SearchTaskExample example);

    int updateByPrimaryKeySelective(SearchTaskDO record);

    int updateByPrimaryKey(SearchTaskDO record);
}