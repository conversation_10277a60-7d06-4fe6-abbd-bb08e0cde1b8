package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TrainPlanDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TrainPlanDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainPlanDO record);

    TrainPlanDO selectByPrimaryKey(Integer id);

    List<TrainPlanDO> selectAll();

    int updateByPrimaryKey(TrainPlanDO record);
}