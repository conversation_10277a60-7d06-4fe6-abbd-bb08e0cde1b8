package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.OrganizationDO;
import ai.conrain.aigc.platform.dal.example.OrganizationExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OrganizationDAO {
    long countByExample(OrganizationExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(OrganizationDO record);

    int insertSelective(OrganizationDO record);

    List<OrganizationDO> selectByExample(OrganizationExample example);

    OrganizationDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") OrganizationDO record, @Param("example") OrganizationExample example);

    int updateByExample(@Param("record") OrganizationDO record, @Param("example") OrganizationExample example);

    int updateByPrimaryKeySelective(OrganizationDO record);

    int updateByPrimaryKey(OrganizationDO record);

    OrganizationDO selectOrganizationByUserId(Integer userId);

    List<OrganizationDO> selectByRootId(@Param("rootId") Integer rootId);

    List<OrganizationDO> selectChildrenByParentId(@Param("parentId") Integer parentId);

    List<OrganizationDO> selectDistributorCorps(Integer offset, Integer limit);
}