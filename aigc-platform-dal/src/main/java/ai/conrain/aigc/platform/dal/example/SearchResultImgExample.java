package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SearchResultImgExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public SearchResultImgExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSearchIdIsNull() {
            addCriterion("search_id is null");
            return (Criteria) this;
        }

        public Criteria andSearchIdIsNotNull() {
            addCriterion("search_id is not null");
            return (Criteria) this;
        }

        public Criteria andSearchIdEqualTo(Integer value) {
            addCriterion("search_id =", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdNotEqualTo(Integer value) {
            addCriterion("search_id <>", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdGreaterThan(Integer value) {
            addCriterion("search_id >", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("search_id >=", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdLessThan(Integer value) {
            addCriterion("search_id <", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdLessThanOrEqualTo(Integer value) {
            addCriterion("search_id <=", value, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdIn(List<Integer> values) {
            addCriterion("search_id in", values, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdNotIn(List<Integer> values) {
            addCriterion("search_id not in", values, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdBetween(Integer value1, Integer value2) {
            addCriterion("search_id between", value1, value2, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchIdNotBetween(Integer value1, Integer value2) {
            addCriterion("search_id not between", value1, value2, "searchId");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchIsNull() {
            addCriterion("search_ret_batch is null");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchIsNotNull() {
            addCriterion("search_ret_batch is not null");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchEqualTo(String value) {
            addCriterion("search_ret_batch =", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchNotEqualTo(String value) {
            addCriterion("search_ret_batch <>", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchGreaterThan(String value) {
            addCriterion("search_ret_batch >", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchGreaterThanOrEqualTo(String value) {
            addCriterion("search_ret_batch >=", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchLessThan(String value) {
            addCriterion("search_ret_batch <", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchLessThanOrEqualTo(String value) {
            addCriterion("search_ret_batch <=", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchLike(String value) {
            addCriterion("search_ret_batch like", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchNotLike(String value) {
            addCriterion("search_ret_batch not like", value, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchIn(List<String> values) {
            addCriterion("search_ret_batch in", values, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchNotIn(List<String> values) {
            addCriterion("search_ret_batch not in", values, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchBetween(String value1, String value2) {
            addCriterion("search_ret_batch between", value1, value2, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andSearchRetBatchNotBetween(String value1, String value2) {
            addCriterion("search_ret_batch not between", value1, value2, "searchRetBatch");
            return (Criteria) this;
        }

        public Criteria andImageIdIsNull() {
            addCriterion("image_id is null");
            return (Criteria) this;
        }

        public Criteria andImageIdIsNotNull() {
            addCriterion("image_id is not null");
            return (Criteria) this;
        }

        public Criteria andImageIdEqualTo(Integer value) {
            addCriterion("image_id =", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotEqualTo(Integer value) {
            addCriterion("image_id <>", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdGreaterThan(Integer value) {
            addCriterion("image_id >", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_id >=", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdLessThan(Integer value) {
            addCriterion("image_id <", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdLessThanOrEqualTo(Integer value) {
            addCriterion("image_id <=", value, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdIn(List<Integer> values) {
            addCriterion("image_id in", values, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotIn(List<Integer> values) {
            addCriterion("image_id not in", values, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdBetween(Integer value1, Integer value2) {
            addCriterion("image_id between", value1, value2, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("image_id not between", value1, value2, "imageId");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlIsNull() {
            addCriterion("image_show_url is null");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlIsNotNull() {
            addCriterion("image_show_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlEqualTo(String value) {
            addCriterion("image_show_url =", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlNotEqualTo(String value) {
            addCriterion("image_show_url <>", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlGreaterThan(String value) {
            addCriterion("image_show_url >", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_show_url >=", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlLessThan(String value) {
            addCriterion("image_show_url <", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlLessThanOrEqualTo(String value) {
            addCriterion("image_show_url <=", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlLike(String value) {
            addCriterion("image_show_url like", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlNotLike(String value) {
            addCriterion("image_show_url not like", value, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlIn(List<String> values) {
            addCriterion("image_show_url in", values, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlNotIn(List<String> values) {
            addCriterion("image_show_url not in", values, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlBetween(String value1, String value2) {
            addCriterion("image_show_url between", value1, value2, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageShowUrlNotBetween(String value1, String value2) {
            addCriterion("image_show_url not between", value1, value2, "imageShowUrl");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdIsNull() {
            addCriterion("image_caption_id is null");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdIsNotNull() {
            addCriterion("image_caption_id is not null");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdEqualTo(Integer value) {
            addCriterion("image_caption_id =", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdNotEqualTo(Integer value) {
            addCriterion("image_caption_id <>", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdGreaterThan(Integer value) {
            addCriterion("image_caption_id >", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_caption_id >=", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdLessThan(Integer value) {
            addCriterion("image_caption_id <", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdLessThanOrEqualTo(Integer value) {
            addCriterion("image_caption_id <=", value, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdIn(List<Integer> values) {
            addCriterion("image_caption_id in", values, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdNotIn(List<Integer> values) {
            addCriterion("image_caption_id not in", values, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdBetween(Integer value1, Integer value2) {
            addCriterion("image_caption_id between", value1, value2, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("image_caption_id not between", value1, value2, "imageCaptionId");
            return (Criteria) this;
        }

        public Criteria andGenreIsNull() {
            addCriterion("genre is null");
            return (Criteria) this;
        }

        public Criteria andGenreIsNotNull() {
            addCriterion("genre is not null");
            return (Criteria) this;
        }

        public Criteria andGenreEqualTo(String value) {
            addCriterion("genre =", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotEqualTo(String value) {
            addCriterion("genre <>", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreGreaterThan(String value) {
            addCriterion("genre >", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreGreaterThanOrEqualTo(String value) {
            addCriterion("genre >=", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLessThan(String value) {
            addCriterion("genre <", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLessThanOrEqualTo(String value) {
            addCriterion("genre <=", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreLike(String value) {
            addCriterion("genre like", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotLike(String value) {
            addCriterion("genre not like", value, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreIn(List<String> values) {
            addCriterion("genre in", values, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotIn(List<String> values) {
            addCriterion("genre not in", values, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreBetween(String value1, String value2) {
            addCriterion("genre between", value1, value2, "genre");
            return (Criteria) this;
        }

        public Criteria andGenreNotBetween(String value1, String value2) {
            addCriterion("genre not between", value1, value2, "genre");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdIsNull() {
            addCriterion("bg_cluster_id is null");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdIsNotNull() {
            addCriterion("bg_cluster_id is not null");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdEqualTo(String value) {
            addCriterion("bg_cluster_id =", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdNotEqualTo(String value) {
            addCriterion("bg_cluster_id <>", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdGreaterThan(String value) {
            addCriterion("bg_cluster_id >", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdGreaterThanOrEqualTo(String value) {
            addCriterion("bg_cluster_id >=", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdLessThan(String value) {
            addCriterion("bg_cluster_id <", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdLessThanOrEqualTo(String value) {
            addCriterion("bg_cluster_id <=", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdLike(String value) {
            addCriterion("bg_cluster_id like", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdNotLike(String value) {
            addCriterion("bg_cluster_id not like", value, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdIn(List<String> values) {
            addCriterion("bg_cluster_id in", values, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdNotIn(List<String> values) {
            addCriterion("bg_cluster_id not in", values, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdBetween(String value1, String value2) {
            addCriterion("bg_cluster_id between", value1, value2, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andBgClusterIdNotBetween(String value1, String value2) {
            addCriterion("bg_cluster_id not between", value1, value2, "bgClusterId");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityIsNull() {
            addCriterion("style_similarity is null");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityIsNotNull() {
            addCriterion("style_similarity is not null");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityEqualTo(Double value) {
            addCriterion("style_similarity =", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityNotEqualTo(Double value) {
            addCriterion("style_similarity <>", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityGreaterThan(Double value) {
            addCriterion("style_similarity >", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityGreaterThanOrEqualTo(Double value) {
            addCriterion("style_similarity >=", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityLessThan(Double value) {
            addCriterion("style_similarity <", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityLessThanOrEqualTo(Double value) {
            addCriterion("style_similarity <=", value, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityIn(List<Double> values) {
            addCriterion("style_similarity in", values, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityNotIn(List<Double> values) {
            addCriterion("style_similarity not in", values, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityBetween(Double value1, Double value2) {
            addCriterion("style_similarity between", value1, value2, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andStyleSimilarityNotBetween(Double value1, Double value2) {
            addCriterion("style_similarity not between", value1, value2, "styleSimilarity");
            return (Criteria) this;
        }

        public Criteria andMatchScoreIsNull() {
            addCriterion("match_score is null");
            return (Criteria) this;
        }

        public Criteria andMatchScoreIsNotNull() {
            addCriterion("match_score is not null");
            return (Criteria) this;
        }

        public Criteria andMatchScoreEqualTo(Double value) {
            addCriterion("match_score =", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreNotEqualTo(Double value) {
            addCriterion("match_score <>", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreGreaterThan(Double value) {
            addCriterion("match_score >", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreGreaterThanOrEqualTo(Double value) {
            addCriterion("match_score >=", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreLessThan(Double value) {
            addCriterion("match_score <", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreLessThanOrEqualTo(Double value) {
            addCriterion("match_score <=", value, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreIn(List<Double> values) {
            addCriterion("match_score in", values, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreNotIn(List<Double> values) {
            addCriterion("match_score not in", values, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreBetween(Double value1, Double value2) {
            addCriterion("match_score between", value1, value2, "matchScore");
            return (Criteria) this;
        }

        public Criteria andMatchScoreNotBetween(Double value1, Double value2) {
            addCriterion("match_score not between", value1, value2, "matchScore");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}