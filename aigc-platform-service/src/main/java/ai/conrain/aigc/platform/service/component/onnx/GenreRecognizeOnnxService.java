package ai.conrain.aigc.platform.service.component.onnx;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Collections;
import java.util.Map;

@Slf4j
@Component
public class GenreRecognizeOnnxService {

    // 使用Java内置图像处理，无需加载外部库
    private static final boolean imageProcessingAvailable = true;

    private SimpleOnnxWrapper onnxWrapper;
    private static final String ONNX_MODEL_PATH = "onnx/genre_model.onnx";


    // 类别标签映射
    private static final Map<Integer, String> CLASS_LABELS = Map.of(
            0, "Product_lookbook",
            1, "Runway_shot",
            2, "Vogue_style_fashion_shoot",
            3, "LRSP2111_commercial",
            4, "Selfie",
            5, "Internet_style_photo");

    @PostConstruct
    public void init() {
        onnxWrapper = new SimpleOnnxWrapper(ONNX_MODEL_PATH, "流派识别模型服务");
        onnxWrapper.init();
    }

    @PreDestroy
    public void destroy() {
        if (onnxWrapper != null) {
            onnxWrapper.destroy();
        }
    }

    /**
     * 基于onnx包，识别图像的流派类别
     */
    public String recognizeGenreFromPath(String imgPath) throws OrtException {
        if (!imageProcessingAvailable) {
            log.warn("图像处理功能不可用");
            throw new IllegalStateException("图像处理功能不可用");
        }
        
        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imgPath == null || imgPath.trim().isEmpty()) {
            throw new IllegalArgumentException("图像路径不能为空");
        }

        try {
            log.info("开始识别图像流派，图像路径: {}", imgPath);

            // 加载和预处理图像
            float[][][][] imageData = loadAndPreprocessImage(imgPath);

            // 创建输入张量
            OnnxTensor inputTensor = OnnxTensor.createTensor(onnxWrapper.getEnvironment(), imageData);

            // 执行推理
            Map<String, OnnxTensor> inputs = Collections.singletonMap("image", inputTensor);
            OrtSession.Result result = onnxWrapper.getSession().run(inputs);

            // 获取输出
            OnnxTensor outputTensor = (OnnxTensor) result.get(0);
            long[][] predictions = (long[][]) outputTensor.getValue();

            // 获取预测类别
            int predictedClass = (int) predictions[0][0];
            String genreLabel = CLASS_LABELS.get(predictedClass);

            log.info("图像流派识别完成，预测类别: {} ({})", predictedClass, genreLabel);

            // 清理资源
            inputTensor.close();
            result.close();

            return genreLabel;

        } catch (Exception e) {
            log.error("图像流派识别失败，图像路径: {}", imgPath, e);
            throw new OrtException("图像流派识别失败: " + e.getMessage());
        }
    }

    /**
     * 从URL下载图像并识别流派
     */
    public String recognizeGenreFromUrl(String imageUrl) throws OrtException {
        if (!imageProcessingAvailable) {
            log.warn("图像处理功能不可用");
            throw new IllegalStateException("图像处理功能不可用");
        }
        
        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("图像URL不能为空");
        }

        Path tempFile = null;

        try {
            log.info("开始下载并识别网络图像流派，图像URL: {}", imageUrl);

            // 下载图片到本地
            tempFile = downloadImageFromUrl(imageUrl);
            String localPath = tempFile.toAbsolutePath().toString();
            log.debug("图片下载完成: {} -> {}", imageUrl, localPath);

            // 调用现有的本地文件识别方法
            String result = recognizeGenreFromPath(localPath);

            log.info("网络图像流派识别完成，结果: {}", result);
            return result;

        } catch (IOException e) {
            log.error("下载图像失败，URL: {}", imageUrl, e);
            throw new OrtException("下载图像失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                    log.debug("临时文件已清理: {}", tempFile);
                } catch (Exception e) {
                    log.warn("清理临时文件失败: {}", tempFile, e);
                }
            }
        }
    }

    /**
     * 从URL下载图片到本地临时文件
     * 
     * @param imageUrl 图片URL
     * @return 本地临时文件路径
     * @throws IOException 下载异常
     */
    private Path downloadImageFromUrl(String imageUrl) throws IOException {
        // 从URL中提取文件扩展名，默认为.jpg
        String extension = ".jpg";
        if (imageUrl.contains(".")) {
            String urlPath = imageUrl.split("\\?")[0]; // 去除URL参数
            int lastDotIndex = urlPath.lastIndexOf(".");
            if (lastDotIndex > 0 && lastDotIndex < urlPath.length() - 1) {
                String ext = urlPath.substring(lastDotIndex);
                if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
                    extension = ext;
                }
            }
        }

        // 创建临时文件
        Path tempFile = Files.createTempFile("genre_recognize_", extension);

        // 下载图片
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
        }

        return tempFile;
    }

    /**
     * 加载和预处理图像
     * 
     * @param imagePath 图像文件路径
     * @return 预处理后的图像数据
     */
    private float[][][][] loadAndPreprocessImage(String imagePath) {
        try {
            // 使用Java内置ImageIO加载图像
            BufferedImage originalImage = ImageIO.read(new java.io.File(imagePath));
            
            if (originalImage == null) {
                throw new RuntimeException("无法加载图像: " + imagePath);
            }

            log.debug("原始图像尺寸: {}x{}", originalImage.getWidth(), originalImage.getHeight());

            // 调整图像尺寸到模型要求的224x224
            BufferedImage resizedImage = new BufferedImage(224, 224, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(originalImage, 0, 0, 224, 224, null);
            g2d.dispose();

            log.debug("调整后尺寸: 224x224");

            // 转换为float数组
            float[][][][] imageData = new float[1][3][224][224];

            // 提取像素数据
            for (int h = 0; h < 224; h++) {
                for (int w = 0; w < 224; w++) {
                    int rgb = resizedImage.getRGB(w, h);
                    // 提取RGB通道，值范围保持[0, 255]
                    imageData[0][0][h][w] = (float) ((rgb >> 16) & 0xFF); // R
                    imageData[0][1][h][w] = (float) ((rgb >> 8) & 0xFF);  // G
                    imageData[0][2][h][w] = (float) (rgb & 0xFF);         // B
                }
            }

            return imageData;
            
        } catch (IOException e) {
            throw new RuntimeException("加载图像失败: " + imagePath, e);
        }
    }
}
