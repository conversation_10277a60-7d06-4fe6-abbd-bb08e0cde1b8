package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 创作批次表
 * 对应数据表：creative_batch
 */
@Data
public class CreativeBatchDO implements Serializable {
    /**
     * 批次id
     */
    private Integer id;

    /**
     * 模型id
     */
    private Integer modelId;

    /** 模型类型 */
    private String modelType;

    /** 创作类型 */
    private String type;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 图片url
     */
    private String showImage;

    /**
     * 图片比例，3:4、1:1等
     */
    private String imageProportion;

    /**
     * 批次数量
     */
    private Integer batchCnt;

    /**
     * ComfyUI返回的唯一标识
     */
    private String promptId;

    /**
     * 结果图片路径
     */
    private String resultPath;

    /** 扩展信息 */
    private String extInfo;

    /**
     * 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED
     */
    private String status;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 标题
     */
    private String title;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * aigc请求参数
     */
    private String aigcRequest;

    /**
     * 结果图片url列表
     */
    private String resultImages;

    //操作人昵称（列表查询时视图返回）
    private String operatorNick;

    //主账户昵称（列表查询时视图返回）
    private String userNick;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}