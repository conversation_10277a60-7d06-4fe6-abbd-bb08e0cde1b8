/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.OperationSession;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.web.helper.CookieUtils;
import ai.conrain.aigc.platform.web.helper.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 操作上下文拦截器
 *
 * <AUTHOR>
 * @version : OperationContextInterceptor.java, v 0.1 2023/9/3 18:22 renxiao.wu Exp $
 */
@Slf4j
@Component
public class OperationContextInterceptor implements HandlerInterceptor {
    /**
     * session帮助类
     */
    @Autowired
    private SessionHelper sessionHelper;

    /** 缓存服务 */
    @Autowired
    private TairService tairService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        // 预处理时清理一遍，防止有残留
        OperationContextHolder.clean();

        OperationContext context = OperationContextHolder.getContext();
        context.setIp(request.getRemoteAddr());
        context.setRequestFrom(request.getHeader("from"));
        context.setMiniAppId(request.getHeader("miniAppId"));

        //初始化session
        OperationSession session = getOrCreateSession(request, response);
        context.setOperationSession(session);

        //日志上下文
        MDC.put("sessionId", session.getSessionId());
        MDC.put("userId", context.getCurrentUserId() != null ? context.getCurrentUserId().toString() : "");

        //校验用户状态
        if (session.getLoginUser() != null
            && (!UserStatusEnum.ENABLED.equals(session.getLoginUser().getStatus())
            || !UserStatusEnum.ENABLED.equals(session.getLoginUser().getMasterStatus()))) {
            log.warn("当前用户状态异常，清除对应登录态自动退出.uid={}", session.getLoginUser().getId());
            sessionHelper.clear(response);
            return false;
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        //刷新cookie中的session过期时间
        sessionHelper.refreshSessionExpire(request, response);

        OperationContextHolder.clean();

        //清理日志上下文
        MDC.remove("sessionId");
        MDC.remove("userId");
    }

    private OperationSession getOrCreateSession(HttpServletRequest request, HttpServletResponse response) {

        //从cookie中取出自定义的session id
        String sessionId = CookieUtils.fetchCookieValue(CommonConstants.SESSION_KEY_ID, request);

        //cookie里有session id才去查用户会话（注意，此时不一定有登录态）
        if (StringUtils.isNotBlank(sessionId)) {
            OperationSession session = tairService.getObject(sessionId, OperationSession.class);
            if (session != null) {
                return session;
            } else {
                log.info("当前cookie中session id已经无效，清除它{}", sessionId);
                CookieUtils.deleteCookie(response, CommonConstants.SESSION_KEY_ID, sessionId);
            }
        }

        //cookie中没有session id 或 tair中session已经过期，重新生成会话，并写入cookie和tair
        return sessionHelper.createSessionAndSetCookie(response);
    }
}