package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 服务器配置表
 * 对应数据表：server
 */
@Data
public class ServerDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 等级，一级为服务器，二级为具体端口
     */
    private Integer level;

    /**
     * 配置值
     */
    private String config;

    /** 配置值别名 */
    private String configAlias;

    /** 内网地址 */
    private String intranetAddress;

    /**
     * 类型，生图、Lora训练、文件服务
     */
    private String type;

    /** 状态 */
    private String status;

    /**
     * 父节点id
     */
    private Integer parentId;

    /**
     * 管道id
     */
    private Integer pipelineId;

    /** 设备信息编号 */
    private String deviceId;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config == null ? null : config.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Integer pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}