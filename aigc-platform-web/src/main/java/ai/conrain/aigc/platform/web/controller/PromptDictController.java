package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONArray;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DEMO_ACCOUNT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.MERCHANT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.OPERATOR;

/**
 * PromptDict控制器
 *
 * <AUTHOR>
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/promptDict")
public class PromptDictController {

    /** promptDictService */
    @Autowired
    private PromptDictService promptDictService;

    @GetMapping("/getById/{id}")
    public Result<PromptDictVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(promptDictService.selectByIdFromDB(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody PromptDictVO promptDict) {
        try {
            PromptDictVO data = promptDictService.insert(promptDict);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加prompt关键字字典失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建prompt关键字字典失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        promptDictService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateById(@Valid @RequestBody PromptDictVO promptDict) {
        promptDictService.updateById(promptDict);
        return Result.success();
    }

    @GetMapping("/queryAll")
    public Result<List<PromptDictVO>> findAll() {
        return Result.success(promptDictService.queryAll());
    }

    @GetMapping("/querySystemCollocation")
    public Result<List<PromptDictVO>> querySystemCollocation() {
        List<PromptDictVO> data = promptDictService.querySystemCollocation();
        return Result.success(pruneForFront(data, false));
    }

    @GetMapping("/querySystemScene")
    public Result<List<PromptDictVO>> querySystemScene() {
        List<PromptDictVO> data = promptDictService.querySystemScene();
        return Result.success(pruneForFront(data, true));
    }

    @GetMapping("/queryGarmentList")
    public Result<List<PromptDictVO>> queryGarmentList() {
        List<PromptDictVO> data = promptDictService.queryGarmentList();
        return Result.success(pruneForFront(data, true));
    }

    @GetMapping("/queryImageByTags/{tagName}")
    public Result<List<PromptDictVO>> queryImageByTags(@PathVariable String tagName) {

        if (StringUtils.isBlank(tagName)) {
            return Result.failedWithMessage(ResultCode.SYS_ERROR, "标签不能为空");
        }

        // 获取标签列表
        List<PromptDictVO> data = promptDictService.queryListByType(DictTypeEnum.IMAGE_TAGS, tagName);

        // 返回结果
        return Result.success(pruneForFront(data, true));
    }

    @PostMapping("/queryListByTypeAndTags")
    public Result<List<PromptDictVO>> queryListByTypeAndTags(@NotBlank @JsonArg String type, @NotEmpty @JsonArg JSONArray tagNames) {

        DictTypeEnum dictType = DictTypeEnum.getByCode(type);
        AssertUtil.assertNotNull(dictType, "字典类型不存在");

        // 获取标签列表
        List<PromptDictVO> data = promptDictService.queryListByType(dictType, tagNames.toJavaList(String.class));

        // 返回结果
        return Result.success(pruneForFront(data, true));
    }

    @PostMapping("/queryByKeyAndTags")
    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    public Result<Map<String, List<PromptDictVO>>> queryByKeyAndTags(@NotBlank @JsonArg String key, @NotEmpty @JsonArg JSONArray tagNamesList) {

        DictTypeEnum dictType = DictTypeEnum.getByCode(key);
        AssertUtil.assertNotNull(dictType, "字典类型不存在");

        Map<String, List<PromptDictVO>> result = new HashMap<>();
        for (Object each : tagNamesList) {
            if (!(each instanceof List)) {
                continue;
            }
            //noinspection unchecked
            List<String> tagNames = (List<String>)each;
            List<PromptDictVO> items = promptDictService.queryListByType(dictType, tagNames);
            result.put(CommonUtil.sortAndJoin(tagNames), pruneForFront(items, true));
        }

        // 返回结果
        return Result.success(result);
    }

    /**
     * 对前台查询结果进行剪枝
     *
     * @param data       查询结果
     * @param needPrompt 是否需要prompt
     * @return 结果
     */
    private static List<PromptDictVO> pruneForFront(List<PromptDictVO> data, boolean needPrompt) {
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        List<PromptDictVO> target = new ArrayList<>();
        for (PromptDictVO item : data) {
            PromptDictVO newItem = new PromptDictVO();
            newItem.setId(item.getId());
            newItem.setType(item.getType());
            newItem.setWord(item.getWord());
            newItem.setTags(item.getTags());
            newItem.setMemo(item.getMemo());
            if (needPrompt) {
                newItem.setPrompt(item.getPrompt());
            }
            target.add(newItem);
        }
        return target;
    }

}
