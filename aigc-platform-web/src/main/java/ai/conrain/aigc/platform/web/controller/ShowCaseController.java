package ai.conrain.aigc.platform.web.controller;

import java.io.IOException;
import java.util.*;

import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.ShowCaseDetail;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.ShowCaseVO;
import ai.conrain.aigc.platform.service.component.ShowCaseService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import com.alibaba.fastjson.JSONObject;

/**
 * ShowCase控制器
 *
 * <AUTHOR>
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/showCase")
public class ShowCaseController {

    /** showCaseService */
    @Autowired
    private ShowCaseService showCaseService;
    @Autowired
    private CreativeElementService creativeElementService;

    @GetMapping("/getById/{id}")
    public Result<ShowCaseVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(showCaseService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody ShowCaseVO showCase) {
        try {
            showCase.setOrder(1);
            showCase.setStatus(MaterialModelStatusEnum.ENABLED);
            ShowCaseVO data = showCaseService.insert(showCase);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加优秀案例失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建优秀案例失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        showCaseService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateById(@Valid @RequestBody ShowCaseVO showCase) throws IOException {
        showCaseService.updateById(showCase);
        return Result.success();
    }

    @GetMapping("/findAll")
    public Result<List<ShowCaseVO>> findAll() {
        return Result.success(showCaseService.queryAll());
    }

    @GetMapping("/queryIndex")
    public Result<List<ShowCaseDetail>> query() {
        List<ShowCaseVO> data = showCaseService.queryAll();
        List<ShowCaseDetail> detailList = new ArrayList<>();

        List<Integer> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data)) {
            ids.addAll(data.stream().map(ShowCaseVO::getFaceId).collect(Collectors.toList()));
            ids.addAll(data.stream().map(ShowCaseVO::getSceneId).collect(Collectors.toList()));
        }

        List<CreativeElementVO> elements = null;
        //TODO 增加缓存
        if (CollectionUtils.isNotEmpty(ids)) {
            elements = creativeElementService.batchQueryByIds(ids);
        }

        for (ShowCaseVO item : data) {
            if (item.getStatus() != MaterialModelStatusEnum.ENABLED) {
                continue;
            }

            ShowCaseDetail detail = new ShowCaseDetail();
            detail.setType(item.getType());
            detail.setMainUrl(item.getMainUrl());
            detail.setShowImage(item.getShowImage());
            detail.setFace(buildShowElement(item.getFaceId(), elements));
            detail.setScene(buildShowElement(item.getSceneId(), elements));
            detail.setModelUrl(item.getModelUrl());
            detail.setModelMiniUrl(item.getModelMiniUrl());
            detail.setTags(item.getTags());
            detail.setTopped(item.isTopped());
            detail.setClothCollocation(item.getClothCollocation());
            detailList.add(detail);
        }
        return Result.success(detailList);
    }

    public JSONObject buildShowElement(Integer id, List<CreativeElementVO> elements) {
        if (CollectionUtils.isEmpty(elements)) {
            return null;
        }

        CreativeElementVO element = elements.stream().filter(e -> e.getId().equals(id)).findFirst().orElse(null);
        if (element == null) {
            return null;
        }

        JSONObject json = new JSONObject();
        json.put("name", element.getName());
        json.put("url", element.getShowImage());
        json.put("id", id);
        return json;
    }
}
