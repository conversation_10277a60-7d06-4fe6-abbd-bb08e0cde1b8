package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ImageGroupService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ImageGroup控制器
 *
 * <AUTHOR>
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/imageGroup")
public class ImageGroupController {

	/** imageGroupService */
	@Autowired
	private ImageGroupService imageGroupService;

	@GetMapping("/getById/{id}")
	public Result<ImageGroupVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageGroupService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageGroupVO imageGroup){
		return Result.success(imageGroupService.insert(imageGroup).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageGroupService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageGroupVO imageGroup){
		imageGroupService.updateByIdSelective(imageGroup);
		return Result.success();
	}

    @PostMapping("/getClothInfoById")
    public Result<ClothInfoVO> getClothInfoById(@JsonArg @NotNull Integer id) {
        return Result.success(imageGroupService.getClothInfoById(id));
    }

    @PostMapping("/updateByClothId")
	public Result<Boolean> updateByClothId(@JsonArg @NotNull Integer imageGroupId, @JsonArg @NotNull Integer clothId){
        imageGroupService.updateByClothId(imageGroupId, clothId);
		return Result.success(true);
	}

	@PostMapping("/queryList")
	public Result<List<ImageGroupVO>> queryImageGroupList(@Valid @RequestBody ImageGroupQuery query){
		return Result.success(imageGroupService.queryImageGroupList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageGroupVO>> getImageGroupByPage(@Valid @RequestBody ImageGroupQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageGroupService.queryImageGroupByPage(query));
	}
}
