package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO;
import ai.conrain.aigc.platform.dal.example.SearchTaskExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.SearchTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;
import ai.conrain.aigc.platform.service.model.converter.SearchTaskConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.SearchTaskDAO;
import ai.conrain.aigc.platform.service.component.SearchTaskService;

/**   
 * SearchTaskService实现
 *
 * <AUTHOR>
 * @version SearchTaskService.java v 0.1 2025-08-16 05:50:47
 */
@Slf4j
@Service
public class SearchTaskServiceImpl implements SearchTaskService {

	/** DAO */
	@Autowired
	private SearchTaskDAO searchTaskDAO;

	@Override
	public SearchTaskVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		SearchTaskDO data = searchTaskDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return SearchTaskConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = searchTaskDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除SearchTask失败");
	}

	@Override
	public SearchTaskVO insert(SearchTaskVO searchTask) {
		AssertUtil.assertNotNull(searchTask, ResultCode.PARAM_INVALID, "searchTask is null");
		AssertUtil.assertTrue(searchTask.getId() == null, ResultCode.PARAM_INVALID, "searchTask.id is present");

		//创建时间、修改时间兜底
		if (searchTask.getCreateTime() == null) {
			searchTask.setCreateTime(new Date());
		}

		if (searchTask.getModifyTime() == null) {
			searchTask.setModifyTime(new Date());
		}

		SearchTaskDO data = SearchTaskConverter.vo2DO(searchTask);
		Integer n = searchTaskDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建SearchTask失败");
		AssertUtil.assertNotNull(data.getId(), "新建SearchTask返回id为空");
		searchTask.setId(data.getId());
		return searchTask;
	}


	@Override
	public void updateByIdSelective(SearchTaskVO searchTask) {
		AssertUtil.assertNotNull(searchTask, ResultCode.PARAM_INVALID, "searchTask is null");
    	AssertUtil.assertTrue(searchTask.getId() != null, ResultCode.PARAM_INVALID, "searchTask.id is null");

		//修改时间必须更新
		searchTask.setModifyTime(new Date());
		SearchTaskDO data = SearchTaskConverter.vo2DO(searchTask);
		int n = searchTaskDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新SearchTask失败，影响行数:" + n);
	}

	@Override
	public List<SearchTaskVO> querySearchTaskList(SearchTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		SearchTaskExample example = SearchTaskConverter.query2Example(query);

		List<SearchTaskDO> list = searchTaskDAO.selectByExample(example);
		return SearchTaskConverter.doList2VOList(list);
	}

	@Override
	public Long querySearchTaskCount(SearchTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		SearchTaskExample example = SearchTaskConverter.query2Example(query);
		return searchTaskDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询搜索记录历史
	 */
	@Override
	public PageInfo<SearchTaskVO> querySearchTaskByPage(SearchTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<SearchTaskVO> page = new PageInfo<>();

		SearchTaskExample example = SearchTaskConverter.query2Example(query);
		long totalCount = searchTaskDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<SearchTaskDO> list = searchTaskDAO.selectByExample(example);
		page.setList(SearchTaskConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}