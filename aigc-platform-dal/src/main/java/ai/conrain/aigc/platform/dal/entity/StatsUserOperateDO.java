package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户操作统计表（出图、下载）
 * 对应数据表：stats_user_operate
 */
@Data
public class StatsUserOperateDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * 用户 id
     */
    private Integer userId;

    /**
     * 服装 id
     */
    private Integer materialId;

    /**
     * 用户类型（主账户：MASTER  子账号：SUB）
     */
    private String userType;

    /**
     * 用户出图量
     */
    private Integer createCount;

    /**
     * 图片下载量
     */
    private Integer downloadCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 扩展字段
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}