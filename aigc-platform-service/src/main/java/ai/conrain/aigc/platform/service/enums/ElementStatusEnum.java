/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 创作元素状态枚举
 *
 * <AUTHOR>
 * @version : ElementStatusEnum.java, v 0.1 2024/6/25 21:13 renxiao.wu Exp $
 */
@Getter
public enum ElementStatusEnum {
    TEST("TEST", "测试中"),

    PROD("PROD", "生产上线"),

    REVIEW_FAILED("REVIEW_FAILED", "审核不通过"),

    ;

    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;

    private ElementStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static ElementStatusEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ElementStatusEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
