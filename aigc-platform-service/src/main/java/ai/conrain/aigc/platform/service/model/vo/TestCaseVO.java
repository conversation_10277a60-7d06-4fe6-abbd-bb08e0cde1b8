package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * TestCaseVO
 *
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
@Data
public class TestCaseVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** id */
	private Integer id;

	/** 测试用例名称 */
	private String name;

	/** 类型 */
	private TestCaseTypeEnum type;

	/** 用例数量 */
	private Integer caseNum;

	/** 上传用户id */
	private Integer userId;

	/** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

	/** 扩展 */
	private String extInfo;

}