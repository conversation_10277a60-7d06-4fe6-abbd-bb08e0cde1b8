package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * AB测试计划表
 * 对应数据表：test_plan
 */
@Data
public class TestPlanDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 测试计划名称
     */
    private String name;

    /**
     * 类型，TRAIN、CREATIVE
     */
    private String type;

    /**
     * 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED
     */
    private String status;

    /**
     * 备注
     */
    private String memo;

    /** 结论 */
    private String conclusion;

    /**
     * 归属用户id
     */
    private Integer userId;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /** 扩展信息 */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}