package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 系统配置表
 * 对应数据表：system_config
 */
@Data
public class SystemConfigDO implements Serializable {
    private static final long serialVersionUID = -8667396769263581577L;
    /**
     * id
     */
    private Integer id;

    /**
     * 配置key
     */
    private String confKey;

    /**
     * 配置值，状态非0时直接取该值
     */
    private String confValue;

    /**
     * 执行变更后的配置值
     */
    private String confValueNext;

    /**
     * 状态
     */
    private String status;

    /**
     * 变更生效时间
     */
    private Date effectTime;

    /** 备注 */
    private String memo;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}