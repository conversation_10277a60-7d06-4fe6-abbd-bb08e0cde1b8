package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 对应数据表：fixed_creative_template
 */
@Data
public class FixedCreativeTemplateDO implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 创建时间（收藏时间）
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 模板图片列表
     */
    private String templateList;

    /**
     * 扩展信息
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;
}