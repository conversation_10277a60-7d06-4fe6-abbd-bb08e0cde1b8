package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.InvoiceOrderDO;
import ai.conrain.aigc.platform.dal.example.InvoiceOrderExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InvoiceOrderDAO {
    long countByExample(InvoiceOrderExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(InvoiceOrderDO record);

    int insertSelective(InvoiceOrderDO record);

    List<InvoiceOrderDO> selectByExample(InvoiceOrderExample example);

    InvoiceOrderDO selectByPrimaryKey(Integer id);

    InvoiceOrderDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") InvoiceOrderDO record, @Param("example") InvoiceOrderExample example);

    int updateByExample(@Param("record") InvoiceOrderDO record, @Param("example") InvoiceOrderExample example);

    int updateByPrimaryKeySelective(InvoiceOrderDO record);

    int updateByPrimaryKey(InvoiceOrderDO record);

    int logicalDeleteByExample(@Param("example") InvoiceOrderExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}