package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.StatsUserPointVO;
import ai.conrain.aigc.platform.service.model.query.StatsUserPointQuery;
import ai.conrain.aigc.platform.service.component.StatsUserPointService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsUserPoint控制器
 *
 * <AUTHOR>
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsUserPoint")
public class StatsUserPointController {

	/** statsUserPointService */
	@Autowired
	private StatsUserPointService statsUserPointService;
	
	@GetMapping("/getById/{id}")
	public Result<StatsUserPointVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsUserPointService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody StatsUserPointVO statsUserPoint){
		try {
			StatsUserPointVO data = statsUserPointService.insert(statsUserPoint);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加用户点数消耗统计失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户点数消耗统计失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsUserPointService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsUserPointVO statsUserPoint){
		statsUserPointService.updateByIdSelective(statsUserPoint);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<StatsUserPointVO>> queryStatsUserPointList(@Valid @RequestBody StatsUserPointQuery query){
		return Result.success(statsUserPointService.queryStatsUserPointList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<StatsUserPointVO>> getStatsUserPointByPage(@Valid @RequestBody StatsUserPointQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsUserPointService.queryStatsUserPointByPage(query));
	}
}
