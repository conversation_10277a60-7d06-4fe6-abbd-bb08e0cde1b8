package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.WorkScheduleDO;
import ai.conrain.aigc.platform.dal.example.WorkScheduleExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WorkScheduleDAO {
    long countByExample(WorkScheduleExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(WorkScheduleDO record);

    int insertSelective(WorkScheduleDO record);

    List<WorkScheduleDO> selectByExample(WorkScheduleExample example);

    WorkScheduleDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") WorkScheduleDO record, @Param("example") WorkScheduleExample example);

    int updateByExample(@Param("record") WorkScheduleDO record, @Param("example") WorkScheduleExample example);

    int updateByPrimaryKeySelective(WorkScheduleDO record);

    int updateByPrimaryKey(WorkScheduleDO record);

    int deleteByIds(@Param("ids") List<Integer> ids);
}