package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应数据表：image_case_sync_record
 */
public class ImageCaseSyncRecordDO implements Serializable {
    /**
     * 主键 id
     */
    private Integer id;

    /**
     * 图片案例id
     */
    private Integer caseId;

    /**
     * 目标服务器 IP
     */
    private String targetServer;

    /**
     * 目标存储目录
     */
    private String targetStorePath;

    /**
     * 目标图片完整地址
     */
    private String imageUrl;

    /**
     * 图片上传时间
     */
    private Date uploadTime;

    /**
     * 是否同步成功
     */
    private Boolean isSuccess;

    /** 同步类型（badCase、精选图...） */
    private String syncType;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCaseId() {
        return caseId;
    }

    public void setCaseId(Integer caseId) {
        this.caseId = caseId;
    }

    public String getTargetServer() {
        return targetServer;
    }

    public void setTargetServer(String targetServer) {
        this.targetServer = targetServer == null ? null : targetServer.trim();
    }

    public String getTargetStorePath() {
        return targetStorePath;
    }

    public void setTargetStorePath(String targetStorePath) {
        this.targetStorePath = targetStorePath == null ? null : targetStorePath.trim();
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl == null ? null : imageUrl.trim();
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Boolean getIsSuccess() {
        return isSuccess;
    }

    public void setIsSuccess(Boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getSyncType() {
        return syncType;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }
}