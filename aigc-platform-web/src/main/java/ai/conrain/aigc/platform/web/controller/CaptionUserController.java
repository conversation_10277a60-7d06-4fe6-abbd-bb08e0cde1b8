package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.query.CaptionUserQuery;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CaptionUser控制器
 *
 * <AUTHOR>
 * @version CaptionUserService.java v 0.1 2025-08-12 12:55:30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/captionUser")
public class CaptionUserController {

	/** captionUserService */
	@Autowired
	private CaptionUserService captionUserService;
	
	@GetMapping("/getById/{id}")
	public Result<CaptionUserVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(captionUserService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody CaptionUserVO captionUser){
		return Result.success(captionUserService.create(captionUser).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		captionUserService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody CaptionUserVO captionUser){
		captionUserService.updateByIdSelective(captionUser);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<CaptionUserVO>> queryCaptionUserList(@Valid @RequestBody CaptionUserQuery query){
		return Result.success(captionUserService.queryCaptionUserList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<CaptionUserVO>> getCaptionUserByPage(@Valid @RequestBody CaptionUserQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(captionUserService.queryCaptionUserByPage(query));
	}
}