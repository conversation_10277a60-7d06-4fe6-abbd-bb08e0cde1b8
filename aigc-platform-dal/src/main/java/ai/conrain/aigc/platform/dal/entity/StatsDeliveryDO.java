/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * 统计交付情况
 *
 * <AUTHOR>
 * @version : StatsDeliveryDO.java, v 0.1 2024/9/14 11:52 renxiao.wu Exp $
 */
@Data
public class StatsDeliveryDO implements Serializable {
    private static final long serialVersionUID = -3865428999120076331L;
    /** 统计时间 */
    private String dt;
    /** 总数 */
    private Integer total;
    /** vip总数 */
    private Integer vipTotal;
    /** vip交付总数 */
    private Integer vipDelivery;
    /** 普通总数 */
    private Integer normalTotal;
    /** 普通交付总数 */
    private Integer normalDelivery;
    /** vip未知 */
    private Integer vipUnknown;
    /** vip24 */
    private Integer vip24;
    /** vip48 */
    private Integer vip48;
    /** vip其他 */
    private Integer vipOther;
    /** 普通未知 */
    private Integer normalUnknown;
    /** 普通24 */
    private Integer normal24;
    /** 普通48 */
    private Integer normal48;
    /** 普通其他 */
    private Integer normalOther;
    /** 自动交付数 */
    private Integer autoDelivery;
}
