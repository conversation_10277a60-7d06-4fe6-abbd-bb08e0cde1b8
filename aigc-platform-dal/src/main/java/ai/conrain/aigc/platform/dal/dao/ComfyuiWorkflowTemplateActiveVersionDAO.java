package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateActiveVersionDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ComfyuiWorkflowTemplateActiveVersionDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(ComfyuiWorkflowTemplateActiveVersionDO record);

    ComfyuiWorkflowTemplateActiveVersionDO selectByPrimaryKey(Integer id);

    List<ComfyuiWorkflowTemplateActiveVersionDO> selectAll();

    int updateByPrimaryKey(ComfyuiWorkflowTemplateActiveVersionDO record);

    ComfyuiWorkflowTemplateActiveVersionDO selectByTemplateKey(@Param("templateKey") String templateKey);

    List<ComfyuiWorkflowTemplateActiveVersionDO> batchSelectByTemplateKey(@Param("templateKeys") List<String> templateKeys);
}