package ai.conrain.aigc.platform.web.controller;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.UserPointLogService;
import ai.conrain.aigc.platform.service.component.annotation.Roles;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.UserPointLogQuery;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointUsageInfoVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.ADMIN;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DEMO_ACCOUNT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.DISTRIBUTOR;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.MERCHANT;
import static ai.conrain.aigc.platform.service.enums.RoleTypeEnum.OPERATOR;

/**
 * UserPointLog控制器
 *
 * <AUTHOR>
 * @version UserPointLogService.java v 0.1 2024-06-21 04:14:12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/userPointLog")
public class UserPointLogController {

    /** userPointLogService */
    @Autowired
    private UserPointLogService userPointLogService;

    @GetMapping("/getById/{id}")
    public Result<UserPointLogVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(userPointLogService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody UserPointLogVO userPointLog) {
        try {
            UserPointLogVO data = userPointLogService.insert(userPointLog);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加用户算力流水失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户算力流水失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        userPointLogService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody UserPointLogVO userPointLog) {
        userPointLogService.updateByIdSelective(userPointLog);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<UserPointLogVO>> queryUserPointLogList(@Valid @RequestBody UserPointLogQuery query) {
        return Result.success(userPointLogService.queryUserPointLogList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<UserPointLogVO>> getUserPointLogByPage(@Valid @RequestBody UserPointLogQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(userPointLogService.queryUserPointLogByPage(query));
    }

    /**
     * 前台分页查询积分使用记录
     * 主账号看全部的，子账号只看自己的
     *
     * @param query
     * @return
     */
    @PostMapping("/queryPointUsageInfoByPage")
    public Result<PageInfo<UserPointUsageInfoVO>> queryPointUsageInfoByPage(
        @Valid @RequestBody UserPointLogQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        // 如果指定 userId，则必须是管理员
        if (Objects.nonNull(query.getUserId())) {
            AssertUtil.assertTrue(OperationContextHolder.isAdmin(), ResultCode.ILLEGAL_PERMISSION, "无权限访问");
        }
        // 管理员可以查看全量数据, 其余只能查看自己的
        if (!OperationContextHolder.isAdmin()) {
            query.setUserId(OperationContextHolder.getMasterUserId());
        }
        List<String> typeList = new ArrayList<>(CreativeTypeEnum.getShowPointLogTypeList());
        typeList.add(PointLogTypeEnum.LORA_TRAIN.getCode());
        typeList.add(PointLogTypeEnum.LORA_TRAIN_RETURN.getCode());
        typeList.add(PointLogTypeEnum.ADJUST_MANUAL.getCode());
        typeList.add(PointLogTypeEnum.RECHARGE.getCode());
        typeList.add(PointLogTypeEnum.RECHARGE_MANUAL.getCode());
        query.setTypeList(typeList);

        //主账号看全部的，子账号只看自己的
        if (OperationContextHolder.isSubUser()) {
            query.setOperatorId(OperationContextHolder.getOperatorUserId());
        }

        return Result.success(userPointLogService.queryPointUsageInfoByPage(query));
    }

    @PostMapping("/exportPointUsage")
    @Roles({ADMIN, OPERATOR, DEMO_ACCOUNT, MERCHANT, DISTRIBUTOR})
    public void exportOrders(@Valid @RequestBody UserPointLogQuery query, HttpServletResponse response)
        throws IOException {
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        query.setPageNum(1);
        query.setPageSize(100000);
        Result<PageInfo<UserPointUsageInfoVO>> pageInfoResult = queryPointUsageInfoByPage(query);

        // 创建工作簿对象
        Workbook workbook = new XSSFWorkbook();

        // 在工作簿中创建工作表
        Sheet sheet = workbook.createSheet("导出结果");

        // 准备数据
        List<List<String>> data = new ArrayList<>();
        data.add(Arrays.asList("使用场景", "使用方式", "消耗数量", "创建时间", "使用账号"));

        if (pageInfoResult.getData() != null && CollectionUtils.isNotEmpty(pageInfoResult.getData().getList())) {

            for (UserPointUsageInfoVO info : pageInfoResult.getData().getList()) {
                data.add(Arrays.asList(info.getUsageScene(), info.getUsageWay(), buildConsumeAmount(info),
                    DateUtils.formatSimpleDate(info.getCreateTime()), info.getOperatorNickName()));
            }
        }

        int rowNum = 0;
        for (List<String> rowData : data) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            for (String field : rowData) {
                Cell cell = row.createCell(colNum++);
                cell.setCellValue(field);
            }
        }

        // 设置响应头信息
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=export.xlsx");

        // 将工作簿写入到响应输出流中
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 构建消耗数量
     *
     * @param info 使用信息
     * @return 消耗数量
     */
    private static String buildConsumeAmount(UserPointUsageInfoVO info) {
        String result = "";
        if (info.getUsedPoint() != null && !BigDecimalUtils.equalsZero(info.getUsedPoint())) {
            result += String.format("%s缪斯点", BigDecimalUtils.formatWith2Digits(info.getUsedPoint()));
        }

        if (info.getUsedExperiencePoint() != null) {
            result += (StringUtils.isNotBlank(result) ? " " : "") + String.format("%s体验点",
                info.getUsedExperiencePoint());
        }

        if (info.getUsedGivePoint() != null) {
            result += (StringUtils.isNotBlank(result) ? " " : "") + String.format("%s赠送点", info.getUsedGivePoint());
        }

        if (info.getUsedModelPoint() != null) {
            result += (StringUtils.isNotBlank(result) ? " " : "") + String.format("%s张创作图",
                info.getUsedModelPoint());
        }

        return result;
    }
}
