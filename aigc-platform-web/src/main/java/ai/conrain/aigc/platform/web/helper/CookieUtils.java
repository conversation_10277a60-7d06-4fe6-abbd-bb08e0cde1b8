package ai.conrain.aigc.platform.web.helper;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class CookieUtils {

    public static Cookie buildCookieWithExpire(String key, String val, int maxAge) {
        Cookie cookie = new Cookie(key, val);
        cookie.setPath("/");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(maxAge);

        return cookie;
    }

    /**
     * 删除cookie
     *
     * @param response
     * @param key
     * @param val
     */
    public static void deleteCookie(HttpServletResponse response, String key, String val) {
        response.addCookie(buildCookieWithExpire(key, val, 0));
    }

    /**
     * 获取cookie值
     *
     * @param key     cookie的key
     * @param request http请求
     */
    public static String fetchCookieValue(String key, HttpServletRequest request) {
        Cookie cookie = fetchCookie(key, request);
        if (cookie != null) {
            return cookie.getValue();
        }
        return null;
    }

    /**
     * 重置过期时间
     *
     * @param key      cookieKye
     * @param maxAge   最大过期时间
     * @param request  http请求
     * @param response http响应
     */
    public static void resetLoginedExpire(String key, String sessionId, int maxAge, HttpServletRequest request, HttpServletResponse response) {
        Cookie cookie = fetchCookie(key, sessionId, request);

        if (cookie != null) {
            //重新构建，不然取到的path("/")和HttpOnly(true)都不对
            cookie = CookieUtils.buildCookieWithExpire(cookie.getName(), cookie.getValue(),
                maxAge);

            log.info("当前用户操作有登录态，延长其cookie有效期{}", sessionId);
            //重置过期时间
            response.addCookie(cookie);
        }
    }

    /**
     * 获取cookie值
     *
     * @param key     cookie的key
     * @param request http请求
     */
    private static Cookie fetchCookie(String key, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        //查询cookie中的sessionId
        if (ArrayUtils.isEmpty(cookies)) {
            return null;
        }

        for (Cookie each : cookies) {
            if (StringUtils.equals(key, each.getName())) {
                return each;
            }
        }

        return null;
    }

    /**
     * 获取cookie值
     *
     * @param key     cookie的key
     * @param request http请求
     */
    private static Cookie fetchCookie(String key, String val, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        //查询cookie中的sessionId
        if (ArrayUtils.isEmpty(cookies)) {
            return null;
        }

        for (Cookie each : cookies) {
            if (StringUtils.equals(key, each.getName()) && StringUtils.equals(val, each.getValue())) {
                return each;
            }
        }

        return null;
    }
}