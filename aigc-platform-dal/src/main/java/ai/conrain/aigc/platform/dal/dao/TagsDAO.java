package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TagsDO;
import ai.conrain.aigc.platform.dal.example.TagsExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TagsDAO {
    long countByExample(TagsExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TagsDO record);

    int insertSelective(TagsDO record);

    List<TagsDO> selectByExampleWithBLOBs(TagsExample example);

    List<TagsDO> selectByExample(TagsExample example);

    TagsDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TagsDO record, @Param("example") TagsExample example);

    int updateByExampleWithBLOBs(@Param("record") TagsDO record, @Param("example") TagsExample example);

    int updateByExample(@Param("record") TagsDO record, @Param("example") TagsExample example);

    int updateByPrimaryKeySelective(TagsDO record);

    int updateByPrimaryKeyWithBLOBs(TagsDO record);

    int updateByPrimaryKey(TagsDO record);
}