package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.FixedCreativeTemplateDO;
import ai.conrain.aigc.platform.service.model.vo.FixedCreativeTemplateVO;
import ai.conrain.aigc.platform.service.model.query.FixedCreativeTemplateQuery;
import ai.conrain.aigc.platform.service.component.FixedCreativeTemplateService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * FixedCreativeTemplate控制器
 *
 * <AUTHOR>
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/fixedCreativeTemplate")
public class FixedCreativeTemplateController {

	/** fixedCreativeTemplateService */
	@Autowired
	private FixedCreativeTemplateService fixedCreativeTemplateService;
	
	@GetMapping("/getById/{id}")
	public Result<FixedCreativeTemplateVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(fixedCreativeTemplateService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody FixedCreativeTemplateVO fixedCreativeTemplate){
		try {
			FixedCreativeTemplateVO data = fixedCreativeTemplateService.insert(fixedCreativeTemplate);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		fixedCreativeTemplateService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody FixedCreativeTemplateVO fixedCreativeTemplate){
		fixedCreativeTemplateService.updateByIdSelective(fixedCreativeTemplate);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<FixedCreativeTemplateVO>> queryFixedCreativeTemplateList(@Valid @RequestBody FixedCreativeTemplateQuery query){
		return Result.success(fixedCreativeTemplateService.queryFixedCreativeTemplateList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<FixedCreativeTemplateVO>> getFixedCreativeTemplateByPage(@Valid @RequestBody FixedCreativeTemplateQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(fixedCreativeTemplateService.queryFixedCreativeTemplateByPage(query));
	}
}
