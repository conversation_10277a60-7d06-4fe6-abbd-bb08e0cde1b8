package ai.conrain.aigc.platform.service.util;

import java.util.HashMap;
import java.util.Map;

/**
 * Map工具类，提供扁平化和嵌套化功能
 */
public class MapUtil {
    
    /**
     * 默认分隔符
     */
    private static final String DEFAULT_SEPARATOR = ".";

    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * 将嵌套的Map扁平化为点分格式的键值对
     * 使用默认分隔符 "."
     * 
     * @param nestedMap 嵌套的Map
     * @return 扁平化后的Map
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap) {
        return flatten(nestedMap, DEFAULT_SEPARATOR);
    }
    
    /**
     * 将嵌套的Map扁平化为指定分隔符格式的键值对
     * 
     * @param nestedMap 嵌套的Map
     * @param separator 分隔符
     * @return 扁平化后的Map
     */
    public static Map<String, Object> flatten(Map<String, Object> nestedMap, String separator) {
        Map<String, Object> flatMap = new HashMap<>();
        flattenRecursive(nestedMap, "", separator, flatMap);
        return flatMap;
    }
    
    /**
     * 递归扁平化Map
     * 
     * @param map 当前处理的Map
     * @param prefix 键前缀
     * @param separator 分隔符
     * @param result 结果Map
     */
    @SuppressWarnings("unchecked")
    private static void flattenRecursive(Map<String, Object> map, String prefix, String separator, Map<String, Object> result) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            String newKey = prefix.isEmpty() ? key : prefix + separator + key;
            
            if (value instanceof Map) {
                // 如果值是Map，递归处理
                flattenRecursive((Map<String, Object>) value, newKey, separator, result);
            } else {
                // 如果值不是Map，直接添加到结果中
                result.put(newKey, value);
            }
        }
    }
    
    /**
     * 将扁平化的点分格式键值对转换为嵌套的Map
     * 使用默认分隔符 "."
     * 
     * @param flatMap 扁平化的Map
     * @return 嵌套的Map
     */
    public static Map<String, Object> nest(Map<String, Object> flatMap) {
        return nest(flatMap, DEFAULT_SEPARATOR);
    }
    
    /**
     * 将扁平化的指定分隔符格式键值对转换为嵌套的Map
     * 
     * @param flatMap 扁平化的Map
     * @param separator 分隔符
     * @return 嵌套的Map
     */
    public static Map<String, Object> nest(Map<String, Object> flatMap, String separator) {
        Map<String, Object> nestedMap = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : flatMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            setNestedValue(nestedMap, key, value, separator);
        }
        
        return nestedMap;
    }
    
    /**
     * 在嵌套Map中设置值
     * 
     * @param map 目标Map
     * @param key 点分格式的键
     * @param value 值
     * @param separator 分隔符
     */
    @SuppressWarnings("unchecked")
    private static void setNestedValue(Map<String, Object> map, String key, Object value, String separator) {
        String[] keys = key.split(separator);
        Map<String, Object> currentMap = map;
        
        // 遍历到倒数第二个键
        for (int i = 0; i < keys.length - 1; i++) {
            String currentKey = keys[i];
            
            if (!currentMap.containsKey(currentKey)) {
                currentMap.put(currentKey, new HashMap<String, Object>());
            }
            
            Object currentValue = currentMap.get(currentKey);
            if (!(currentValue instanceof Map)) {
                // 如果当前值不是Map，创建一个新的Map
                currentValue = new HashMap<String, Object>();
                currentMap.put(currentKey, currentValue);
            }
            
            currentMap = (Map<String, Object>) currentValue;
        }
        
        // 设置最后一个键的值
        currentMap.put(keys[keys.length - 1], value);
    }
}
