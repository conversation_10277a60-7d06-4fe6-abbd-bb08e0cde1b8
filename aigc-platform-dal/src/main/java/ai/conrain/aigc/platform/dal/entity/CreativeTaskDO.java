package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 创作任务表
 * 对应数据表：creative_task
 */
@Data
public class CreativeTaskDO implements Serializable {
    /**
     * 任务id
     */
    private Integer id;

    /**
     * 批次id
     */
    private Integer batchId;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 模型id
     */
    private Integer modelId;

    /** 类型 */
    private String type;

    /**
     * 图片比例，3:4、1:1等
     */
    private String imageProportion;

    /**
     * 批次数量
     */
    private Integer batchCnt;

    /**
     * ComfyUI返回的唯一标识
     */
    private String promptId;

    /**
     * 结果图片路径
     */
    private String resultPath;

    /**
     * 状态，INIT、QUEUE、PROCESSING、FINISHED、FAILED
     */
    private String status;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * aigc请求参数
     */
    private String aigcRequest;

    /**
     * 结果图片url列表
     */
    private String resultImages;

    /**
     * 扩展信息
     */
    private String extInfo;

    //comfyui tpl info
    private String tplInfo;

    /**
     * 前置任务 ID
     */
    private Integer preTaskId;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}