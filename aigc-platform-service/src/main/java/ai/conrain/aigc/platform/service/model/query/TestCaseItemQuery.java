package ai.conrain.aigc.platform.service.model.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TestCaseItemQuery
 *
 * @version TestCaseItemService.java v 0.1 2025-08-12 07:18:08
 */
@Data
public class TestCaseItemQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试用例id */
    private Integer caseId;

    /** 测试用例项名称 */
    private String name;

    /** 类型 */
    private String type;

    /** 执行次数 */
    private Integer runTimes;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}