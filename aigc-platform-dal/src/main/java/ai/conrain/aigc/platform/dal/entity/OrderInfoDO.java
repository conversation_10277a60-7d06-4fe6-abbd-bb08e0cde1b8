package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单表
 * 对应数据表：order_info
 */
@Data
public class OrderInfoDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 订单号
     */
    private String orderNo;

    // 充值账号关联的公司名称（由masterUserId关联查询得到）
    private String masterCorpName;

    // 渠道商master id
    private Integer distributorMasterUserId;

    // 渠道商
    private String distributorCorpName;

    // 渠道商corp id
    private Integer distributorCorpOrgId;

    /**
     * 关联的主用户id
     */
    private Integer masterUserId;

    /**
     * 关联的主用户昵称快照
     */
    private String masterUserNick;

    /**
     * 关联的主用户登录账号快照
     */
    private String masterUserLoginId;

    /**
     * 关联的操作员用户id
     */
    private Integer operatorUserId;

    /**
     * 关联的操作员用户昵称快照
     */
    private String operatorUserNick;

    /**
     * 关联的操作员用户登录账号快照
     */
    private String operatorUserLoginId;

    /**
     * 订单原始金额，不可修改（单位元）
     */
    private BigDecimal originalAmount;

    /**
     * 订单支付金额
     */
    private BigDecimal payAmount;

    /**
     * 支付信息
     */
    private String payDetail;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 产品码
     */
    private String productCode;

    /**
     * 产品详情
     */
    private String productDetail;

    /**
     * 订单完结时间
     */
    private Date finishTime;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 开票状态
     */
    private String invoiceStatus;

    /**
     * 发票文件url
     */
    private String invoiceFileUrl;

    /**
     * 销售id
     */
    private Integer distributorSalesUserId;

    /**
     * 销售昵称
     */
    private String distributorSalesNickName;

    // 渠道商销售组织id
    private Integer distributorSalesOrgId;
    private String distributorSalesOrgName;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}