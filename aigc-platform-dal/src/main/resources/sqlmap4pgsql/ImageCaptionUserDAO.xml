<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionUserDAO">
  <resultMap id="BaseResultMap" type="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="image_id" jdbcType="INTEGER" property="imageId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="original_id" jdbcType="INTEGER" property="originalId" />
    <result column="caption" jdbcType="OTHER" property="caption" />
    <result column="caption_version" jdbcType="VARCHAR" property="captionVersion" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, image_id, user_id, original_id, caption, caption_version, create_time, modify_time, 
    deleted
  </sql>
  <select id="selectByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from image_caption_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        LIMIT ${rows} OFFSET ${offset}
      </if>
      <if test="offset == null">
        LIMIT ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image_caption_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from image_caption_user
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        true
      </when>
      <otherwise>
        false
      </otherwise>
    </choose>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from image_caption_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO" useGeneratedKeys="true">
    insert into image_caption_user (image_id, user_id, original_id, 
      caption, caption_version, create_time, 
      modify_time, deleted)
    values (#{imageId,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{originalId,jdbcType=INTEGER}, 
      #{caption,jdbcType=OTHER}, #{captionVersion,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO" useGeneratedKeys="true">
    insert into image_caption_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageId != null">
        image_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="originalId != null">
        original_id,
      </if>
      <if test="caption != null">
        caption,
      </if>
      <if test="captionVersion != null">
        caption_version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageId != null">
        #{imageId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        #{originalId,jdbcType=INTEGER},
      </if>
      <if test="caption != null">
        #{caption,jdbcType=OTHER},
      </if>
      <if test="captionVersion != null">
        #{captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="ai.conrain.aigc.platform.dal.example.ImageCaptionUserExample" resultType="java.lang.Long">
    select count(*) from image_caption_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update image_caption_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.imageId != null">
        image_id = #{record.imageId,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.originalId != null">
        original_id = #{record.originalId,jdbcType=INTEGER},
      </if>
      <if test="record.caption != null">
         caption = COALESCE(caption, '{}') || #{record.caption,jdbcType=OTHER},
      </if>
      <if test="record.captionVersion != null">
        caption_version = #{record.captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifyTime != null">
        modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update image_caption_user
    set id = #{record.id,jdbcType=INTEGER},
      image_id = #{record.imageId,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      original_id = #{record.originalId,jdbcType=INTEGER},
       caption = COALESCE(caption, '{}') || #{record.caption,jdbcType=OTHER},
      caption_version = #{record.captionVersion,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      modify_time = #{record.modifyTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO">
    update image_caption_user
    <set>
      <if test="imageId != null">
        image_id = #{imageId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=INTEGER},
      </if>
      <if test="caption != null">
        caption = COALESCE(caption, '{}') || #{caption,jdbcType=OTHER},
      </if>
      <if test="captionVersion != null">
        caption_version = #{captionVersion,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO">
    update image_caption_user
    set image_id = #{imageId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      original_id = #{originalId,jdbcType=INTEGER},
       caption = COALESCE(caption, '{}') || #{caption,jdbcType=OTHER},
      caption_version = #{captionVersion,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="logicalDeleteByExample" parameterType="map">
    update image_caption_user set deleted = true
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update image_caption_user set deleted = true
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>