package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户组织关系
 * 对应数据表：user_organization
 */
public class UserOrganizationDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 用户（主账号或操作员）id
     */
    private Integer userId;

    /**
     * 组织id
     */
    private Integer orgId;

    /**
     * 创建人操作员账号id
     */
    private Integer creatorOperatorUserId;

    /**
     * 修改人操作员账号id
     */
    private Integer modifierOperatorUserId;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getCreatorOperatorUserId() {
        return creatorOperatorUserId;
    }

    public void setCreatorOperatorUserId(Integer creatorOperatorUserId) {
        this.creatorOperatorUserId = creatorOperatorUserId;
    }

    public Integer getModifierOperatorUserId() {
        return modifierOperatorUserId;
    }

    public void setModifierOperatorUserId(Integer modifierOperatorUserId) {
        this.modifierOperatorUserId = modifierOperatorUserId;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}