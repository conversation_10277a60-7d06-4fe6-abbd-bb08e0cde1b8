package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 对应数据表：workflow_task
 */
@Data
public class WorkflowTaskDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的业务id
     */
    private Integer bizId;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 操作人
     */
    private Integer operatorId;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 拓展字段
     */
    private String meta;

    private static final long serialVersionUID = 1L;
}