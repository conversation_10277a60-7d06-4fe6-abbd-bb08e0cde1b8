package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserFavorDO;
import ai.conrain.aigc.platform.dal.example.UserFavorExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserFavorDAO {
    long countByExample(UserFavorExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(UserFavorDO record);

    int insertSelective(UserFavorDO record);

    List<UserFavorDO> selectByExampleWithBLOBs(UserFavorExample example);

    List<UserFavorDO> selectByExample(UserFavorExample example);

    UserFavorDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") UserFavorDO record, @Param("example") UserFavorExample example);

    int updateByExampleWithBLOBs(@Param("record") UserFavorDO record, @Param("example") UserFavorExample example);

    int updateByExample(@Param("record") UserFavorDO record, @Param("example") UserFavorExample example);

    int updateByPrimaryKeySelective(UserFavorDO record);

    int updateByPrimaryKeyWithBLOBs(UserFavorDO record);

    int updateByPrimaryKey(UserFavorDO record);

    long count4Model(UserFavorDO record);

    List<UserFavorDO> select4ModelByExample(UserFavorExample example);

    List<UserFavorDO> select4Model(UserFavorExample example);
}