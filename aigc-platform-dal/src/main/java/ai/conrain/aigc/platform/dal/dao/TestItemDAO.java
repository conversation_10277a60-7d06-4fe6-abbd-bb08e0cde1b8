package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TestItemDO;
import ai.conrain.aigc.platform.dal.example.TestItemExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestItemDAO {
    long countByExample(TestItemExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TestItemDO record);

    int insertSelective(TestItemDO record);

    List<TestItemDO> selectByExampleWithBLOBs(TestItemExample example);

    List<TestItemDO> selectByExample(TestItemExample example);

    TestItemDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TestItemDO record, @Param("example") TestItemExample example);

    int updateByExampleWithBLOBs(@Param("record") TestItemDO record, @Param("example") TestItemExample example);

    int updateByExample(@Param("record") TestItemDO record, @Param("example") TestItemExample example);

    int updateByPrimaryKeySelective(TestItemDO record);

    int updateByPrimaryKeyWithBLOBs(TestItemDO record);

    int updateByPrimaryKey(TestItemDO record);
}