/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 测试用例类型
 *
 * <AUTHOR>
 * @version : TestCaseTypeEnum.java, v 0.1 2025/8/12 19:12 renxiao.wu Exp $
 */
@Getter
public enum TestCaseTypeEnum {
    PROMPT_CREATE_IMAGE("PROMPT_CREATE_IMAGE", "纯prompt出图"),
    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    private TestCaseTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static TestCaseTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (TestCaseTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}
