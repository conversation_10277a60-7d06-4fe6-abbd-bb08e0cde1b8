package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ImageCaseDO;
import ai.conrain.aigc.platform.dal.entity.ImageCaseTagDO;
import ai.conrain.aigc.platform.dal.example.ImageCaseExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageCaseDAO {
    long countByExample(ImageCaseExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageCaseDO record);

    int insertSelective(ImageCaseDO record);

    List<ImageCaseDO> selectByExample(ImageCaseExample example);

    ImageCaseDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ImageCaseDO record, @Param("example") ImageCaseExample example);

    int updateByExample(@Param("record") ImageCaseDO record, @Param("example") ImageCaseExample example);

    int updateByPrimaryKeySelective(ImageCaseDO record);

    int updateByPrimaryKey(ImageCaseDO record);

    int insertTag(ImageCaseTagDO record);

    int deleteTag(ImageCaseTagDO record);
}