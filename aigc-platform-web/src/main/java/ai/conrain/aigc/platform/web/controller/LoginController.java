/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.controller;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.acm.AcmConfigService;
import ai.conrain.aigc.platform.service.component.SecurityService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.LoginRequest;
import ai.conrain.aigc.platform.service.model.request.RegisterRequest;
import ai.conrain.aigc.platform.service.model.request.SmsLoginRequest;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import ai.conrain.aigc.platform.web.helper.SessionHelper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @version : LoginController.java, v 0.1 2024/1/20 19:49 renxiao.wu Exp $
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/login")
public class LoginController {

    @Autowired
    private UserService userService;
    @Autowired
    private SessionHelper sessionHelper;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private TairService tairService;

    @PostMapping("/checkMobile")
    public Result<Boolean> checkMobile(@JsonArg @NotBlank String mobile) {
        return Result.success(userService.loginMobileCheck(mobile));
    }

    @PostMapping("/mobileAuth")
    public Result<UserVO> loginByMobileAuth(@NotBlank @JsonArg String appId, @NotBlank @JsonArg String code) {
        return handleLoginSuccess(userService.loginByMobileAuth(appId, code), false);
    }

    @PostMapping("/sms")
    public Result<UserVO> smsLogin(@Valid @RequestBody SmsLoginRequest request) {
        return handleLoginSuccess(userService.loginBySms(request), false);
    }

    @PostMapping("/register")
    public Result<UserVO> register(@Valid @RequestBody RegisterRequest request) {
        return handleLoginSuccess(userService.register(request), false);
    }

    @PostMapping("/fetchSalt")
    @JsonIgnore
    public Result<String> fetchSalt() {
        String salt = securityService.genRandomKey();

        //存储到tair中
        tairService.setString(getSaltTairKey(), salt, CommonConstants.PC_SESSION_EXPIRE_TIME);

        return Result.success(salt);
    }

    @PostMapping("/pswd")
    public Result<UserVO> loginBack(@Valid @RequestBody LoginRequest request) {
        String salt = tairService.getString(getSaltTairKey());
        if (StringUtils.isBlank(salt)) {
            log.error("非法请求，执行登录缺少salt，request={}", request);
            return Result.failedWithMessage(ResultCode.SYS_ERROR, "未找到盐值");
        }
        //对前台返回的密码进行解码
        String decrypt = securityService.decrypt(request.getPswd(), salt);
        //重置解密后的密码

        return handleLoginSuccess(userService.loginByPswd(request.getLoginId(), decrypt), true);
    }

    @PostMapping("/restPassword")
    public Result<?> restPassword(@JsonArg @NotBlank String newPassword) {
        String mobile = OperationContextHolder.getContext().getAttribute(CommonConstants.TAIR_REST_PSWD_MOBLE);
        if (StringUtils.isBlank(mobile)) {
            log.warn("mobile已过期，需要重试");
            return Result.failedWithMessage(ResultCode.RESET_PSWD_EXPIRED, "未找到手机号");
        }

        String salt = tairService.getString(getSaltTairKey());
        if (StringUtils.isBlank(salt)) {
            log.warn("salt已过期，需要重试");
            return Result.failedWithMessage(ResultCode.RESET_PSWD_EXPIRED, "未找到盐值");
        }
        //对前台返回的密码进行解码
        String decrypt = securityService.decrypt(newPassword, salt);

        if (!CommonConstants.PASSWORD_PATTERN.matcher(decrypt).matches()) {
            log.warn("重置密码失败，密码格式不正确");
            return Result.failedWithMessage(ResultCode.ILLEGAL_PASSWORD_TYPE, "密码过于简单");
        }

        userService.resetPassword(mobile, decrypt);
        return Result.success();
    }

    @PostMapping("/back/restPassword")
    public Result<?> restPassword(@JsonArg @NotNull Integer userId, @JsonArg @NotBlank String newPassword) {
        String salt = tairService.getString(getSaltTairKey());
        if (StringUtils.isBlank(salt)) {
            log.warn("salt已过期，需要重试");
            return Result.failedWithMessage(ResultCode.RESET_PSWD_EXPIRED, "未找到盐值");
        }
        //对前台返回的密码进行解码
        String decrypt = securityService.decrypt(newPassword, salt);

        if (!CommonConstants.PASSWORD_PATTERN.matcher(decrypt).matches()) {
            log.warn("重置密码失败，密码格式不正确");
            return Result.failedWithMessage(ResultCode.ILLEGAL_PASSWORD_TYPE, "密码过于简单");
        }

        userService.resetPassword(userId, decrypt);
        return Result.success();
    }

    @PostMapping("/logout")
    public Result<?> logout(HttpServletResponse response) {
        sessionHelper.clear(response);
        return Result.success();
    }

    @GetMapping("/status")
    public Result<?> status() {
        RoleTypeEnum roleType = OperationContextHolder.getRoleType();
        return Result.success(roleType == null ? RoleTypeEnum.NONE : roleType);
    }

    /**
     * 获取盐值的tair key
     *
     * @return 盐值tair key
     */
    private String getSaltTairKey() {
        String sessionId = sessionHelper.fetchSessionId();
        return CommonConstants.TAIR_KEY_PREFIX + CommonConstants.SALT_KEY_PREFIX + sessionId;
    }

    /**
     * 处理登录成功后的通用逻辑
     *
     * @param result 登录结果
     * @param needClearSalt 是否需要清理salt缓存
     * @return 处理后的结果
     */
    private Result<UserVO> handleLoginSuccess(Result<UserVO> result, boolean needClearSalt) {
        if (result.isSuccess() && result.getData() != null) {
            //登录后将用户信息保存到session中
            sessionHelper.saveLoginSession(result.getData());

            if (needClearSalt) {
                //清理salt缓存
                tairService.clear(getSaltTairKey());
            }

            result.getData().setMobile(SecurityUtils.maskPhoneNumber(result.getData().getMobile()));
            result.getData().setLoginId(SecurityUtils.maskPhoneNumber(result.getData().getLoginId()));
            result.getData().setMasterLoginId(SecurityUtils.maskPhoneNumber(result.getData().getMasterLoginId()));
        }
        return result;
    }
}
