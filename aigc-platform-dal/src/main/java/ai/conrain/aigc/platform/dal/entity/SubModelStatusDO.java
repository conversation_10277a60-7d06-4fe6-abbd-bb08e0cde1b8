/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 子模型状态DO
 *
 * <AUTHOR>
 * @version : SubModelStatusDO.java, v 0.1 2025/5/13 00:01 renxiao.wu Exp $
 */
@Data
public class SubModelStatusDO implements Serializable {
    private static final long serialVersionUID = -3568504105958170787L;
    /** 主模型id */
    private Integer mainId;
    /** 需要确认的数量 */
    private Integer needConfirmCnt;
    /** 审核中数量 */
    private Integer testingCnt;
    /** 子模型总数 */
    private Integer total;
}
