package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 对应数据表：train_param
 */
public class TrainParamDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 关联的训练计划ID
     */
    private Integer trainPlanId;

    /**
     * 训练分辨率
     */
    private String trainResolution;

    /**
     * 学习内容或风格
     */
    private String contentOrStyle;

    /**
     * 关联的loraModelId
     */
    private Integer relatedLoraModelId;

    /**
     * 关联的loraModelName
     */
    private String relatedLoraModelName;

    /**
     * Rank值
     */
    private Integer loraRank;

    /**
     * Alpha值
     */
    private Integer alpha;

    /**
     * 训练步数
     */
    private Integer trainStep;

    /**
     * 学习率
     */
    private BigDecimal lr;

    /**
     * Dropout值
     */
    private BigDecimal dropout;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTrainPlanId() {
        return trainPlanId;
    }

    public void setTrainPlanId(Integer trainPlanId) {
        this.trainPlanId = trainPlanId;
    }

    public String getTrainResolution() {
        return trainResolution;
    }

    public void setTrainResolution(String trainResolution) {
        this.trainResolution = trainResolution == null ? null : trainResolution.trim();
    }

    public String getContentOrStyle() {
        return contentOrStyle;
    }

    public void setContentOrStyle(String contentOrStyle) {
        this.contentOrStyle = contentOrStyle == null ? null : contentOrStyle.trim();
    }

    public Integer getRelatedLoraModelId() {
        return relatedLoraModelId;
    }

    public void setRelatedLoraModelId(Integer relatedLoraModelId) {
        this.relatedLoraModelId = relatedLoraModelId;
    }

    public String getRelatedLoraModelName() {
        return relatedLoraModelName;
    }

    public void setRelatedLoraModelName(String relatedLoraModelName) {
        this.relatedLoraModelName = relatedLoraModelName == null ? null : relatedLoraModelName.trim();
    }

    public Integer getLoraRank() {
        return loraRank;
    }

    public void setLoraRank(Integer loraRank) {
        this.loraRank = loraRank;
    }

    public Integer getAlpha() {
        return alpha;
    }

    public void setAlpha(Integer alpha) {
        this.alpha = alpha;
    }

    public Integer getTrainStep() {
        return trainStep;
    }

    public void setTrainStep(Integer trainStep) {
        this.trainStep = trainStep;
    }

    public BigDecimal getLr() {
        return lr;
    }

    public void setLr(BigDecimal lr) {
        this.lr = lr;
    }

    public BigDecimal getDropout() {
        return dropout;
    }

    public void setDropout(BigDecimal dropout) {
        this.dropout = dropout;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}