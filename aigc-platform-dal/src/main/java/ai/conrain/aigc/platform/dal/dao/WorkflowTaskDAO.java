package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.WorkflowTaskDO;
import ai.conrain.aigc.platform.dal.example.WorkflowTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WorkflowTaskDAO {
    long countByExample(WorkflowTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(WorkflowTaskDO record);

    int insertSelective(WorkflowTaskDO record);

    List<WorkflowTaskDO> selectByExampleWithBLOBs(WorkflowTaskExample example);

    List<WorkflowTaskDO> selectByExample(WorkflowTaskExample example);

    WorkflowTaskDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") WorkflowTaskDO record, @Param("example") WorkflowTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") WorkflowTaskDO record, @Param("example") WorkflowTaskExample example);

    int updateByExample(@Param("record") WorkflowTaskDO record, @Param("example") WorkflowTaskExample example);

    int updateByPrimaryKeySelective(WorkflowTaskDO record);

    int updateByUniqueKeySelective(WorkflowTaskDO record);

    int updateByPrimaryKeyWithBLOBs(WorkflowTaskDO record);

    int updateByPrimaryKey(WorkflowTaskDO record);
}