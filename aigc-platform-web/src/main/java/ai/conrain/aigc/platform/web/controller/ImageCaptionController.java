package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ImageCaption控制器
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-08-06 06:04:01
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageCaption")
public class ImageCaptionController {

	/** imageCaptionService */
	@Autowired
	private ImageCaptionService imageCaptionService;
	
	@GetMapping("/getById/{id}")
	public Result<ImageCaptionVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageCaptionService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageCaptionVO imageCaption){
		return Result.success(imageCaptionService.insert(imageCaption).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageCaptionService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageCaptionVO imageCaption){
		imageCaptionService.updateByIdSelective(imageCaption);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<ImageCaptionVO>> queryImageCaptionList(@Valid @RequestBody ImageCaptionQuery query){
		return Result.success(imageCaptionService.queryImageCaptionList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageCaptionVO>> getImageCaptionByPage(@Valid @RequestBody ImageCaptionQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageCaptionService.queryImageCaptionByPage(query));
	}
}