package ai.conrain.aigc.platform.web.controller;

import java.util.*;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.model.request.WorkScheduleBatchRequest;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.component.WorkScheduleService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;

import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * WorkSchedule控制器
 *
 * <AUTHOR>
 * @version WorkScheduleService.java v 0.1 2025-04-01 03:40:32
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/workSchedule")
public class WorkScheduleController {

	/** workScheduleService */
	@Autowired
	private WorkScheduleService workScheduleService;
	
	@GetMapping("/getById/{id}")
	public Result<WorkScheduleVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(workScheduleService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<WorkScheduleVO> create(@Valid @RequestBody WorkScheduleVO workSchedule){
		try {
			WorkScheduleVO data = workScheduleService.insert(workSchedule);
			if (data != null) {
				return Result.success(data);
			}
		} catch (Exception e) {
			log.error("添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		workScheduleService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody WorkScheduleVO workSchedule){
		workScheduleService.updateByIdSelective(workSchedule);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<WorkScheduleVO>> queryWorkScheduleList(@Valid @RequestBody WorkScheduleQuery query){
		return Result.success(workScheduleService.queryWorkScheduleList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<WorkScheduleVO>> getWorkScheduleByPage(@Valid @RequestBody WorkScheduleQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		query.setOrderBy("start_time desc");
		return Result.success(workScheduleService.queryWorkScheduleByPage(query));
	}
	
	@PostMapping("/batchCreate")
	public Result<List<WorkScheduleVO>> batchCreate(@Valid @RequestBody WorkScheduleBatchRequest request) {
		try {
			if (CollectionUtils.isEmpty(request.getList())) {
				return Result.failedWithMessage(ResultCode.PARAM_INVALID, "批量新建请求参数为空");
			}
			
			List<WorkScheduleVO> data = workScheduleService.batchInsert(request.getList());
			if (data != null && !data.isEmpty()) {
				return Result.success(data);
			}
		} catch (Exception e) {
			log.error("批量添加失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "批量新建失败");
	}

	@PostMapping("/batchDelete")
	public Result<?> batchDelete(@Valid @RequestBody WorkScheduleBatchRequest request) {
		if (CollectionUtils.isEmpty(request.getList())) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "批量删除请求参数为空");
		}
		List<Integer> ids = request.getList().stream().map(WorkScheduleVO::getId).collect(Collectors.toList());
		workScheduleService.batchDeleteByIds(ids);
		return Result.success();
	}
}
