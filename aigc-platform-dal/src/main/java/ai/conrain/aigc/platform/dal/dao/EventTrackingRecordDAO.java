package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.EventTrackingRecordDO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EventTrackingRecordDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(EventTrackingRecordDO record);

    EventTrackingRecordDO selectByPrimaryKey(Integer id);

    List<EventTrackingRecordDO> selectAll();

    int updateByPrimaryKey(EventTrackingRecordDO record);

    /**
     * 批量插入
     *
     * @param list 集合列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<EventTrackingRecordDO> list);

}