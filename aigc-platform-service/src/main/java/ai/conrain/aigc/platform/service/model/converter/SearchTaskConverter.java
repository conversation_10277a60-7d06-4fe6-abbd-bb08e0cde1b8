package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.SearchTaskDO;
import ai.conrain.aigc.platform.service.model.query.SearchTaskQuery;
import ai.conrain.aigc.platform.dal.example.SearchTaskExample;
import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * SearchTaskConverter
 *
 * @version SearchTaskService.java v 0.1 2025-08-16 05:50:47
 */
public class SearchTaskConverter {

    /**
     * DO -> VO
     */
    public static SearchTaskVO do2VO(SearchTaskDO from) {
        SearchTaskVO to = new SearchTaskVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setUserSessionId(from.getUserSessionId());
        to.setUserSessionTaskId(from.getUserSessionTaskId());
        to.setClothImgDetail(from.getClothImgDetail());
        to.setClothAnalysis(from.getClothAnalysis());
        to.setRefImgDetail(from.getRefImgDetail());
        to.setRefImgAnalysis(from.getRefImgAnalysis());
        to.setExcludeRetBatch(from.getExcludeRetBatch());
        to.setSpecifiedGenres(from.getSpecifiedGenres());
        to.setSpecifiedRetImgId(from.getSpecifiedRetImgId());
        to.setExtSearchParams(from.getExtSearchParams());
        to.setStatus(from.getStatus());
        to.setSearchRetBatch(from.getSearchRetBatch());
        to.setSearchRetBatchSummary(from.getSearchRetBatchSummary());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static SearchTaskDO vo2DO(SearchTaskVO from) {
        SearchTaskDO to = new SearchTaskDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setUserSessionId(from.getUserSessionId());
        to.setUserSessionTaskId(from.getUserSessionTaskId());
        to.setClothImgDetail(from.getClothImgDetail());
        to.setClothAnalysis(from.getClothAnalysis());
        to.setRefImgDetail(from.getRefImgDetail());
        to.setRefImgAnalysis(from.getRefImgAnalysis());
        to.setExcludeRetBatch(from.getExcludeRetBatch());
        to.setSpecifiedGenres(from.getSpecifiedGenres());
        to.setSpecifiedRetImgId(from.getSpecifiedRetImgId());
        to.setExtSearchParams(from.getExtSearchParams());
        to.setStatus(from.getStatus());
        to.setSearchRetBatch(from.getSearchRetBatch());
        to.setSearchRetBatchSummary(from.getSearchRetBatchSummary());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static SearchTaskExample query2Example(SearchTaskQuery from) {
        SearchTaskExample to = new SearchTaskExample();
        SearchTaskExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getUserSessionId())) {
            c.andUserSessionIdEqualTo(from.getUserSessionId());
        }
        if (!ObjectUtils.isEmpty(from.getUserSessionTaskId())) {
            c.andUserSessionTaskIdEqualTo(from.getUserSessionTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getClothImgDetail())) {
            c.andClothImgDetailEqualTo(from.getClothImgDetail());
        }
        if (!ObjectUtils.isEmpty(from.getClothAnalysis())) {
            c.andClothAnalysisEqualTo(from.getClothAnalysis());
        }
        if (!ObjectUtils.isEmpty(from.getRefImgDetail())) {
            c.andRefImgDetailEqualTo(from.getRefImgDetail());
        }
        if (!ObjectUtils.isEmpty(from.getRefImgAnalysis())) {
            c.andRefImgAnalysisEqualTo(from.getRefImgAnalysis());
        }
        if (!ObjectUtils.isEmpty(from.getExcludeRetBatch())) {
            c.andExcludeRetBatchEqualTo(from.getExcludeRetBatch());
        }
        if (!ObjectUtils.isEmpty(from.getSpecifiedGenres())) {
            c.andSpecifiedGenresEqualTo(from.getSpecifiedGenres());
        }
        if (!ObjectUtils.isEmpty(from.getSpecifiedRetImgId())) {
            c.andSpecifiedRetImgIdEqualTo(from.getSpecifiedRetImgId());
        }
        if (!ObjectUtils.isEmpty(from.getExtSearchParams())) {
            c.andExtSearchParamsEqualTo(from.getExtSearchParams());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getSearchRetBatch())) {
            c.andSearchRetBatchEqualTo(from.getSearchRetBatch());
        }
        if (!ObjectUtils.isEmpty(from.getSearchRetBatchSummary())) {
            c.andSearchRetBatchSummaryEqualTo(from.getSearchRetBatchSummary());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<SearchTaskVO> doList2VOList(List<SearchTaskDO> list) {
        return CommonUtil.listConverter(list, SearchTaskConverter::do2VO);
    }
}