package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.integration.aliyun.model.ImageOperateOutputModal;
import ai.conrain.aigc.platform.service.component.ImageOperateService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ImageOperateVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/imageOperate")
public class ImageOperateController {

    @Autowired
    private ImageOperateService imageOperateService;

    /**
     * 图片擦除
     *
     * @param imageOperateVO 图片处理参数
     * @param serverType     擦除服务类型
     * @return taskId（任务 Id）
     */
    @PostMapping("/erase/{serverType}")
    public Result<String> createImageErase(@RequestBody @Validated ImageOperateVO imageOperateVO,
                                     @PathVariable @NotNull String serverType) {
        // 调用图片擦除服务
        String taskId = imageOperateService.imageErase(imageOperateVO, serverType);
        if (taskId != null){
            return Result.success(taskId);
        }

        return Result.failedWithMessage(ResultCode.SYS_ERROR, "图片擦除服务调用失败");
    }

    /**
     * 图片擦除查询
     *
     * @param taskId     擦除任务 Id
     * @param serverType 擦除服务类型
     * @return 图片处理结果
     */
    @GetMapping("/erase/query/{serverType}")
    public Result<ImageOperateOutputModal> imageEraseQuery(@PathVariable String serverType,@RequestParam("taskId") String taskId) {
        return Result.success(imageOperateService.imageEraseQuery(taskId, serverType));
    }

}
