package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 商家偏好配置表
 * 对应数据表：merchant_preference
 */
@Data
public class MerchantPreferenceDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 类型，AUTO_DELIVERY、CREATIVE
     */
    private String type;

    /** 标签列表，以逗号隔开 */
    private String tags;

    /**
     * 备注
     */
    private String memo;

    /**
     * 模特id配置
     */
    private String faces;

    /**
     * 场景id配置
     */
    private String scenes;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /** 是否开启自动创作 */
    private Boolean enableAutoCreative;

    /** 图片数量 */
    private Integer imageNum;

    /** 图片尺寸 */
    private String imageProportion;

    /** 扩展信息 */
    private String extInfo;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted = false;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 服装搭配信息
     */
    private String clothCollocation;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}