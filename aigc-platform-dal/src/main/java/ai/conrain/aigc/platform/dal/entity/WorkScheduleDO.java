package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 对应数据表：work_schedule
 */
@Data
public class WorkScheduleDO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 人员id
     */
    private Integer userId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}