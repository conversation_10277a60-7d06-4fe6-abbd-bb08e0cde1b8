package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionService;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 匹配度打标结果聚合任务
 */
@Slf4j
@Component
public class ImageGroupCaptionJob extends JavaProcessor {
    @Autowired
    private ImageGroupCaptionService imageGroupCaptionService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));

        try {
            log.info("[匹配度打标结果聚合任务] 开始执行，参数: {}", params);

            // 从参数中获取用户ID列表
            List<Integer> userIds = params.getUserIds();
            if (userIds == null || userIds.isEmpty()) {
                log.warn("[匹配度打标结果聚合任务] 参数中未提供用户ID列表，任务结束");
                return new ProcessResult(false, "参数中未提供用户ID列表");
            }

            log.info("[匹配度打标结果聚合任务] 使用的标注员用户ID: {}", userIds);

            // 执行聚合处理
            imageGroupCaptionService.aggregateCaptionResult(userIds, params.isForceUpdate());

            log.info("[匹配度打标结果聚合任务] 执行结束");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("[匹配度打标结果聚合任务] 执行过程中发生异常", e);
            return new ProcessResult(false, e.getMessage());
        } finally {
            MDC.remove("traceId");
            MDC.remove("env");
            OperationContextHolder.clean();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        /**
         * 是否强制更新
         */
        private boolean forceUpdate;

        /**
         * 用户ID列表
         */
        private List<Integer> userIds;
    }
}
