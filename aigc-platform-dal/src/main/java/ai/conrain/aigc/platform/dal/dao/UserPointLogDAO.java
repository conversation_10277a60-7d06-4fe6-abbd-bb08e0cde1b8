package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserPointLogDO;
import ai.conrain.aigc.platform.dal.example.UserPointLogExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserPointLogDAO {
    long countByExample(UserPointLogExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(UserPointLogDO record);

    int insertSelective(UserPointLogDO record);

    List<UserPointLogDO> selectByExampleWithBLOBs(UserPointLogExample example);

    List<UserPointLogDO> selectByExample(UserPointLogExample example);

    UserPointLogDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") UserPointLogDO record, @Param("example") UserPointLogExample example);

    int updateByExampleWithBLOBs(@Param("record") UserPointLogDO record, @Param("example") UserPointLogExample example);

    int updateByExample(@Param("record") UserPointLogDO record, @Param("example") UserPointLogExample example);

    int updateByPrimaryKeySelective(UserPointLogDO record);

    int updateByPrimaryKeyWithBLOBs(UserPointLogDO record);

    int updateByPrimaryKey(UserPointLogDO record);

    UserPointLogDO lockByBizId(Integer relatedId, String type);

    /**
     * 根据用户id和日期范围获取用户消耗缪斯点
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 用户 id 列表
     * @return 消耗缪斯点数
     */
    Integer getCustomerConsumptionPoints(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("list") List<Integer> userIdList);

    /**
     * 根据日期范围获取付费用户数
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param customUserList 用户列表
     * @return 消费的用户数量
     */
    Integer getPayUserCountByDate(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("list") List<Integer> customUserList);

    /**
     * 根据日期范围获取付费用户id 列表
     *
     * @param startDate      开始日期
     * @param endDate        结束时间
     * @param customUserList 用户列表
     * @return 消费的用户 id 列表
     */
    List<Integer> getPayUserByDate(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("list") List<Integer> customUserList);
}