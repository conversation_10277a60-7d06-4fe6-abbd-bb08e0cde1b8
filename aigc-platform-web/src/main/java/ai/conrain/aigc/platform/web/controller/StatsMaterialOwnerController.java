package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.StatsMaterialOwnerDO;
import ai.conrain.aigc.platform.service.model.vo.StatsMaterialOwnerVO;
import ai.conrain.aigc.platform.service.model.query.StatsMaterialOwnerQuery;
import ai.conrain.aigc.platform.service.component.StatsMaterialOwnerService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsMaterialOwner控制器
 *
 * <AUTHOR>
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsMaterialOwner")
public class StatsMaterialOwnerController {

	/** statsMaterialOwnerService */
	@Autowired
	private StatsMaterialOwnerService statsMaterialOwnerService;
	
	@GetMapping("/getById/{id}")
	public Result<StatsMaterialOwnerVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsMaterialOwnerService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody StatsMaterialOwnerVO statsMaterialOwner){
		try {
			StatsMaterialOwnerVO data = statsMaterialOwnerService.insert(statsMaterialOwner);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加服装负责人失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建服装负责人失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsMaterialOwnerService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsMaterialOwnerVO statsMaterialOwner){
		statsMaterialOwnerService.updateByIdSelective(statsMaterialOwner);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<StatsMaterialOwnerVO>> queryStatsMaterialOwnerList(@Valid @RequestBody StatsMaterialOwnerQuery query){
		return Result.success(statsMaterialOwnerService.queryStatsMaterialOwnerList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<StatsMaterialOwnerVO>> getStatsMaterialOwnerByPage(@Valid @RequestBody StatsMaterialOwnerQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsMaterialOwnerService.queryStatsMaterialOwnerByPage(query));
	}
}
