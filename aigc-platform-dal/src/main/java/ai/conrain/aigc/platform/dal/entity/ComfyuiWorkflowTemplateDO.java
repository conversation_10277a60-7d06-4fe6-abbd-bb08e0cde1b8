package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * comfyui模板表
 * 对应数据表：comfyui_workflow_template
 */
@Data
public class ComfyuiWorkflowTemplateDO implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 模板key
     */
    private String templateKey;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 模板版本，如20250610.1
     */
    private String version;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 创建人id
     */
    private Integer createBy;

    /**
     * 修改人id
     */
    private Integer modifyBy;

    /**
     * 模板数据
     */
    private String templateData;

    /**
     * 是否删除
     */
    private boolean deleted;

    private static final long serialVersionUID = 1L;
}