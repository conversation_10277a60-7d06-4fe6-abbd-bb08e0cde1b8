package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.qichacha.model.QiChaChaModelPageVO;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.OrganizationQuery;
import ai.conrain.aigc.platform.service.model.vo.CorpAuthVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * Organization控制器
 *
 * <AUTHOR>
 * @version OrganizationService.java v 0.1 2024-07-12 04:26:40
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/organization")
public class OrganizationController {

    /** organizationService */
    @Autowired
    private OrganizationService organizationService;

    @GetMapping("/getById/{id}")
    public Result<OrganizationVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(organizationService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody OrganizationVO organization) {
        try {
            OrganizationVO data = organizationService.insert(organization);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加组织失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建组织失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        organizationService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody OrganizationVO organization) {
        organizationService.updateByIdSelective(organization);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<OrganizationVO>> queryOrganizationList(@Valid @RequestBody OrganizationQuery query) {
        return Result.success(organizationService.queryOrganizationList(query));
    }

    @PostMapping("/queryDistributorOrganizationTrees")
    public Result<List<OrganizationVO>> queryDistributorOrganizations() {
        return Result.success(organizationService.queryDistributorOrganizationTrees());
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<OrganizationVO>> getOrganizationByPage(@Valid @RequestBody OrganizationQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(organizationService.queryOrganizationByPage(query));
    }

    /**
     * 通过【企查查】查询企业信息
     *
     * @param corpName  企业名称
     * @param pageIndex 页码
     * @return 查询到的相关信息
     */
    @GetMapping("/qiChaCha/query")
    public Result<QiChaChaModelPageVO> qiChaChaFuzzyQuery(@NotNull @RequestParam("corpName") String corpName,
                                                          @NotNull @RequestParam("pageIndex") Integer pageIndex) {
        return Result.success(organizationService.qiChaChaFuzzyQuery(corpName, pageIndex));
    }

    /**
     * 更新企业认证信息
     *
     * @param corpAuthVO 企业信息
     * @return 更新结果
     */
    @PostMapping("/updateCorpAuthInfo")
    public Result<?> updateCorpAuthInfo(@Valid @RequestBody CorpAuthVO corpAuthVO) {
        organizationService.updateCorpAuthInfo(corpAuthVO);
        return Result.success();
    }

    /**
     * 分页查询渠道根组织
     */
    @PostMapping("/queryRootCorpByPage")
    public Result<PageInfo<OrganizationVO>> queryRootCorpByPage(@Valid @RequestBody OrganizationQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(organizationService.queryDistributorCorpByPage(query));
    }

}
