package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.ImageSearchService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AutoGenPairDataJob extends JavaProcessor {

    @Autowired
    private ImageSearchService imageSearchService;

    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        String uuid = CommonUtil.uuid();
        MDC.put("traceId", uuid);
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        OperationContextHolder.setContext(CommonUtil.mockSystemContext());

        // 任务参数
        String instanceParameters = context.getInstanceParameters();

        log.info("[AutoGenPairDataJob] start, instanceParameters: {}", instanceParameters);

        int count = 1;
        if (CommonUtil.isValidJson(instanceParameters)) {
            count = JSONObject.parseObject(instanceParameters).getInteger("count");
        }

        try {
            MaterialModelQuery query = new MaterialModelQuery();
            query.setMaterialType(MaterialType.cloth.name());
            query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
            query.setType(ModelTypeEnum.CUSTOM.getCode());
            query.setOrderBy("id desc");
            query.setPageSize(count);
            query.setPageNum(1);

            List<MaterialModelVO> recentClothes = materialModelService.queryListWithBlogs(query);
            if (!recentClothes.isEmpty()) {
                for (MaterialModelVO c : recentClothes) {
                    try {
                        MDC.put("traceId", uuid + "_" + c.getId());
                        log.info("[AutoGenPairDataJob] start for cloth id:{}", c.getId());
                        imageSearchService.searchByClothImgAndBuildPairs(c);
                    } catch (Exception e){
                        log.error("[AutoGenPairDataJob] error for cloth:{}", c.getId(), e);
                    }
                }
            }
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
            OperationContextHolder.clean();
        }

        return new ProcessResult(true);
    }
}
