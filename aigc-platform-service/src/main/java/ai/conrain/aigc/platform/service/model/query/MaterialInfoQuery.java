package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * MaterialInfoQuery
 *
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
@Data
public class MaterialInfoQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 名称 */
    private String name;

    /** 类型 */
    private String type;

    /** 子类型 */
    private String subType;

    /** 扩展信息，是否生成背面照等 */
    private String extInfo;

    /** 归属主账号id */
    private Integer userId;

    /** 操作者id */
    private Integer operatorId;


    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 素材详情，不同类型的素材类型不一样 */
    private String materialDetail;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
