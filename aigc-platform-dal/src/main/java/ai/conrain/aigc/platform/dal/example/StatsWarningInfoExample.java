package ai.conrain.aigc.platform.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StatsWarningInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public StatsWarningInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public StatsWarningInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public StatsWarningInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public StatsWarningInfoExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNull() {
            addCriterion("stats_type is null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNotNull() {
            addCriterion("stats_type is not null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeEqualTo(String value) {
            addCriterion("stats_type =", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotEqualTo(String value) {
            addCriterion("stats_type <>", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThan(String value) {
            addCriterion("stats_type >", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stats_type >=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThan(String value) {
            addCriterion("stats_type <", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThanOrEqualTo(String value) {
            addCriterion("stats_type <=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLike(String value) {
            addCriterion("stats_type like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotLike(String value) {
            addCriterion("stats_type not like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIn(List<String> values) {
            addCriterion("stats_type in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotIn(List<String> values) {
            addCriterion("stats_type not in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeBetween(String value1, String value2) {
            addCriterion("stats_type between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotBetween(String value1, String value2) {
            addCriterion("stats_type not between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNull() {
            addCriterion("stats_date is null");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNotNull() {
            addCriterion("stats_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatsDateEqualTo(String value) {
            addCriterion("stats_date =", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotEqualTo(String value) {
            addCriterion("stats_date <>", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThan(String value) {
            addCriterion("stats_date >", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThanOrEqualTo(String value) {
            addCriterion("stats_date >=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThan(String value) {
            addCriterion("stats_date <", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThanOrEqualTo(String value) {
            addCriterion("stats_date <=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLike(String value) {
            addCriterion("stats_date like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotLike(String value) {
            addCriterion("stats_date not like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateIn(List<String> values) {
            addCriterion("stats_date in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotIn(List<String> values) {
            addCriterion("stats_date not in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateBetween(String value1, String value2) {
            addCriterion("stats_date between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotBetween(String value1, String value2) {
            addCriterion("stats_date not between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateIsNull() {
            addCriterion("weekly_no_consumption_rate is null");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateIsNotNull() {
            addCriterion("weekly_no_consumption_rate is not null");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateEqualTo(String value) {
            addCriterion("weekly_no_consumption_rate =", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateNotEqualTo(String value) {
            addCriterion("weekly_no_consumption_rate <>", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateGreaterThan(String value) {
            addCriterion("weekly_no_consumption_rate >", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateGreaterThanOrEqualTo(String value) {
            addCriterion("weekly_no_consumption_rate >=", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateLessThan(String value) {
            addCriterion("weekly_no_consumption_rate <", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateLessThanOrEqualTo(String value) {
            addCriterion("weekly_no_consumption_rate <=", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateLike(String value) {
            addCriterion("weekly_no_consumption_rate like", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateNotLike(String value) {
            addCriterion("weekly_no_consumption_rate not like", value, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateIn(List<String> values) {
            addCriterion("weekly_no_consumption_rate in", values, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateNotIn(List<String> values) {
            addCriterion("weekly_no_consumption_rate not in", values, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateBetween(String value1, String value2) {
            addCriterion("weekly_no_consumption_rate between", value1, value2, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andWeeklyNoConsumptionRateNotBetween(String value1, String value2) {
            addCriterion("weekly_no_consumption_rate not between", value1, value2, "weeklyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateIsNull() {
            addCriterion("monthly_no_consumption_rate is null");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateIsNotNull() {
            addCriterion("monthly_no_consumption_rate is not null");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateEqualTo(String value) {
            addCriterion("monthly_no_consumption_rate =", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateNotEqualTo(String value) {
            addCriterion("monthly_no_consumption_rate <>", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateGreaterThan(String value) {
            addCriterion("monthly_no_consumption_rate >", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateGreaterThanOrEqualTo(String value) {
            addCriterion("monthly_no_consumption_rate >=", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateLessThan(String value) {
            addCriterion("monthly_no_consumption_rate <", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateLessThanOrEqualTo(String value) {
            addCriterion("monthly_no_consumption_rate <=", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateLike(String value) {
            addCriterion("monthly_no_consumption_rate like", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateNotLike(String value) {
            addCriterion("monthly_no_consumption_rate not like", value, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateIn(List<String> values) {
            addCriterion("monthly_no_consumption_rate in", values, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateNotIn(List<String> values) {
            addCriterion("monthly_no_consumption_rate not in", values, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateBetween(String value1, String value2) {
            addCriterion("monthly_no_consumption_rate between", value1, value2, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andMonthlyNoConsumptionRateNotBetween(String value1, String value2) {
            addCriterion("monthly_no_consumption_rate not between", value1, value2, "monthlyNoConsumptionRate");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountIsNull() {
            addCriterion("customer_refund_rate_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountIsNotNull() {
            addCriterion("customer_refund_rate_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountEqualTo(Integer value) {
            addCriterion("customer_refund_rate_count =", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountNotEqualTo(Integer value) {
            addCriterion("customer_refund_rate_count <>", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountGreaterThan(Integer value) {
            addCriterion("customer_refund_rate_count >", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_refund_rate_count >=", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountLessThan(Integer value) {
            addCriterion("customer_refund_rate_count <", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_refund_rate_count <=", value, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountIn(List<Integer> values) {
            addCriterion("customer_refund_rate_count in", values, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountNotIn(List<Integer> values) {
            addCriterion("customer_refund_rate_count not in", values, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_refund_rate_count between", value1, value2, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andCustomerRefundRateCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_refund_rate_count not between", value1, value2, "customerRefundRateCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountIsNull() {
            addCriterion("delivery_timeout_count is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountIsNotNull() {
            addCriterion("delivery_timeout_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountEqualTo(Integer value) {
            addCriterion("delivery_timeout_count =", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountNotEqualTo(Integer value) {
            addCriterion("delivery_timeout_count <>", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountGreaterThan(Integer value) {
            addCriterion("delivery_timeout_count >", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("delivery_timeout_count >=", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountLessThan(Integer value) {
            addCriterion("delivery_timeout_count <", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountLessThanOrEqualTo(Integer value) {
            addCriterion("delivery_timeout_count <=", value, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountIn(List<Integer> values) {
            addCriterion("delivery_timeout_count in", values, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountNotIn(List<Integer> values) {
            addCriterion("delivery_timeout_count not in", values, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountBetween(Integer value1, Integer value2) {
            addCriterion("delivery_timeout_count between", value1, value2, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeoutCountNotBetween(Integer value1, Integer value2) {
            addCriterion("delivery_timeout_count not between", value1, value2, "deliveryTimeoutCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountIsNull() {
            addCriterion("customer_balance_alert_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountIsNotNull() {
            addCriterion("customer_balance_alert_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountEqualTo(Integer value) {
            addCriterion("customer_balance_alert_count =", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountNotEqualTo(Integer value) {
            addCriterion("customer_balance_alert_count <>", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountGreaterThan(Integer value) {
            addCriterion("customer_balance_alert_count >", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_balance_alert_count >=", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountLessThan(Integer value) {
            addCriterion("customer_balance_alert_count <", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_balance_alert_count <=", value, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountIn(List<Integer> values) {
            addCriterion("customer_balance_alert_count in", values, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountNotIn(List<Integer> values) {
            addCriterion("customer_balance_alert_count not in", values, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_balance_alert_count between", value1, value2, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerBalanceAlertCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_balance_alert_count not between", value1, value2, "customerBalanceAlertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountIsNull() {
            addCriterion("customer_not_convert_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountIsNotNull() {
            addCriterion("customer_not_convert_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountEqualTo(Integer value) {
            addCriterion("customer_not_convert_count =", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountNotEqualTo(Integer value) {
            addCriterion("customer_not_convert_count <>", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountGreaterThan(Integer value) {
            addCriterion("customer_not_convert_count >", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_not_convert_count >=", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountLessThan(Integer value) {
            addCriterion("customer_not_convert_count <", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_not_convert_count <=", value, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountIn(List<Integer> values) {
            addCriterion("customer_not_convert_count in", values, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountNotIn(List<Integer> values) {
            addCriterion("customer_not_convert_count not in", values, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_not_convert_count between", value1, value2, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCustomerNotConvertCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_not_convert_count not between", value1, value2, "customerNotConvertCount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}