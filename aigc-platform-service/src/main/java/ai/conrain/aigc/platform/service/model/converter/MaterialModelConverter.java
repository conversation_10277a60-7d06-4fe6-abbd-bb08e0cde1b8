package ai.conrain.aigc.platform.service.model.converter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.example.MaterialModelExample;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.model.biz.AutoCreateImageItem;
import ai.conrain.aigc.platform.service.model.biz.ClothColorDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothTypeConfig;
import ai.conrain.aigc.platform.service.model.biz.LabelExtTagsDetail;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.OutfitDetail;
import ai.conrain.aigc.platform.service.model.event.CreateTestImgEvent.CreateTestRequest;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import static ai.conrain.aigc.platform.service.constants.BizConstants.AUTO_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.FLUX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CFG;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY_OTHER;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_COLOR_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_LOWER_BODY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_TYPE_CONFIGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_UPPER_BODY_FRONT_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXT_TAGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_CUTOUT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_LABEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_MODIFY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_SEND_TEST_EVENT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NEGATIVE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PROBLEM_TAGS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_IMAGE_CNT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_IMAGE_FINISHED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.lowerGarment;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.outfit;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.upperGarment;
import static ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum.COMPLETED_STATUS_LIST;

/**
 * MaterialModelConverter
 *
 * @version MaterialModelService.java v 0.1 2024-05-09 02:06:17
 */
@Slf4j
public class MaterialModelConverter {

    /**
     * DO -> VO
     */
    public static MaterialModelVO do2VO(MaterialModelDO from) {
        MaterialModelVO to = new MaterialModelVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(ModelTypeEnum.getByCode(from.getType()));
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setLoraName(from.getLoraName());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(ComfyUIUtils.parseJsonStr(from.getTags()));
        to.setMainType(MainTypeEnum.getByCode(from.getMainType()));
        to.setMainId(from.getMainId());
        to.setMaterialType(
            StringUtils.isNotBlank(from.getMaterialType()) ? MaterialType.valueOf(from.getMaterialType()) : null);

        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);
        if (to.getExtInfo() != null) {
            to.getExtInfo().forEach((k, v) -> {
                v = ComfyUIUtils.parseJsonObj(k, v);
                to.addExtInfo(k, v);
            });
        }

        to.setClothLoraTrainDetail(
            StringUtils.isNotBlank(from.getTrainDetail()) ? JSONObject.parseObject(from.getTrainDetail(),
                LoraTrainDetail.class) : null);
        to.setOperatorNick(from.getOperatorNick());
        to.setUserNick(from.getUserNick());

        LoraTrainDetail detail = to.getClothLoraTrainDetail();
        boolean isLowerBody = detail != null && StringUtils.equals(lowerGarment, detail.getClothType());

        to.setLowerBody(isLowerBody);
        to.setVersion(detail != null ? ModelVersionEnum.getByLoraType(detail.getLoraType()) : ModelVersionEnum.SDXL);

        return to;
    }

    /**
     * VO -> DO
     */
    public static MaterialModelDO vo2DO(MaterialModelVO from) {
        MaterialModelDO to = new MaterialModelDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setLoraName(from.getLoraName());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setTags(ComfyUIUtils.revertJsonStr(from.getTags()));

        to.setMainType(from.getMainType() != null ? from.getMainType().getCode() : null);
        to.setMainId(from.getMainId());
        to.setMaterialType(from.getMaterialType() != null ? from.getMaterialType().name() : null);

        if (from.getExtInfo() != null) {
            from.getExtInfo().forEach((k, v) -> {
                if (v != null) {
                    v = ComfyUIUtils.revertJsonObj(k, v);
                    from.addExtInfo(k, v);
                }
            });
        }
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);

        to.setTrainDetail(CommonUtil.toJSONString(from.getClothLoraTrainDetail()));

        return to;
    }

    /**
     * DO -> Query
     */
    public static MaterialModelQuery do2Query(MaterialModelDO from) {
        MaterialModelQuery to = new MaterialModelQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setLoraName(from.getLoraName());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());

        return to;
    }

    /**
     * Query -> DO
     */
    public static MaterialModelDO query2DO(MaterialModelQuery from) {
        MaterialModelDO to = new MaterialModelDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setLoraName(from.getLoraName());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());

        return to;
    }

    //渠道商管理，全部资产，分页查询，特殊处理：销售分页查看名下客户的资产，或自己上传的资产
    public static MaterialModelExample convertDistributorModelsQuery2Example(MaterialModelQuery from) {
        if (CollectionUtils.isNotEmpty(from.getUserIds()) && from.getOperatorId() != null) {
            MaterialModelExample exam = new MaterialModelExample();

            exam.setRelatedOperatorType(from.getRelatedOperatorType());
            exam.setRelatedOrOperatorId(from.getRelatedOrOperatorId());
            exam.setOnlyUnconfirmedLora(from.isOnlyUnconfirmedLora());

            //q1 or q2
            MaterialModelQuery q1 = new MaterialModelQuery();
            BeanUtils.copyProperties(from, q1);
            q1.setOperatorId(null);
            query2CriteriaItem(q1, exam);

            MaterialModelQuery q2 = new MaterialModelQuery();
            BeanUtils.copyProperties(from, q2);
            q2.setUserIds(null);
            MaterialModelExample.Criteria c2 = query2CriteriaItem(q2, exam);

            //这里只需要or(c2），上面q1创建时自动添加到条件了
            exam.or(c2);

            //逻辑删除过滤
            for (MaterialModelExample.Criteria each : exam.getOredCriteria()) {
                each.andLogicalDeleted(false);
            }

            //翻页参数
            if (from.getPageSize() != null && from.getPageNum() != null) {
                exam.page(from.getPageNum(), from.getPageSize());
            }

            //排序参数
            if (StringUtils.isNotBlank(from.getOrderBy())) {
                exam.setOrderByClause(from.getOrderBy());
            }

            return exam;
        }

        return query2Example(from);
    }

    /**
     * Query -> Example
     */
    public static MaterialModelExample query2Example(MaterialModelQuery from) {
        MaterialModelExample to = new MaterialModelExample();

        to.setRelatedOperatorType(from.getRelatedOperatorType());
        to.setRelatedOrOperatorId(from.getRelatedOrOperatorId());
        to.setOnlyUnconfirmedLora(from.isOnlyUnconfirmedLora());

        query2CriteriaItem(from, to);

        //逻辑删除过滤
        for (MaterialModelExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    private static MaterialModelExample.Criteria query2CriteriaItem(MaterialModelQuery from, MaterialModelExample to) {
        MaterialModelExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getShowImage())) {
            c.andShowImageEqualTo(from.getShowImage());
        }
        if (!ObjectUtils.isEmpty(from.getLoraName())) {
            c.andLoraNameEqualTo(from.getLoraName());
        }
        if (!ObjectUtils.isEmpty(from.getNameLike())) {
            c.andNameLike("%" + from.getNameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getIsIgnoreCopy()) && from.getIsIgnoreCopy()) {
            c.andNameNotLike("%_copy%");
        }
        if (!ObjectUtils.isEmpty(from.getNameOrUserLike())) {
            to.setNameOrUserLike(from.getNameOrUserLike());
        }

        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }

        if (!ObjectUtils.isEmpty(from.getStatusList())) {
            c.andStatusIn(from.getStatusList());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getStartCreateTime())) {
            c.andCreateTimeGreaterThanOrEqualTo(from.getStartCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getClothStyleType())) {
            if (StringUtils.equals(from.getClothStyleType(), "unset")) {
                c.andClothStyleTypeIsNull();
            } else {
                c.andClothStyleTypeEqualTo(from.getClothStyleType());
            }
        }

        if (from.isNeedTrainDetail()) {
            c.andTrainDetailIsNotNull();
        }

        if (from.isNeedInitClothCategory()) {
            c.andExtValueIsBlank(KEY_CLOTH_CATEGORY);
            c.andExtValueIsBlank(KEY_CLOTH_CATEGORY_OTHER);
        }

        if (from.getIsOwner() != null) {
            Integer operatorUserId = OperationContextHolder.getOperatorUserId();
            if (from.getIsOwner()) {
                c.andOperatorIdEqualTo(operatorUserId);
            } else {
                c.andOperatorIdNotEqualTo(operatorUserId);
            }
        }

        if (!ObjectUtils.isEmpty(from.getDateFrom())) {
            c.andCreateTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getDateFrom()));
        }

        if (!ObjectUtils.isEmpty(from.getDateTo())) {
            c.andCreateTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getDateTo()));
        }

        if (!ObjectUtils.isEmpty(from.getStartDate()) && !ObjectUtils.isEmpty(from.getEndDate())) {
            c.andCreateTimeBetween(from.getStartDate(), from.getEndDate());
        }

        if (!ObjectUtils.isEmpty(from.getDeliveryStartDate()) && !ObjectUtils.isEmpty(from.getDeliveryEndDate())) {
            c.andDeliveryTimeBetween(from.getDeliveryStartDate(), from.getDeliveryEndDate());
        }

        if (!ObjectUtils.isEmpty(from.getAgeRanges())) {
            c.andAgeRangeIn(from.getAgeRanges());
        }


        if (from.isOnlyShowV2()) {
            c.andLoraTypeEquals(FLUX);
        }

        if (from.isOnlyShowHasCase()) {
            c.andCustomerCaseIsNotNull();
        }

        if (!ObjectUtils.isEmpty(from.getGarmentType())) {
            c.andGarmentTypeEquals(from.getGarmentType());
        }

        if (!ObjectUtils.isEmpty(from.getGarmentTypeList())) {
            c.andGarmentTypeIn(from.getGarmentTypeList());
        }

        if (!ObjectUtils.isEmpty(from.getLabelType())) {
            c.andLabelTypeEquals(from.getLabelType());
        }

        if (from.isOnlyShowDemo()) {
            c.andIsDemoTag();
        }

        if (from.isNeedSyncTestImageStatus()) {
            c.andExtValueEquals(KEY_IS_SEND_TEST_EVENT, Boolean.toString(true));
            c.andExtValueGreaterThan(KEY_TEST_IMAGE_CNT, 0);
            c.andExtValueNotEquals(KEY_TEST_IMAGE_FINISHED, YES);
        }

        if (from.isOnlyNearingDelivery()) {
            c.andStatusNotIn(COMPLETED_STATUS_LIST);
            c.andUserTypeIsCustomerOrSeller();

            //创建时间在 20 小时前，并且创建时间在 1 周内
            c.andCreateTimeLessThan(DateUtils.addHours(new Date(), -20));
            c.andCreateTimeGreaterThan(DateUtils.addWeeks(new Date(), -1));
        }

        if (from.isOnlyDeliveryTimeout()) {
            c.andStatusNotIn(COMPLETED_STATUS_LIST);
            c.andUserTypeIsCustomer();

            //创建时间在 24 小时前，并且创建时间在 1 周内
            c.andCreateTimeLessThan(DateUtils.addHours(new Date(), -24));
            c.andCreateTimeGreaterThan(DateUtils.addWeeks(new Date(), -1));
        }

        if (CollectionUtils.isNotEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (from.isInitialReviewable()) {
            c.andStatusEqualTo(MaterialModelStatusEnum.IN_TRAINING.getCode());
            c.andLabelCompleted();
            c.andLoraUnconfirmed();
        }
        if (from.isTesting()) {
            c.andStatusEqualTo(MaterialModelStatusEnum.TESTING.getCode());
        }
        // 仅查看实验标签
        if (from.getOnlyExperimental() != null) {
            if (from.getOnlyExperimental()) {
                c.andIsExperimental();
            } else {
                c.andIsNotExperimental();
            }
        }
        if (from.isOnlyShowSaleLora()) {
            c.andUserTypeIsSeller();
        }

        if (from.getOnlyShowColorSplit() != null && from.getOnlyShowColorSplit()) {
            c.andMainTypeEqualTo(MainTypeEnum.SUB.getCode());
        } else {
            if (!ObjectUtils.isEmpty(from.getMainType())) {
                c.andMainTypeEqualTo(from.getMainType());
            }
            if (!ObjectUtils.isEmpty(from.getMainTypes())) {
                c.andMainTypeIn(from.getMainTypes());
            }
        }

        if (!ObjectUtils.isEmpty(from.getMainId())) {
            c.andMainIdEqualTo(from.getMainId());
        }

        if (!ObjectUtils.isEmpty(from.getMaterialType())) {
            c.andMaterialTypeEqualTo(from.getMaterialType());
        }

        // 年龄范围
        if (!ObjectUtils.isEmpty(from.getAgeRange())) {
            c.andExtValueLike(KEY_AGE_RANGE, from.getAgeRange());
        }

        if (from.getOnlyProcessed() != null && from.getOnlyProcessed()) {
            c.andStatusIn(COMPLETED_STATUS_LIST);
        }

        if (from.getOnlyProblematic() != null && from.getOnlyProblematic()) {
            Map<String, String> map = new HashMap<>();
            map.put(KEY_IS_MODIFY_CUTOUT, YES);
            map.put(KEY_IS_MODIFY_LABEL, YES);
            map.put(KEY_IS_MODIFY_PROMPT, YES);
            c.andExtValueEqualsOr(map, Collections.singletonList(KEY_PROBLEM_TAGS));
        }

        // 交付方式筛选 自动交付/人工交付
        String deliveryMode = from.getDeliveryMode();
        if (StringUtils.isNotBlank(deliveryMode)) {
            if ("AUTO".equals(deliveryMode)) {
                c.andIsAutoDelivery();
            }else if ("MANUAL".equals(deliveryMode)) {
                c.andIsManualDelivery();
            }
        }

        if (from.isRelatedToMe()) {
            if (CollectionUtils.isNotEmpty(from.getUserIds())) {
                c.andReviewerIdOrUserIdsIn(from.getReviewerId(), from.getUserIds());
            } else {
                c.andReviewerIdEqualTo(from.getReviewerId());
            }
        } else {
            if (from.getReviewerId() != null) {
                c.andReviewerIdEqualTo(from.getReviewerId());
            }
            if (CollectionUtils.isNotEmpty(from.getUserIds())) {
                c.andUserIdIn(from.getUserIds());
            }
        }
        return c;
    }

    @SuppressWarnings("unchecked")
    public static List<ClothTypeConfig> convert2ClothTypeConfig(MaterialModelVO model) {
        if (model == null || StringUtils.equals(model.getStatus(), MaterialModelStatusEnum.IN_TRAINING.getCode())) {
            return Collections.emptyList();
        }
        String configJson = model.getExtInfo(KEY_CLOTH_TYPE_CONFIGS, String.class);
        List<String> colorImages = model.getExtInfo(KEY_CLOTH_COLOR_IMAGES, List.class);
        LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();
        if (trainDetail == null) {
            return Collections.emptyList();
        }
        String colorDesc = trainDetail.getColorDescriptions();
        List<String> colorDescList = StringUtils.isNotBlank(colorDesc) ? JSONArray.parseArray(colorDesc, String.class)
            : null;
        if (StringUtils.isNotBlank(configJson)) {
            try {
                List<ClothTypeConfig> clothTypeConfigs = JSONArray.parseArray(configJson, ClothTypeConfig.class);

                for (ClothTypeConfig config : clothTypeConfigs) {
                    //默认sdxl的模型
                    if (!config.getType().contains(ModelVersionEnum.SDXL.getCode()) && !config.getType().contains(
                        ModelVersionEnum.FLUX.getCode())) {
                        config.getType().add(ModelVersionEnum.SDXL.getCode());
                    }

                    //兼容性处理
                    if (CollectionUtils.isEmpty(config.getColorList())) {
                        ArrayList<ClothColorDetail> colorList = new ArrayList<>();
                        ClothColorDetail detail = new ClothColorDetail(1, null,
                            config.getExtInfo(KEY_EXT_TAGS, String.class));
                        detail.setShowImg(model.getShowImage());
                        colorList.add(detail);
                        config.setColorList(colorList);
                    }

                    if (CollectionUtils.isNotEmpty(colorImages) && CollectionUtils.isNotEmpty(config.getColorList())) {
                        config.getColorList().forEach(color -> color.setShowImg(
                            colorImages.size() >= color.getIndex() ? colorImages.get(color.getIndex() - 1) : null));
                    }

                    if (CollectionUtils.isNotEmpty(colorDescList) && CollectionUtils.isNotEmpty(
                        config.getColorList())) {
                        config.getColorList().forEach(color -> {
                            if (StringUtils.isBlank(color.getDesc())) {
                                color.setDesc(colorDescList.size() >= color.getIndex() && StringUtils.isNotBlank(
                                    colorDescList.get(color.getIndex() - 1)) ? colorDescList.get(color.getIndex() - 1)
                                    : null);
                            }
                        });
                    }

                    if (CollectionUtils.isNotEmpty(config.getColorList())) {

                        //格式化补充激活词
                        config.getColorList().forEach(
                            e -> MaterialModelConverter.formatExtTags(e, config.getType(), trainDetail));
                        //通过index对颜色排正序
                        config.getColorList().sort(Comparator.comparingInt(ClothColorDetail::getIndex));
                    }
                }

                return clothTypeConfigs;
            } catch (Exception e) {
                log.error("convert2ClothTypeConfig error,id=" + model.getId() + ",json=" + configJson, e);
            }
        }
        //填充一个默认的配置
        ModelVersionEnum modelVersion = trainDetail != null ? ModelVersionEnum.getByLoraType(trainDetail.getLoraType())
            : ModelVersionEnum.SDXL;

        ClothTypeConfig config = new ClothTypeConfig();
        config.setType(Arrays.asList(CameraAngleEnum.WHOLE_BODY.getCode(), CameraAngleEnum.FRONT_VIEW.getCode(),
            modelVersion.getCode()));
        config.setTags(model.getTags());
        //只设置以下3个扩展信息
        String extTags = model.getExtInfo(KEY_EXT_TAGS, String.class);
        config.addExtInfo(KEY_EXT_TAGS, extTags);
        config.addExtInfo(KEY_NEGATIVE, model.getExtInfo(KEY_NEGATIVE, String.class));
        config.addExtInfo(KEY_CFG, model.getExtInfo(KEY_CFG, String.class));

        config.setColorList(new ArrayList<>());
        ClothColorDetail detail = new ClothColorDetail(1, null, extTags);
        detail.setShowImg(model.getShowImage());
        config.getColorList().add(detail);
        return Collections.singletonList(config);
    }

    public static List<ClothTypeConfig> convert2ClothTypeConfig(List<LabelExtTagsDetail> extTagsList, String version,
                                                                String tags, String activateKey,
                                                                LoraTrainDetail trainDetail) {
        if (CollectionUtils.isEmpty(extTagsList)) {
            return null;
        }
        Map<String, String> activateKeyMap = null;
        if (CommonUtil.isExpression(activateKey)) {
            activateKeyMap = CommonUtil.parseStrToMap(activateKey);
        }

        List<ClothTypeConfig> result = new ArrayList<>();
        for (LabelExtTagsDetail detail : extTagsList) {
            ClothTypeConfig config = new ClothTypeConfig();
            List<CameraAngleEnum> angles = detail.getAngles();
            List<String> types = angles.stream().map(CameraAngleEnum::getCode).collect(Collectors.toList());
            types.add(version);
            config.setType(types);
            config.setIncludesBra(detail.getIncludesBra() != null ? detail.getIncludesBra() : false);

            String orientation = CameraAngleEnum.getOrientation(angles).getTag();
            CameraAngleEnum bodyAngle = CameraAngleEnum.getBodyPosition(angles);
            String bodyPosition = bodyAngle.getTag();
            if (MapUtils.isEmpty(activateKeyMap)) {
                config.setTags(tags + orientation + bodyPosition);
            } else {
                if (bodyAngle == CameraAngleEnum.WHOLE_BODY) {
                    bodyPosition = "mid shot, " + bodyPosition;
                }
                config.setTags(orientation + bodyPosition);
            }

            if (CollectionUtils.isNotEmpty(detail.getColorList())) {
                config.setColorList(initColorValue(detail.getColorList(), activateKeyMap, types, trainDetail));
            } else {//todo 发布兼容逻辑，10.15发布后可删除
                config.addExtInfo(KEY_EXT_TAGS, detail.getValue());
            }
            result.add(config);
        }
        return result;
    }

    public static List<ClothTypeConfig> convert2ClothTypeConfig(List<MaterialModelVO> subList) {
        if (CollectionUtils.isEmpty(subList)) {
            return null;
        }

        List<ClothTypeConfig> result = null;

        for (int i = 0; i < subList.size(); i++) {
            MaterialModelVO model = subList.get(i);

            List<ClothTypeConfig> current = convert2ClothTypeConfig(model);
            //设置颜色的index
            int idx = i + 1;
            current.forEach(e -> e.getColorList().forEach(t -> t.setIndex(idx)));

            if (result == null) {
                result = current;
                continue;
            }

            //基于type进行合并
            for (ClothTypeConfig each : current) {
                //筛选所有type一致的配置
                ClothTypeConfig origin = result.stream().filter(e -> e != null && new HashSet<>(
                    e.getType()).containsAll(each.getType())).findFirst().orElse(null);

                //如果已经存在，则将目标颜色添加到该配置
                if (origin != null) {
                    origin.getColorList().addAll(each.getColorList());
                } else {
                    //如果不存在，则直接添加一个新的配置
                    result.add(each);
                }
            }
        }

        return result;
    }

    /**
     * 同步变更子模型的服装配置
     *
     * @param subList 子模型列表
     * @param main    主模型
     */
    public static void syncSubModelClothTypeConfig(List<MaterialModelVO> subList, MaterialModelVO main) {
        List<ClothTypeConfig> configs = main.getClothTypeConfigs();
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        if (CollectionUtils.isEmpty(subList)) {
            return;
        }

        for (int i = 0; i < subList.size(); i++) {
            MaterialModelVO sub = subList.get(i);
            List<ClothTypeConfig> subConfig = convert2ClothTypeConfig(sub);

            int idx = i + 1;
            subConfig.forEach(e -> {
                List<ClothTypeConfig> mainTypeConfig = configs.stream().filter(t -> t != null && new HashSet<>(
                    t.getType()).containsAll(e.getType())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(mainTypeConfig)) {
                    e.setColorList(mainTypeConfig.get(0).getColorList().stream().filter(t -> t.getIndex() == idx)
                        .collect(Collectors.toList()));
                }
            });

            sub.setClothTypeConfigs(subConfig);
        }

    }

    public static List<String> buildColorImageList(List<MaterialModelVO> subList) {
        List<String> colorList = new ArrayList<>();

        for (MaterialModelVO sub : subList) {
            //noinspection unchecked
            List<String> images = sub.getExtInfo(KEY_CLOTH_COLOR_IMAGES, List.class);
            if (CollectionUtils.isNotEmpty(images)) {
                colorList.add(images.get(0));
            } else {
                colorList.add("");
            }
        }

        return colorList;
    }

    /**
     * do list -> vo list
     */
    public static List<MaterialModelVO> doList2VOList(List<MaterialModelDO> list) {
        return CommonUtil.listConverter(list, MaterialModelConverter::do2VO);
    }

    /**
     * 初始化颜色的value（展示在颜色后的补充激活词）
     *
     * @param colorList      原颜色列表
     * @param activateKeyMap 激活词map
     * @param types          角度标签列表
     * @param trainDetail    训练详情
     * @return 颜色列表
     */
    private static List<ClothColorDetail> initColorValue(List<ClothColorDetail> colorList,
                                                         Map<String, String> activateKeyMap, List<String> types,
                                                         LoraTrainDetail trainDetail) {
        if (CollectionUtils.isEmpty(colorList)) {
            return colorList;
        }

        colorList.forEach(color -> {

            if (MapUtils.isNotEmpty(activateKeyMap)) {
                formatExtTags(color, types, trainDetail, activateKeyMap);
            } else {
                formatExtTags(color, types, trainDetail);
            }
        });
        return colorList;
    }

    private static void formatExtTags(ClothColorDetail color, List<String> types, LoraTrainDetail trainDetail,
                                      Map<String, String> activateKeyMap) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("color", color.getName());
        variables.put("garment_type", color.getGarmentType());
        String key = activateKeyMap.get("c" + color.getIndex());
        String tags = CommonUtil.formatExpression(key, variables);
        tags = CommonUtil.replaceIgnoreCase(tags, "dark", "deep");

        String value = color.getPrefix() + tags + ". " + (StringUtils.isNotBlank(color.getGarmentStatus())
            ? color.getGarmentStatus() : "");
        String reservedItems = fetchReservedItems(color, trainDetail);
        value += StringUtils.isNotBlank(reservedItems) ? "," + reservedItems : "";
        value = appendExtTagsSuffix(value, types);
        color.setValue(value);
    }

    /**
     * 格式化补充激活词
     *
     * @param detail      服装颜色模型
     * @param trainDetail 训练详情
     */
    private static void formatExtTags(ClothColorDetail detail, List<String> types, LoraTrainDetail trainDetail) {
        if (StringUtils.isNotBlank(detail.getValue())) {
            return;
        }

        String detailType = detail.getDetailType();
        String detailDesc = detail.getDetailDesc();
        String prefix = detail.getPrefix();

        if (StringUtils.isNotBlank(detailType) && StringUtils.isNotBlank(detailDesc)) {
            String extTags = detailType + ", " + detailDesc;
            extTags = (prefix == null ? "" : prefix) + CommonUtil.replaceIgnoreCase(extTags, "dark", "deep");
            extTags = ComfyUIUtils.appendDotIfNeeded(extTags);
            String reservedItems = fetchReservedItems(detail, trainDetail);
            extTags += StringUtils.isNotBlank(reservedItems) ? "," + reservedItems : "";
            extTags = appendExtTagsSuffix(extTags, types);
            detail.setValue(extTags);
        }
    }

    /**
     * 获取保留物品
     *
     * @param color       颜色详情
     * @param trainDetail 训练详情
     * @return 保留物品
     */
    private static String fetchReservedItems(ClothColorDetail color, LoraTrainDetail trainDetail) {
        if (!StringUtils.equals(YES, trainDetail.getReservedItems())) {
            return "";
        }

        String views = color.getViews();
        if (StringUtils.isBlank(views) || !CommonUtil.isValidJson(views)) {
            return "";
        }

        OutfitDetail outfitDetail = JSONObject.parseObject(views, OutfitDetail.class);
        String clothType = trainDetail.getClothType();
        switch (clothType) {
            case upperGarment:
                outfitDetail.setOuterwear(null);
                outfitDetail.setInnerwear(null);
                break;
            case lowerGarment:
                outfitDetail.setBottoms(null);
                break;
            case outfit:
                outfitDetail.setOuterwear(null);
                outfitDetail.setInnerwear(null);
                outfitDetail.setBottoms(null);
                break;
            default:
                log.error("不支持的服装类型：{}", clothType);
        }

        String result = "";
        result = CommonUtil.appendWithComma(result, outfitDetail.getInnerwear());
        result = CommonUtil.appendWithComma(result, outfitDetail.getOuterwear());
        result = CommonUtil.appendWithComma(result, outfitDetail.getBottoms());
        result = CommonUtil.appendWithComma(result, outfitDetail.getShoes());
        result = CommonUtil.appendWithComma(result, outfitDetail.getAccessories());

        return result;
    }

    private static String appendExtTagsSuffix(String value, List<String> types) {
        //正面上半身：A portrait of a person's upper body, detailed facial features and torso, no lower body visible,
        //背面上半身：A portrait of a person's upper body, no lower body visible,
        //正面/背面下半身：A portrait of a person's lower body, no upper body visible
        String suffix = null;
        CameraAngleEnum bodyPosition = CameraAngleEnum.getBodyPositionByStr(types);
        if (bodyPosition == CameraAngleEnum.LOWER_BODY) {
            suffix = KEY_CLOTH_LOWER_BODY_PROMPT;
        } else if (bodyPosition == CameraAngleEnum.UPPER_BODY && CameraAngleEnum.getOrientationByStr(types)
                                                                 == CameraAngleEnum.FRONT_VIEW) {
            suffix = KEY_CLOTH_UPPER_BODY_FRONT_PROMPT;
        }

        if (StringUtils.isNotBlank(suffix) && !StringUtils.contains(value, suffix)) {
            return value + suffix;
        }
        return value;
    }

    public static List<ClothTypeConfig> mergeColor(List<ClothTypeConfig> origin, List<ClothTypeConfig> target) {
        for (ClothTypeConfig each : origin) {
            ClothTypeConfig find = target.stream().filter(e -> new HashSet<>(e.getType()).containsAll(each.getType()))
                .findFirst().orElse(null);

            if (find == null) {
                continue;
            }

            for (ClothColorDetail color : find.getColorList()) {
                if (each.getColorList().stream().anyMatch(e -> color.getIndex() == e.getIndex())) {
                    continue;
                }
                each.getColorList().add(color);
            }
        }
        return origin;
    }

    @NotNull
    public static CreateTestRequest buildCreateRequest(AutoCreateImageItem config, int i) {
        CreateTestRequest request = new CreateTestRequest();
        request.setFaceId(config.getFaceId());
        request.setSceneId(config.getSceneId());
        request.setNum(config.getImageNum());
        request.setImgProportions(config.getProportion());
        request.setExpression(config.getExpression());
        request.setCameraAngle(Arrays.asList(config.getPosition(), config.getBodyType()));
        request.setBizTag(AUTO_IMAGES);
        request.setColorIndex(i + 1);
        return request;
    }
}