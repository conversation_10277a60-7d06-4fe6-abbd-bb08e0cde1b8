package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.OrderSettlementDO;
import ai.conrain.aigc.platform.dal.example.OrderSettlementExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderSettlementDAO {
    long countByExample(OrderSettlementExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(OrderSettlementDO record);

    int insertSelective(OrderSettlementDO record);

    List<OrderSettlementDO> selectByExample(OrderSettlementExample example);

    OrderSettlementDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") OrderSettlementDO record, @Param("example") OrderSettlementExample example);

    int updateByExample(@Param("record") OrderSettlementDO record, @Param("example") OrderSettlementExample example);

    int updateByPrimaryKeySelective(OrderSettlementDO record);

    int updateByPrimaryKey(OrderSettlementDO record);

    List<OrderSettlementDO> queryPendingSettle(@Param("distributorCorpId") Integer distributorCorpId, @Param("month") String month);

}