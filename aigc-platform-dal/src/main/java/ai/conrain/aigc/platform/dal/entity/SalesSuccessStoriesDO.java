package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 销售成功案例表
 * 对应数据表：sales_success_stories
 */
@Data
public class SalesSuccessStoriesDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 案例名称
     */
    private String name;

    /**
     * 是否置顶
     */
    private Boolean topped;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 创作批次id
     */
    private Integer batchId;

    /**
     * 服装id
     */
    private Integer modelId;

    /**
     * 服装名称
     */
    private String modelName;

    /**
     * 服装展示图片
     */
    private String modelUrl;

    /**
     * 备注
     */
    private String memo;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 操作员/子账号id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 图片地址列表，jsonArray格式
     */
    private String imageUrls;

    /**
     * 扩展
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}