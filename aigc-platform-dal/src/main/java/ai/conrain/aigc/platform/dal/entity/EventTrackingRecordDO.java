package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应数据表：event_tracking_record
 */
public class EventTrackingRecordDO implements Serializable {
    /**
     * 埋点记录主键
     */
    private Integer id;

    /**
     * 关联用户 Id（为空时则说明用户尚未登录）
     */
    private Integer userId;

    /**
     * 临时uuid（唯一 UUID）
     */
    private String tempUserUuid;

    /**
     * ip 地址
     */
    private String ipAddress;

    /**
     * 会话 ID，用于标记一次用户的会话操作
     */
    private String sessionId;

    /**
     * 事件类型 枚举记录 （1.进入页面 2.离开页面 3.按钮点击 4.鼠标悬停 ....）
     */
    private String eventType;

    /**
     * 具体事件内容(点击登录按钮、点击发送验证码、鼠标悬停在视频上面...)
     */
    private String eventContent;

    /**
     * 网页标题
     */
    private String pageTitle;

    /**
     * 当前页面的 URL
     */
    private String pageUrl;

    /**
     * 用户留存时间 （单位：秒） 默认为0
     */
    private Integer userRetentionTime;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 浏览器信息
     */
    private String browser;

    /**
     * 请求来源（1.PC  2.App 3.小程序 4.抖音....）
     */
    private String requestResource;

    /**
     * 上一个页面的 url
     */
    private String preReferrer;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 存储其他额外的事件数据（JSON 格式存储）
     */
    private String additionalData;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getTempUserUuid() {
        return tempUserUuid;
    }

    public void setTempUserUuid(String tempUserUuid) {
        this.tempUserUuid = tempUserUuid == null ? null : tempUserUuid.trim();
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress == null ? null : ipAddress.trim();
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType == null ? null : eventType.trim();
    }

    public String getEventContent() {
        return eventContent;
    }

    public void setEventContent(String eventContent) {
        this.eventContent = eventContent == null ? null : eventContent.trim();
    }

    public String getPageTitle() {
        return pageTitle;
    }

    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle == null ? null : pageTitle.trim();
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl == null ? null : pageUrl.trim();
    }

    public Integer getUserRetentionTime() {
        return userRetentionTime;
    }

    public void setUserRetentionTime(Integer userRetentionTime) {
        this.userRetentionTime = userRetentionTime;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os == null ? null : os.trim();
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser == null ? null : browser.trim();
    }

    public String getRequestResource() {
        return requestResource;
    }

    public void setRequestResource(String requestResource) {
        this.requestResource = requestResource == null ? null : requestResource.trim();
    }

    public String getPreReferrer() {
        return preReferrer;
    }

    public void setPreReferrer(String preReferrer) {
        this.preReferrer = preReferrer == null ? null : preReferrer.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(String additionalData) {
        this.additionalData = additionalData == null ? null : additionalData.trim();
    }
}