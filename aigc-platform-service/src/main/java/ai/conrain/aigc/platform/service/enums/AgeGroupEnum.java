package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

//幼童1-2
//中童3-9
//青少年10-19
//成人20-69
//老人70以上
@Getter
public enum AgeGroupEnum {
    YOUNG("Young", "幼童"),
    CHILD("Child", "中童"),
    TEENAGER("Teenager", "青少年"),
    ADULT("Adult", "成人"),
    ELDERLY("Elderly", "老人"),
    UNKNOWN("unknown", "未知"),
    ;
    private String code;
    private String desc;
    AgeGroupEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }
    public static AgeGroupEnum getByCode(String code){
        for (AgeGroupEnum e : values()) {
            if (StringUtils.equalsIgnoreCase(e.code, code)) {
                return e;
            }
        }
        return null;
    }
}
