package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageGroupCaptionDAO {
    long countByExample(ImageGroupCaptionExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageGroupCaptionDO record);

    int insertSelective(ImageGroupCaptionDO record);

    List<ImageGroupCaptionDO> selectByExample(ImageGroupCaptionExample example);

    ImageGroupCaptionDO selectByPrimaryKey(Integer id);

    ImageGroupCaptionDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageGroupCaptionDO record, @Param("example") ImageGroupCaptionExample example);

    int updateByExample(@Param("record") ImageGroupCaptionDO record, @Param("example") ImageGroupCaptionExample example);

    int updateByPrimaryKeySelective(ImageGroupCaptionDO record);

    int updateByPrimaryKey(ImageGroupCaptionDO record);

    int logicalDeleteByExample(@Param("example") ImageGroupCaptionExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    /**
     * 查询需要更新的图片组ID列表
     *
     * @param userIds 用户ID列表
     * @param requiredUserCount 要求的用户数量
     * @param forceUpdate 是否强制更新（忽略时间比较）
     * @param limit 限制返回数量
     * @param offset 偏移量
     * @return 需要更新的图片组ID列表
     */
    List<Integer> selectImageGroupIdsForUpdate(@Param("userIds") List<Integer> userIds,
                                               @Param("requiredUserCount") Integer requiredUserCount,
                                               @Param("forceUpdate") Boolean forceUpdate,
                                               @Param("limit") Integer limit,
                                               @Param("offset") Integer offset);

    /**
     * 查询需要更新的图片组ID总数
     *
     * @param userIds 用户ID列表
     * @param requiredUserCount 要求的用户数量
     * @param forceUpdate 是否强制更新（忽略时间比较）
     * @return 需要更新的图片组总数
     */
    Long countImageGroupIdsForUpdate(@Param("userIds") List<Integer> userIds,
                                     @Param("requiredUserCount") Integer requiredUserCount,
                                     @Param("forceUpdate") Boolean forceUpdate);
}