package ai.conrain.aigc.platform.service.model.biz.agent;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.service.enums.AgeGroupEnum;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import lombok.Data;

@Data
public class ClothImgItem {
    private String clothImgUrl;
    // 服装类型：上装/下装/套装/泳衣/连体衣/情趣内衣
    private ClothTypeEnum clothType;
    // 性别
    private ClothGenderEnum clothGender;
    // 服装用户年龄段
    private AgeGroupEnum ageGroup;
    // 服装特征描述打标
    private ImageAnalysisCaption clothAnalysis;
}
