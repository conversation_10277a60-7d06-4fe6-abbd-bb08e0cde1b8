package ai.conrain.aigc.platform.dal.generator.entity;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 存储表配置和freemarker模板变量参数，因此轻易不要改这些变量命名
 */
@Data
public class ConfigInfo implements Serializable {
    private static final long serialVersionUID = 123123L;

    //creator in javadoc
    private String author;

    //version in javadoc
    private String version;

    //user_info
    private String table;

    //UserInfo do类名
    private String entityName;

    //userInfo 变量名，小写开头
    private String objectName;

    //表注释
    private String entityComment;

    //创建时间
    private String createTime;

    //do包路径
    private String entityUrl;

    //example路径
    private String exampleUrl;

    //dao包路径
    private String daoUrl;

    //sqlmap包路径
    private String mapperUrl;

    //service包路径
    private String serviceUrl;

    //service impl包路径
    private String serviceImplUrl;

    //controller包路径
    private String controllerUrl;

    //vo包路径
    private String voPackageUrl;

    //converter包路径
    private String converterPackageUrl;

    //query包路径
    private String queryPackageUrl;

    //主键类型 java
    private String idType;

    //主键类型 jdbc
    private String idJdbcType;

    //columns
    private List<PropertyInfo> columns;

    //entity java imports
    private List<String> entityJavaImports;

    //文件直接覆盖
    private boolean overwrite;

    //是否支持逻辑删除
    private boolean logicDelete;

    //配置里的运行模式，MyBatis3 / MyBatis3Simple
    private String targetRuntime;

    /** 是否允许根据主键删除 */
    private boolean enableDeleteByPrimaryKey;

    /** 是否允许根据主键更新 */
    private boolean enableUpdateByPrimaryKey;

    /** 是否允许根据条件删除 */
    private boolean enableDeleteByExample;

    /** 是否允许根据条件更新 */
    private boolean enableUpdateByExample;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PropertyInfo implements Serializable {

        private static final long serialVersionUID = 123124L;

        // 数据库字段
        private String column;

        // 数据库字段类型
        private String jdbcType;

        // 数据库字段备注
        private String comment;

        // java 字段
        private String property;

        // java 字段（首字母大写）
        private String upFiled;

        // java类型（短名称）
        private String javaType;

        //java类型全名（含包路径）
        private String fullyJavaType;
    }
}