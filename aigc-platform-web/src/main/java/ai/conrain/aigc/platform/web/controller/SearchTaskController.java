package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;
import ai.conrain.aigc.platform.service.model.query.SearchTaskQuery;
import ai.conrain.aigc.platform.service.component.SearchTaskService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * SearchTask控制器
 *
 * <AUTHOR>
 * @version SearchTaskService.java v 0.1 2025-08-16 05:50:47
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/searchTask")
public class SearchTaskController {

	/** searchTaskService */
	@Autowired
	private SearchTaskService searchTaskService;
	
	@GetMapping("/getById/{id}")
	public Result<SearchTaskVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(searchTaskService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody SearchTaskVO searchTask){
		return Result.success(searchTaskService.insert(searchTask).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		searchTaskService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody SearchTaskVO searchTask){
		searchTaskService.updateByIdSelective(searchTask);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<SearchTaskVO>> querySearchTaskList(@Valid @RequestBody SearchTaskQuery query){
		return Result.success(searchTaskService.querySearchTaskList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<SearchTaskVO>> getSearchTaskByPage(@Valid @RequestBody SearchTaskQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(searchTaskService.querySearchTaskByPage(query));
	}
}