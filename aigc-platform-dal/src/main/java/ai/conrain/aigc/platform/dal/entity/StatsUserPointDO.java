package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户点数消耗统计表
 * 对应数据表：stats_user_point
 */
@Data
public class StatsUserPointDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Integer id;

    /**
     * 商家昵称
     */
    private String nickName;

    /**
     * 所属渠道商
     */
    private String distributorCorpName;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL
     */
    private String statsType;

    /**
     * 统计日期: 格式为yyyy-MM-dd
     */
    private String statsDate;

    /**
     * 消耗的算力点
     */
    private Integer pointConsumed;

    /**
     * 消耗的赠送点
     */
    private Integer givePointConsumed;

    /**
     * 消耗的体验点
     */
    private Integer expPointConsumed;

    /**
     * 消耗的套内点
     */
    private Integer modelPointConsumed;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

}