package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.SalesSuccessStoriesDO;
import ai.conrain.aigc.platform.dal.example.SalesSuccessStoriesExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SalesSuccessStoriesDAO {
    long countByExample(SalesSuccessStoriesExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SalesSuccessStoriesDO record);

    int insertSelective(SalesSuccessStoriesDO record);

    List<SalesSuccessStoriesDO> selectByExampleWithBLOBs(SalesSuccessStoriesExample example);

    List<SalesSuccessStoriesDO> selectByExample(SalesSuccessStoriesExample example);

    SalesSuccessStoriesDO selectByPrimaryKey(Integer id);

    SalesSuccessStoriesDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") SalesSuccessStoriesDO record, @Param("example") SalesSuccessStoriesExample example);

    int updateByExampleWithBLOBs(@Param("record") SalesSuccessStoriesDO record, @Param("example") SalesSuccessStoriesExample example);

    int updateByExample(@Param("record") SalesSuccessStoriesDO record, @Param("example") SalesSuccessStoriesExample example);

    int updateByPrimaryKeySelective(SalesSuccessStoriesDO record);

    int updateByPrimaryKeyWithBLOBs(SalesSuccessStoriesDO record);

    int updateByPrimaryKey(SalesSuccessStoriesDO record);

    int logicalDeleteByExample(@Param("example") SalesSuccessStoriesExample example);

    int logicalDeleteByPrimaryKey(Integer id);
}