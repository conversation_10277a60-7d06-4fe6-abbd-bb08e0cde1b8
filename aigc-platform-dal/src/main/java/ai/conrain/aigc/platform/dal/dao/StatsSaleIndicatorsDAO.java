package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.StatsSaleIndicatorsDO;
import ai.conrain.aigc.platform.dal.example.StatsSaleIndicatorsExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StatsSaleIndicatorsDAO {
    long countByExample(StatsSaleIndicatorsExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(StatsSaleIndicatorsDO record);

    int insertSelective(StatsSaleIndicatorsDO record);

    List<StatsSaleIndicatorsDO> selectByExampleWithBLOBs(StatsSaleIndicatorsExample example);

    List<StatsSaleIndicatorsDO> selectByExample(StatsSaleIndicatorsExample example);

    StatsSaleIndicatorsDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") StatsSaleIndicatorsDO record,
                                 @Param("example") StatsSaleIndicatorsExample example);

    int updateByExampleWithBLOBs(@Param("record") StatsSaleIndicatorsDO record,
                                 @Param("example") StatsSaleIndicatorsExample example);

    int updateByExample(@Param("record") StatsSaleIndicatorsDO record,
                        @Param("example") StatsSaleIndicatorsExample example);

    int updateByPrimaryKeySelective(StatsSaleIndicatorsDO record);

    int updateByPrimaryKeyWithBLOBs(StatsSaleIndicatorsDO record);

    int updateByPrimaryKey(StatsSaleIndicatorsDO record);

    /**
     * 批量插入或更新
     *
     * @param statsSaleIndicatorsDOList 数据集合
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("list") List<StatsSaleIndicatorsDO> statsSaleIndicatorsDOList);

    /**
     * 根据日期和统计周期查询统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsType 统计周期
     * @return 统计数据列表
     */
    List<StatsSaleIndicatorsDO> selectStatsInfoByDateAndPeriod(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("statsType") String statsType);


    /**
     * 根据日期和统计周期查询统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsType 统计周期
     * @param parentId  父级id
     * @return 统计数据列表
     */
    List<StatsSaleIndicatorsDO> selectStatsInfoByDateAndParentId(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("statsType") String statsType,
            @Param("parentId") Integer parentId,
            @Param("userId") Integer userId,
            @Param("nickName") String nickName);
}