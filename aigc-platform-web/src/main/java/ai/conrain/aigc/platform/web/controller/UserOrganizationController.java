package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.UserOrganizationService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * UserOrganization控制器
 *
 * <AUTHOR>
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/userOrganization")
public class UserOrganizationController {

	/** userOrganizationService */
	@Autowired
	private UserOrganizationService userOrganizationService;
	
	@GetMapping("/getById/{id}")
	public Result<UserOrganizationVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(userOrganizationService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody UserOrganizationVO userOrganization){
		try {
			UserOrganizationVO data = userOrganizationService.insert(userOrganization);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加用户组织关系失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建用户组织关系失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		userOrganizationService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody UserOrganizationVO userOrganization){
		userOrganizationService.updateById(userOrganization);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<UserOrganizationVO>> findAll(){
		return Result.success(userOrganizationService.findAll());
	}
}
