package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * MaterialInfoVO
 *
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
@Data
public class MaterialInfoVO implements Serializable {
    /**
     * serialVersionUID
     **/
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 子类型(服装种类)
     */
    private String subType;

    /**
     * 扩展信息，是否生成背面照等
     */
    private JSONObject extInfo;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 素材详情，不同类型的素材类型不一样
     * @see ai.conrain.aigc.platform.service.model.biz.MaterialDetail
     */
    private JSONObject materialDetail;

}
