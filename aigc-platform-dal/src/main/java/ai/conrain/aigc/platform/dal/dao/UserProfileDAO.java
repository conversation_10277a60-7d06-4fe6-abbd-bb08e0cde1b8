package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.UserProfileDO;
import ai.conrain.aigc.platform.dal.example.UserProfileExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserProfileDAO {
    long countByExample(UserProfileExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(UserProfileDO record);

    int insertOrUpdate(UserProfileDO record);

    int insertSelective(UserProfileDO record);

    List<UserProfileDO> selectByExample(UserProfileExample example);

    UserProfileDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") UserProfileDO record, @Param("example") UserProfileExample example);

    int updateByExample(@Param("record") UserProfileDO record, @Param("example") UserProfileExample example);

    int updateByPrimaryKeySelective(UserProfileDO record);

    int updateByPrimaryKey(UserProfileDO record);
}