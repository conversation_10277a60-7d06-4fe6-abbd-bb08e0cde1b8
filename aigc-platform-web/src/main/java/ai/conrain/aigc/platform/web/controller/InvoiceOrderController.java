package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.InvoiceOrderDO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceOrderVO;
import ai.conrain.aigc.platform.service.model.query.InvoiceOrderQuery;
import ai.conrain.aigc.platform.service.component.InvoiceOrderService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * InvoiceOrder控制器
 *
 * <AUTHOR>
 * @version InvoiceOrderService.java v 0.1 2024-06-27 12:49:39
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/invoiceOrder")
public class InvoiceOrderController {

	/** invoiceOrderService */
	@Autowired
	private InvoiceOrderService invoiceOrderService;
	
	@GetMapping("/getById/{id}")
	public Result<InvoiceOrderVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(invoiceOrderService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody InvoiceOrderVO invoiceOrder){
		try {
			InvoiceOrderVO data = invoiceOrderService.insert(invoiceOrder);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加发票订单关联失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建发票订单关联失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		invoiceOrderService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody InvoiceOrderVO invoiceOrder){
		invoiceOrderService.updateByIdSelective(invoiceOrder);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<InvoiceOrderVO>> queryInvoiceOrderList(@Valid @RequestBody InvoiceOrderQuery query){
		return Result.success(invoiceOrderService.queryInvoiceOrderList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<InvoiceOrderVO>> getInvoiceOrderByPage(@Valid @RequestBody InvoiceOrderQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(invoiceOrderService.queryInvoiceOrderByPage(query));
	}
}
