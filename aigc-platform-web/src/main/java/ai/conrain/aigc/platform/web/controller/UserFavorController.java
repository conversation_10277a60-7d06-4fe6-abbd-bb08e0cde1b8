package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.UserFavorService;
import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.FavorCreationModel;
import ai.conrain.aigc.platform.service.model.biz.FavorImageDetail;
import ai.conrain.aigc.platform.service.model.biz.FavorImageModel;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserFavorConverter;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.service.model.request.UserFavorRequest;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * UserFavor控制器
 *
 * <AUTHOR>
 * @version UserFavorService.java v 0.1 2025-03-08 11:26:59
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/userFavor")
public class UserFavorController {

	/** userFavorService */
	@Autowired
	private UserFavorService userFavorService;
	
	@GetMapping("/getById/{id}")
	public Result<UserFavorVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(userFavorService.selectById(id));
	}
	
	@PostMapping("/addFavor")
	public Result<UserFavorVO> addFavor(@Valid @RequestBody UserFavorRequest request) {
		UserFavorVO userFavor = UserFavorConverter.request2VO(request);
		return Result.success(userFavorService.addFavor(userFavor));
	}

	@PostMapping("/removeFavor")
	public Result<UserFavorVO> removeFavor(@Valid @RequestBody UserFavorRequest request) {
		// 参数校验
		FavorTypeEnum type = FavorTypeEnum.getByCode(request.getType());
		AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "请求参数无效");

		UserFavorVO userFavor = UserFavorConverter.request2VO(request);
		return Result.success(userFavorService.removeFavor(userFavor));
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		userFavorService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody UserFavorVO userFavor){
		userFavorService.updateByIdSelective(userFavor);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<UserFavorVO>> queryUserFavorList(@Valid @RequestBody UserFavorQuery query){

		return Result.success(userFavorService.queryUserFavorList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<UserFavorVO>> getUserFavorByPage(@Valid @RequestBody UserFavorQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(userFavorService.queryUserFavorByPage(query));
	}

	@PostMapping("/queryFavorInfo")
	public Result<UserFavorVO> queryUserFavor(@Valid @RequestBody UserFavorQuery query){
		AssertUtil.assertNotNull(query.getItemId(), ResultCode.PARAM_INVALID, "itemId is null");
		FavorTypeEnum type = FavorTypeEnum.getByCode(query.getType());
		AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "type is null");
		UserFavorVO userFavor = userFavorService.validateUserFavor(type, query.getItemId());
		return Result.success(userFavor);
	}

	@PostMapping("/queryFavorInfoWithBlobs")
	public Result<UserFavorVO> getFavorInfo(@Valid @RequestBody UserFavorQuery query){
		AssertUtil.assertNotNull(query.getItemId(), ResultCode.PARAM_INVALID, "itemId is null");
		FavorTypeEnum type = FavorTypeEnum.getByCode(query.getType());
		AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "type is null");
		UserFavorVO userFavor = userFavorService.validateUserFavorWithBlobs(type, query.getItemId());
		return Result.success(userFavor);
	}

	@GetMapping("/getAllFavorInfoWithBlobs")
	public Result<List<UserFavorVO>> queryAllFavorInfoWithBlobs(){
		UserFavorQuery query = new UserFavorQuery();
		query.setUserId(OperationContextHolder.getOperatorUserId());
		return Result.success(userFavorService.queryUserFavorListWithBlobs(query));
	}

	@PostMapping("/queryFavorImg4ModelByPage")
	public Result<PageInfo<FavorCreationModel>> queryFavorImg4ModelByPage(@Valid @RequestBody UserFavorQuery query){
		return Result.success(userFavorService.getFavorImg4ModelByPage(query));
	}

	@PostMapping("/queryFavorImgWithoutModelId")
	public Result<List<FavorImageModel>> queryFavorImgWithoutModelId (@Valid @RequestBody UserFavorQuery query) {
		return Result.success(userFavorService.queryFavorImgWithoutModelId(query));
	}
	@PostMapping("/queryFavorDetail")
	public Result<FavorImageDetail> getFavorImages (@Valid @RequestBody UserFavorQuery query) {
		return Result.success(userFavorService.queryFavorDetail(query));
	}


	@GetMapping("/getModels4Favor")
	public Result<List<LoraOption>> getModels4Favor () {
		return Result.success(userFavorService.getModels4Favor());
	}
}
