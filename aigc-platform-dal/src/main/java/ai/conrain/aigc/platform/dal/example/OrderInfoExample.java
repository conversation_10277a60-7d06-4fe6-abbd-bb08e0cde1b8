package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.OrderInfoDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public OrderInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public OrderInfoExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public OrderInfoExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public OrderInfoExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIsNull() {
            addCriterion("master_user_id is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIsNotNull() {
            addCriterion("master_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdEqualTo(Integer value) {
            addCriterion("master_user_id =", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotEqualTo(Integer value) {
            addCriterion("master_user_id <>", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdGreaterThan(Integer value) {
            addCriterion("master_user_id >", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("master_user_id >=", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdLessThan(Integer value) {
            addCriterion("master_user_id <", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("master_user_id <=", value, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdIn(List<Integer> values) {
            addCriterion("master_user_id in", values, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotIn(List<Integer> values) {
            addCriterion("master_user_id not in", values, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdBetween(Integer value1, Integer value2) {
            addCriterion("master_user_id between", value1, value2, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("master_user_id not between", value1, value2, "masterUserId");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIsNull() {
            addCriterion("master_user_nick is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIsNotNull() {
            addCriterion("master_user_nick is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickEqualTo(String value) {
            addCriterion("master_user_nick =", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotEqualTo(String value) {
            addCriterion("master_user_nick <>", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickGreaterThan(String value) {
            addCriterion("master_user_nick >", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickGreaterThanOrEqualTo(String value) {
            addCriterion("master_user_nick >=", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLessThan(String value) {
            addCriterion("master_user_nick <", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLessThanOrEqualTo(String value) {
            addCriterion("master_user_nick <=", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickLike(String value) {
            addCriterion("master_user_nick like", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotLike(String value) {
            addCriterion("master_user_nick not like", value, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickIn(List<String> values) {
            addCriterion("master_user_nick in", values, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotIn(List<String> values) {
            addCriterion("master_user_nick not in", values, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickBetween(String value1, String value2) {
            addCriterion("master_user_nick between", value1, value2, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserNickNotBetween(String value1, String value2) {
            addCriterion("master_user_nick not between", value1, value2, "masterUserNick");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIsNull() {
            addCriterion("master_user_login_id is null");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIsNotNull() {
            addCriterion("master_user_login_id is not null");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdEqualTo(String value) {
            addCriterion("master_user_login_id =", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotEqualTo(String value) {
            addCriterion("master_user_login_id <>", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdGreaterThan(String value) {
            addCriterion("master_user_login_id >", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdGreaterThanOrEqualTo(String value) {
            addCriterion("master_user_login_id >=", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLessThan(String value) {
            addCriterion("master_user_login_id <", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLessThanOrEqualTo(String value) {
            addCriterion("master_user_login_id <=", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdLike(String value) {
            addCriterion("master_user_login_id like", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotLike(String value) {
            addCriterion("master_user_login_id not like", value, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdIn(List<String> values) {
            addCriterion("master_user_login_id in", values, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotIn(List<String> values) {
            addCriterion("master_user_login_id not in", values, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdBetween(String value1, String value2) {
            addCriterion("master_user_login_id between", value1, value2, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andMasterUserLoginIdNotBetween(String value1, String value2) {
            addCriterion("master_user_login_id not between", value1, value2, "masterUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIsNull() {
            addCriterion("operator_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIsNotNull() {
            addCriterion("operator_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdEqualTo(Integer value) {
            addCriterion("operator_user_id =", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotEqualTo(Integer value) {
            addCriterion("operator_user_id <>", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdGreaterThan(Integer value) {
            addCriterion("operator_user_id >", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("operator_user_id >=", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdLessThan(Integer value) {
            addCriterion("operator_user_id <", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("operator_user_id <=", value, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdIn(List<Integer> values) {
            addCriterion("operator_user_id in", values, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotIn(List<Integer> values) {
            addCriterion("operator_user_id not in", values, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdBetween(Integer value1, Integer value2) {
            addCriterion("operator_user_id between", value1, value2, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("operator_user_id not between", value1, value2, "operatorUserId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIsNull() {
            addCriterion("operator_user_nick is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIsNotNull() {
            addCriterion("operator_user_nick is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickEqualTo(String value) {
            addCriterion("operator_user_nick =", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotEqualTo(String value) {
            addCriterion("operator_user_nick <>", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickGreaterThan(String value) {
            addCriterion("operator_user_nick >", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickGreaterThanOrEqualTo(String value) {
            addCriterion("operator_user_nick >=", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLessThan(String value) {
            addCriterion("operator_user_nick <", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLessThanOrEqualTo(String value) {
            addCriterion("operator_user_nick <=", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickLike(String value) {
            addCriterion("operator_user_nick like", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotLike(String value) {
            addCriterion("operator_user_nick not like", value, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickIn(List<String> values) {
            addCriterion("operator_user_nick in", values, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotIn(List<String> values) {
            addCriterion("operator_user_nick not in", values, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickBetween(String value1, String value2) {
            addCriterion("operator_user_nick between", value1, value2, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserNickNotBetween(String value1, String value2) {
            addCriterion("operator_user_nick not between", value1, value2, "operatorUserNick");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIsNull() {
            addCriterion("operator_user_login_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIsNotNull() {
            addCriterion("operator_user_login_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdEqualTo(String value) {
            addCriterion("operator_user_login_id =", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotEqualTo(String value) {
            addCriterion("operator_user_login_id <>", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdGreaterThan(String value) {
            addCriterion("operator_user_login_id >", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdGreaterThanOrEqualTo(String value) {
            addCriterion("operator_user_login_id >=", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLessThan(String value) {
            addCriterion("operator_user_login_id <", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLessThanOrEqualTo(String value) {
            addCriterion("operator_user_login_id <=", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdLike(String value) {
            addCriterion("operator_user_login_id like", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotLike(String value) {
            addCriterion("operator_user_login_id not like", value, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdIn(List<String> values) {
            addCriterion("operator_user_login_id in", values, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotIn(List<String> values) {
            addCriterion("operator_user_login_id not in", values, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdBetween(String value1, String value2) {
            addCriterion("operator_user_login_id between", value1, value2, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOperatorUserLoginIdNotBetween(String value1, String value2) {
            addCriterion("operator_user_login_id not between", value1, value2, "operatorUserLoginId");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIsNull() {
            addCriterion("original_amount is null");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIsNotNull() {
            addCriterion("original_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountEqualTo(BigDecimal value) {
            addCriterion("original_amount =", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotEqualTo(BigDecimal value) {
            addCriterion("original_amount <>", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountGreaterThan(BigDecimal value) {
            addCriterion("original_amount >", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("original_amount >=", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountLessThan(BigDecimal value) {
            addCriterion("original_amount <", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("original_amount <=", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIn(List<BigDecimal> values) {
            addCriterion("original_amount in", values, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotIn(List<BigDecimal> values) {
            addCriterion("original_amount not in", values, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("original_amount between", value1, value2, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("original_amount not between", value1, value2, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNull() {
            addCriterion("pay_amount is null");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNotNull() {
            addCriterion("pay_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPayAmountEqualTo(BigDecimal value) {
            addCriterion("pay_amount =", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotEqualTo(BigDecimal value) {
            addCriterion("pay_amount <>", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThan(BigDecimal value) {
            addCriterion("pay_amount >", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_amount >=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThan(BigDecimal value) {
            addCriterion("pay_amount <", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("pay_amount <=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountIn(List<BigDecimal> values) {
            addCriterion("pay_amount in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotIn(List<BigDecimal> values) {
            addCriterion("pay_amount not in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_amount between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("pay_amount not between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayDetailIsNull() {
            addCriterion("pay_detail is null");
            return (Criteria) this;
        }

        public Criteria andPayDetailIsNotNull() {
            addCriterion("pay_detail is not null");
            return (Criteria) this;
        }

        public Criteria andPayDetailEqualTo(String value) {
            addCriterion("pay_detail =", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailNotEqualTo(String value) {
            addCriterion("pay_detail <>", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailGreaterThan(String value) {
            addCriterion("pay_detail >", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailGreaterThanOrEqualTo(String value) {
            addCriterion("pay_detail >=", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailLessThan(String value) {
            addCriterion("pay_detail <", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailLessThanOrEqualTo(String value) {
            addCriterion("pay_detail <=", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailLike(String value) {
            addCriterion("pay_detail like", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailNotLike(String value) {
            addCriterion("pay_detail not like", value, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailIn(List<String> values) {
            addCriterion("pay_detail in", values, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailNotIn(List<String> values) {
            addCriterion("pay_detail not in", values, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailBetween(String value1, String value2) {
            addCriterion("pay_detail between", value1, value2, "payDetail");
            return (Criteria) this;
        }

        public Criteria andPayDetailNotBetween(String value1, String value2) {
            addCriterion("pay_detail not between", value1, value2, "payDetail");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(String value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(String value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(String value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(String value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(String value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(String value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLike(String value) {
            addCriterion("order_status like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotLike(String value) {
            addCriterion("order_status not like", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<String> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<String> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(String value1, String value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(String value1, String value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNull() {
            addCriterion("product_code is null");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNotNull() {
            addCriterion("product_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualTo(String value) {
            addCriterion("product_code =", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualTo(String value) {
            addCriterion("product_code <>", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThan(String value) {
            addCriterion("product_code >", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_code >=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThan(String value) {
            addCriterion("product_code <", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualTo(String value) {
            addCriterion("product_code <=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLike(String value) {
            addCriterion("product_code like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotLike(String value) {
            addCriterion("product_code not like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeIn(List<String> values) {
            addCriterion("product_code in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotIn(List<String> values) {
            addCriterion("product_code not in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeBetween(String value1, String value2) {
            addCriterion("product_code between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotBetween(String value1, String value2) {
            addCriterion("product_code not between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductDetailIsNull() {
            addCriterion("product_detail is null");
            return (Criteria) this;
        }

        public Criteria andProductDetailIsNotNull() {
            addCriterion("product_detail is not null");
            return (Criteria) this;
        }

        public Criteria andProductDetailEqualTo(String value) {
            addCriterion("product_detail =", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailNotEqualTo(String value) {
            addCriterion("product_detail <>", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailGreaterThan(String value) {
            addCriterion("product_detail >", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailGreaterThanOrEqualTo(String value) {
            addCriterion("product_detail >=", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailLessThan(String value) {
            addCriterion("product_detail <", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailLessThanOrEqualTo(String value) {
            addCriterion("product_detail <=", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailLike(String value) {
            addCriterion("product_detail like", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailNotLike(String value) {
            addCriterion("product_detail not like", value, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailIn(List<String> values) {
            addCriterion("product_detail in", values, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailNotIn(List<String> values) {
            addCriterion("product_detail not in", values, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailBetween(String value1, String value2) {
            addCriterion("product_detail between", value1, value2, "productDetail");
            return (Criteria) this;
        }

        public Criteria andProductDetailNotBetween(String value1, String value2) {
            addCriterion("product_detail not between", value1, value2, "productDetail");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNull() {
            addCriterion("finish_time is null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIsNotNull() {
            addCriterion("finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andFinishTimeEqualTo(Date value) {
            addCriterion("finish_time =", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotEqualTo(Date value) {
            addCriterion("finish_time <>", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThan(Date value) {
            addCriterion("finish_time >", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("finish_time >=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThan(Date value) {
            addCriterion("finish_time <", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("finish_time <=", value, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeIn(List<Date> values) {
            addCriterion("finish_time in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotIn(List<Date> values) {
            addCriterion("finish_time not in", values, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeBetween(Date value1, Date value2) {
            addCriterion("finish_time between", value1, value2, "finishTime");
            return (Criteria) this;
        }

        public Criteria andFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("finish_time not between", value1, value2, "finishTime");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(OrderInfoDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(OrderInfoDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}