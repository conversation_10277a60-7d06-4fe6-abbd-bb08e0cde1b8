package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.entity.DistributorCustomerDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DistributorCustomerExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public DistributorCustomerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public DistributorCustomerExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public DistributorCustomerExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public DistributorCustomerExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdIsNull() {
            addCriterion("customer_master_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdIsNotNull() {
            addCriterion("customer_master_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdEqualTo(Integer value) {
            addCriterion("customer_master_user_id =", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorCorpNameEqualTo(String value) {
            addCriterion("distributor_corp_name =", value, "distributorCorpName");
            return (Criteria) this;
        }

        public Criteria andDistributorCorpOrgIdEqualTo(Integer value) {
            addCriterion("distributor_corp_org_id =", value, "distributorCorpOrgId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdNotEqualTo(Integer value) {
            addCriterion("customer_master_user_id <>", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdGreaterThan(Integer value) {
            addCriterion("customer_master_user_id >", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_master_user_id >=", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdLessThan(Integer value) {
            addCriterion("customer_master_user_id <", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_master_user_id <=", value, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdIn(List<Integer> values) {
            addCriterion("customer_master_user_id in", values, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdNotIn(List<Integer> values) {
            addCriterion("customer_master_user_id not in", values, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_master_user_id between", value1, value2, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andCustomerMasterUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_master_user_id not between", value1, value2, "customerMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdIsNull() {
            addCriterion("distributor_master_user_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdIsNotNull() {
            addCriterion("distributor_master_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdEqualTo(Integer value) {
            addCriterion("distributor_master_user_id =", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdNotEqualTo(Integer value) {
            addCriterion("distributor_master_user_id <>", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdGreaterThan(Integer value) {
            addCriterion("distributor_master_user_id >", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("distributor_master_user_id >=", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdLessThan(Integer value) {
            addCriterion("distributor_master_user_id <", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("distributor_master_user_id <=", value, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdIn(List<Integer> values) {
            addCriterion("distributor_master_user_id in", values, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdNotIn(List<Integer> values) {
            addCriterion("distributor_master_user_id not in", values, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdBetween(Integer value1, Integer value2) {
            addCriterion("distributor_master_user_id between", value1, value2, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorMasterUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("distributor_master_user_id not between", value1, value2, "distributorMasterUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdIsNull() {
            addCriterion("distributor_operator_user_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdIsNotNull() {
            addCriterion("distributor_operator_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdEqualTo(Integer value) {
            addCriterion("distributor_operator_user_id =", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdNotEqualTo(Integer value) {
            addCriterion("distributor_operator_user_id <>", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdGreaterThan(Integer value) {
            addCriterion("distributor_operator_user_id >", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("distributor_operator_user_id >=", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdLessThan(Integer value) {
            addCriterion("distributor_operator_user_id <", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("distributor_operator_user_id <=", value, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdIn(List<Integer> values) {
            addCriterion("distributor_operator_user_id in", values, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdNotIn(List<Integer> values) {
            addCriterion("distributor_operator_user_id not in", values, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdBetween(Integer value1, Integer value2) {
            addCriterion("distributor_operator_user_id between", value1, value2, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorOperatorUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("distributor_operator_user_id not between", value1, value2, "distributorOperatorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdIsNull() {
            addCriterion("distributor_sales_user_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdIsNotNull() {
            addCriterion("distributor_sales_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdEqualTo(Integer value) {
            addCriterion("distributor_sales_user_id =", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdNotEqualTo(Integer value) {
            addCriterion("distributor_sales_user_id <>", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdGreaterThan(Integer value) {
            addCriterion("distributor_sales_user_id >", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("distributor_sales_user_id >=", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdLessThan(Integer value) {
            addCriterion("distributor_sales_user_id <", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("distributor_sales_user_id <=", value, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdIn(List<Integer> values) {
            addCriterion("distributor_sales_user_id in", values, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdNotIn(List<Integer> values) {
            addCriterion("distributor_sales_user_id not in", values, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdBetween(Integer value1, Integer value2) {
            addCriterion("distributor_sales_user_id between", value1, value2, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorSalesUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("distributor_sales_user_id not between", value1, value2, "distributorSalesUserId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNull() {
            addCriterion("creator_id is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIsNotNull() {
            addCriterion("creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorIdEqualTo(Integer value) {
            addCriterion("creator_id =", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotEqualTo(Integer value) {
            addCriterion("creator_id <>", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThan(Integer value) {
            addCriterion("creator_id >", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creator_id >=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThan(Integer value) {
            addCriterion("creator_id <", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("creator_id <=", value, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdIn(List<Integer> values) {
            addCriterion("creator_id in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotIn(List<Integer> values) {
            addCriterion("creator_id not in", values, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdBetween(Integer value1, Integer value2) {
            addCriterion("creator_id between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andCreatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creator_id not between", value1, value2, "creatorId");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andCustomerLike(String value) {
            addCriterion(String.format("(customer_master_nick like %s or customer_master_corp_name like %s)", value, value));
            return (Criteria) this;
        }

        public Criteria andNoVisitIn15Days() {
            addCriterion("visit_in_15days = 0");
            return (Criteria) this;
        }

        public Criteria andCustomerMusePointLessThan500() {
            addCriterion("customer_muse_point < 500 && customer_total_topup_amount >= 3999");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(DistributorCustomerDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(DistributorCustomerDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}