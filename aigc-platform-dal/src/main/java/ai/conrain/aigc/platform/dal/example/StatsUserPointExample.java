package ai.conrain.aigc.platform.dal.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StatsUserPointExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public StatsUserPointExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = "stats_user_point." + orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public StatsUserPointExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public StatsUserPointExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public StatsUserPointExample page(Integer page, Integer pageSize) {
        this.offset = (page - 1) * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("stats_user_point.id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("stats_user_point.id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("stats_user_point.id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("stats_user_point.id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("stats_user_point.id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("stats_user_point.id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("stats_user_point.id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("stats_user_point.id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("stats_user_point.user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("stats_user_point.user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("stats_user_point.user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("stats_user_point.user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("stats_user_point.user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("stats_user_point.user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("stats_user_point.user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("stats_user_point.user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNickNameLike(String value) {
            addCriterion("user_vip_view.nick_name like", value, "nickName");
            return (Criteria) this;
        }

        public Criteria andDistributorCorpNameLike(String value) {
            addCriterion("user_vip_view.distributor_corp_name like", value, "distributorCorpName");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNull() {
            addCriterion("stats_user_point.stats_type is null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIsNotNull() {
            addCriterion("stats_user_point.stats_type is not null");
            return (Criteria) this;
        }

        public Criteria andStatsTypeEqualTo(String value) {
            addCriterion("stats_user_point.stats_type =", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotEqualTo(String value) {
            addCriterion("stats_user_point.stats_type <>", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThan(String value) {
            addCriterion("stats_user_point.stats_type >", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeGreaterThanOrEqualTo(String value) {
            addCriterion("stats_user_point.stats_type >=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThan(String value) {
            addCriterion("stats_user_point.stats_type <", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLessThanOrEqualTo(String value) {
            addCriterion("stats_user_point.stats_type <=", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeLike(String value) {
            addCriterion("stats_user_point.stats_type like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotLike(String value) {
            addCriterion("stats_user_point.stats_type not like", value, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeIn(List<String> values) {
            addCriterion("stats_user_point.stats_type in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotIn(List<String> values) {
            addCriterion("stats_user_point.stats_type not in", values, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeBetween(String value1, String value2) {
            addCriterion("stats_user_point.stats_type between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsTypeNotBetween(String value1, String value2) {
            addCriterion("stats_user_point.stats_type not between", value1, value2, "statsType");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNull() {
            addCriterion("stats_user_point.stats_date is null");
            return (Criteria) this;
        }

        public Criteria andStatsDateIsNotNull() {
            addCriterion("stats_user_point.stats_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatsDateEqualTo(String value) {
            addCriterion("stats_user_point.stats_date =", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotEqualTo(String value) {
            addCriterion("stats_user_point.stats_date <>", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThan(String value) {
            addCriterion("stats_user_point.stats_date >", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateGreaterThanOrEqualTo(String value) {
            addCriterion("stats_user_point.stats_date >=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThan(String value) {
            addCriterion("stats_user_point.stats_date <", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLessThanOrEqualTo(String value) {
            addCriterion("stats_user_point.stats_date <=", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateLike(String value) {
            addCriterion("stats_user_point.stats_date like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotLike(String value) {
            addCriterion("stats_user_point.stats_date not like", value, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateIn(List<String> values) {
            addCriterion("stats_user_point.stats_date in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotIn(List<String> values) {
            addCriterion("stats_user_point.stats_date not in", values, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateBetween(String value1, String value2) {
            addCriterion("stats_user_point.stats_date between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andStatsDateNotBetween(String value1, String value2) {
            addCriterion("stats_user_point.stats_date not between", value1, value2, "statsDate");
            return (Criteria) this;
        }

        public Criteria andPointConsumedIsNull() {
            addCriterion("stats_user_point.point_consumed is null");
            return (Criteria) this;
        }

        public Criteria andPointConsumedIsNotNull() {
            addCriterion("stats_user_point.point_consumed is not null");
            return (Criteria) this;
        }

        public Criteria andPointConsumedEqualTo(Integer value) {
            addCriterion("stats_user_point.point_consumed =", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedNotEqualTo(Integer value) {
            addCriterion("stats_user_point.point_consumed <>", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedGreaterThan(Integer value) {
            addCriterion("stats_user_point.point_consumed >", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.point_consumed >=", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedLessThan(Integer value) {
            addCriterion("stats_user_point.point_consumed <", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.point_consumed <=", value, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedIn(List<Integer> values) {
            addCriterion("stats_user_point.point_consumed in", values, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedNotIn(List<Integer> values) {
            addCriterion("stats_user_point.point_consumed not in", values, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.point_consumed between", value1, value2, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andPointConsumedNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.point_consumed not between", value1, value2, "pointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedIsNull() {
            addCriterion("stats_user_point.give_point_consumed is null");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedIsNotNull() {
            addCriterion("stats_user_point.give_point_consumed is not null");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedEqualTo(Integer value) {
            addCriterion("stats_user_point.give_point_consumed =", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedNotEqualTo(Integer value) {
            addCriterion("stats_user_point.give_point_consumed <>", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedGreaterThan(Integer value) {
            addCriterion("stats_user_point.give_point_consumed >", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.give_point_consumed >=", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedLessThan(Integer value) {
            addCriterion("stats_user_point.give_point_consumed <", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.give_point_consumed <=", value, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedIn(List<Integer> values) {
            addCriterion("stats_user_point.give_point_consumed in", values, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedNotIn(List<Integer> values) {
            addCriterion("stats_user_point.give_point_consumed not in", values, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.give_point_consumed between", value1, value2, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andGivePointConsumedNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.give_point_consumed not between", value1, value2, "givePointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedIsNull() {
            addCriterion("stats_user_point.exp_point_consumed is null");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedIsNotNull() {
            addCriterion("stats_user_point.exp_point_consumed is not null");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedEqualTo(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed =", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedNotEqualTo(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed <>", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedGreaterThan(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed >", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed >=", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedLessThan(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed <", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.exp_point_consumed <=", value, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedIn(List<Integer> values) {
            addCriterion("stats_user_point.exp_point_consumed in", values, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedNotIn(List<Integer> values) {
            addCriterion("stats_user_point.exp_point_consumed not in", values, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.exp_point_consumed between", value1, value2, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andExpPointConsumedNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.exp_point_consumed not between", value1, value2, "expPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedIsNull() {
            addCriterion("stats_user_point.model_point_consumed is null");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedIsNotNull() {
            addCriterion("stats_user_point.model_point_consumed is not null");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedEqualTo(Integer value) {
            addCriterion("stats_user_point.model_point_consumed =", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedNotEqualTo(Integer value) {
            addCriterion("stats_user_point.model_point_consumed <>", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedGreaterThan(Integer value) {
            addCriterion("stats_user_point.model_point_consumed >", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedGreaterThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.model_point_consumed >=", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedLessThan(Integer value) {
            addCriterion("stats_user_point.model_point_consumed <", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedLessThanOrEqualTo(Integer value) {
            addCriterion("stats_user_point.model_point_consumed <=", value, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedIn(List<Integer> values) {
            addCriterion("stats_user_point.model_point_consumed in", values, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedNotIn(List<Integer> values) {
            addCriterion("stats_user_point.model_point_consumed not in", values, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.model_point_consumed between", value1, value2, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andModelPointConsumedNotBetween(Integer value1, Integer value2) {
            addCriterion("stats_user_point.model_point_consumed not between", value1, value2, "modelPointConsumed");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountIsNull() {
            addCriterion("stats_user_point.recharge_amount is null");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountIsNotNull() {
            addCriterion("stats_user_point.recharge_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountEqualTo(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount =", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountNotEqualTo(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount <>", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountGreaterThan(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount >", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount >=", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountLessThan(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount <", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stats_user_point.recharge_amount <=", value, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountIn(List<BigDecimal> values) {
            addCriterion("stats_user_point.recharge_amount in", values, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountNotIn(List<BigDecimal> values) {
            addCriterion("stats_user_point.recharge_amount not in", values, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stats_user_point.recharge_amount between", value1, value2, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andRechargeAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stats_user_point.recharge_amount not between", value1, value2, "rechargeAmount");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("stats_user_point.create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("stats_user_point.create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("stats_user_point.create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("stats_user_point.create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("stats_user_point.create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("stats_user_point.create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("stats_user_point.create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("stats_user_point.create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("stats_user_point.create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("stats_user_point.create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("stats_user_point.create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("stats_user_point.create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("stats_user_point.modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("stats_user_point.modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("stats_user_point.modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("stats_user_point.modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("stats_user_point.modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("stats_user_point.modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("stats_user_point.modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("stats_user_point.modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("stats_user_point.modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("stats_user_point.modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("stats_user_point.modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("stats_user_point.modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}