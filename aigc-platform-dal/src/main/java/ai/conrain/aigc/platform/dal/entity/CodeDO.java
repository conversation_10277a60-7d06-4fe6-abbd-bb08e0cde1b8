package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 码表
 * 对应数据表：code
 */
@Data
public class CodeDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 码值，全局唯一
     */
    private String code;

    /**
     * 码类型，registerPromotion:推广注册
     */
    private String codeType;

    /**
     * 码状态，valid:有效|invalid:已失效
     */
    private String codeStatus;

    /**
     * 码的信息
     */
    private String codeInfo;

    /**
     * 关联的用户
     */
    private Integer relatedUserId;

    /**
     * 创建人主账号id
     */
    private Integer creatorMasterId;

    /**
     * 创建人id
     */
    private Integer creatorId;

    /**
     * 最近修改人id
     */
    private Integer modifierId;

    /**
     * 预留扩展
     */
    private String extInfo;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}