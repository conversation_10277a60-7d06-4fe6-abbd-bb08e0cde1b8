package ai.conrain.aigc.platform.dal.example;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ImageGroupCaptionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer rows;

    private Integer offset;

    public ImageGroupCaptionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return rows;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdIsNull() {
            addCriterion("image_group_id is null");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdIsNotNull() {
            addCriterion("image_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdEqualTo(Integer value) {
            addCriterion("image_group_id =", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdNotEqualTo(Integer value) {
            addCriterion("image_group_id <>", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdGreaterThan(Integer value) {
            addCriterion("image_group_id >", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_group_id >=", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdLessThan(Integer value) {
            addCriterion("image_group_id <", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("image_group_id <=", value, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdIn(List<Integer> values) {
            addCriterion("image_group_id in", values, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdNotIn(List<Integer> values) {
            addCriterion("image_group_id not in", values, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("image_group_id between", value1, value2, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("image_group_id not between", value1, value2, "imageGroupId");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsIsNull() {
            addCriterion("image_caption_ids is null");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsIsNotNull() {
            addCriterion("image_caption_ids is not null");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsEqualTo(String value) {
            addCriterion("image_caption_ids =", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsNotEqualTo(String value) {
            addCriterion("image_caption_ids <>", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsGreaterThan(String value) {
            addCriterion("image_caption_ids >", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsGreaterThanOrEqualTo(String value) {
            addCriterion("image_caption_ids >=", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsLessThan(String value) {
            addCriterion("image_caption_ids <", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsLessThanOrEqualTo(String value) {
            addCriterion("image_caption_ids <=", value, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsIn(List<String> values) {
            addCriterion("image_caption_ids in", values, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsNotIn(List<String> values) {
            addCriterion("image_caption_ids not in", values, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsBetween(String value1, String value2) {
            addCriterion("image_caption_ids between", value1, value2, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andImageCaptionIdsNotBetween(String value1, String value2) {
            addCriterion("image_caption_ids not between", value1, value2, "imageCaptionIds");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("result is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("result is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(String value) {
            addCriterion("result =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(String value) {
            addCriterion("result <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(String value) {
            addCriterion("result >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(String value) {
            addCriterion("result >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThan(String value) {
            addCriterion("result <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(String value) {
            addCriterion("result <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<String> values) {
            addCriterion("result in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<String> values) {
            addCriterion("result not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(String value1, String value2) {
            addCriterion("result between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(String value1, String value2) {
            addCriterion("result not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNull() {
            addCriterion("caption is null");
            return (Criteria) this;
        }

        public Criteria andCaptionIsNotNull() {
            addCriterion("caption is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionEqualTo(String value) {
            addCriterion("caption =", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotEqualTo(String value) {
            addCriterion("caption <>", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThan(String value) {
            addCriterion("caption >", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionGreaterThanOrEqualTo(String value) {
            addCriterion("caption >=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThan(String value) {
            addCriterion("caption <", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLessThanOrEqualTo(String value) {
            addCriterion("caption <=", value, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionIn(List<String> values) {
            addCriterion("caption in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotIn(List<String> values) {
            addCriterion("caption not in", values, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionBetween(String value1, String value2) {
            addCriterion("caption between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionNotBetween(String value1, String value2) {
            addCriterion("caption not between", value1, value2, "caption");
            return (Criteria) this;
        }

        public Criteria andCaptionLogIsNull() {
            addCriterion("caption_log is null");
            return (Criteria) this;
        }

        public Criteria andCaptionLogIsNotNull() {
            addCriterion("caption_log is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionLogEqualTo(String value) {
            addCriterion("caption_log =", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogNotEqualTo(String value) {
            addCriterion("caption_log <>", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogGreaterThan(String value) {
            addCriterion("caption_log >", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogGreaterThanOrEqualTo(String value) {
            addCriterion("caption_log >=", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogLessThan(String value) {
            addCriterion("caption_log <", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogLessThanOrEqualTo(String value) {
            addCriterion("caption_log <=", value, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogIn(List<String> values) {
            addCriterion("caption_log in", values, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogNotIn(List<String> values) {
            addCriterion("caption_log not in", values, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogBetween(String value1, String value2) {
            addCriterion("caption_log between", value1, value2, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionLogNotBetween(String value1, String value2) {
            addCriterion("caption_log not between", value1, value2, "captionLog");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIsNull() {
            addCriterion("caption_version is null");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIsNotNull() {
            addCriterion("caption_version is not null");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionEqualTo(String value) {
            addCriterion("caption_version =", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotEqualTo(String value) {
            addCriterion("caption_version <>", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionGreaterThan(String value) {
            addCriterion("caption_version >", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionGreaterThanOrEqualTo(String value) {
            addCriterion("caption_version >=", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLessThan(String value) {
            addCriterion("caption_version <", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLessThanOrEqualTo(String value) {
            addCriterion("caption_version <=", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionLike(String value) {
            addCriterion("caption_version like", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotLike(String value) {
            addCriterion("caption_version not like", value, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionIn(List<String> values) {
            addCriterion("caption_version in", values, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotIn(List<String> values) {
            addCriterion("caption_version not in", values, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionBetween(String value1, String value2) {
            addCriterion("caption_version between", value1, value2, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCaptionVersionNotBetween(String value1, String value2) {
            addCriterion("caption_version not between", value1, value2, "captionVersion");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }

        public Criteria andLogicalDeleted(boolean deleted) {
            return deleted ? andDeletedEqualTo(ImageGroupCaptionDO.Deleted.IS_DELETED.value()) : andDeletedNotEqualTo(ImageGroupCaptionDO.Deleted.IS_DELETED.value());
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}