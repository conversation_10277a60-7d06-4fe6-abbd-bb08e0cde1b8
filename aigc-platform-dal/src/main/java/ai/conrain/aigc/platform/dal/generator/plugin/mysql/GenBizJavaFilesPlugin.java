package ai.conrain.aigc.platform.dal.generator.plugin.mysql;

import ai.conrain.aigc.platform.dal.generator.entity.ConfigInfo;
import com.alibaba.fastjson.JSON;
import com.google.common.base.CaseFormat;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.mybatis.generator.api.GeneratedJavaFile;
import org.mybatis.generator.api.IntrospectedColumn;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.config.TableConfiguration;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Properties;
import java.util.Set;

/**
 * mybatis generator插件，自动生成vo/query/converter/service/service impl/controller
 */
public class GenBizJavaFilesPlugin extends PluginAdapter {

    public Properties getProjectProperties() {
        InputStream inputStream = GenBizJavaFilesPlugin.class.getClassLoader().getResourceAsStream(
                "generator/mysql/generator-config.properties");
        try {
            properties.load(inputStream);
            return properties;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public List<GeneratedJavaFile> contextGenerateAdditionalJavaFiles(IntrospectedTable introspectedTable) {

        String tableName = introspectedTable.getFullyQualifiedTableNameAtRuntime();
        String domainName = introspectedTable.getFullyQualifiedTable().getDomainObjectName();
        String tableComment = introspectedTable.getRemarks();
        String targetRuntime = introspectedTable.getContext().getTargetRuntime();

        //模板参数
        ConfigInfo cfg = new ConfigInfo();
        {
            //user_info
            cfg.setTable(tableName);
            //UserInfo
            cfg.setEntityName(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, tableName));
            //userInfo
            cfg.setObjectName(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, tableName));
            //表对象备注
            cfg.setEntityComment(org.apache.commons.lang3.StringUtils.stripEnd(tableComment, "表"));
            //列信息
            fillColumns(cfg, introspectedTable);
            //各种包路径配置
            cfg.setEntityUrl(p("pkg.entity"));
            cfg.setExampleUrl(p("pkg.example"));
            cfg.setDaoUrl(p("pkg.dao"));
            cfg.setMapperUrl(p("pkg.sqlmap"));
            cfg.setConverterPackageUrl(p("pkg.converter"));
            cfg.setServiceUrl(p("pkg.service"));
            cfg.setServiceImplUrl(p("pkg.serviceImpl"));
            cfg.setVoPackageUrl(p("pkg.vo"));
            cfg.setControllerUrl(p("pkg.controller"));
            cfg.setConverterPackageUrl(p("pkg.converter"));
            cfg.setQueryPackageUrl(p("pkg.query"));

            cfg.setAuthor(p("base.author"));
            cfg.setVersion(p("base.version"));
            cfg.setCreateTime(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date()));
            cfg.setOverwrite(true);

            //简单模式
            if (targetRuntime.equals("MyBatis3Simple")) {
                //简单模式不支持逻辑删除，这里直接覆盖强制指定
                cfg.setLogicDelete(false);
            }
            cfg.setTargetRuntime(targetRuntime);

            TableConfiguration tableConfig = introspectedTable.getTableConfiguration();
            cfg.setEnableDeleteByPrimaryKey(tableConfig.isDeleteByPrimaryKeyStatementEnabled());
            cfg.setEnableUpdateByPrimaryKey(tableConfig.isUpdateByPrimaryKeyStatementEnabled());
            cfg.setEnableDeleteByExample(tableConfig.isDeleteByExampleStatementEnabled());
            cfg.setEnableUpdateByExample(tableConfig.isUpdateByExampleStatementEnabled());
        }

        System.out.println("[GenBizJavaFilesPlugin] cfg:\n" + JSON.toJSONString(cfg));

        //gen java files by template
        genExtJavaFiles(p("proj.vo"), p("pkg.vo"), "vo.ftl", cfg, "VO.java", tableName);
        genExtJavaFiles(p("proj.query"), p("pkg.query"), "query.ftl", cfg, "Query.java", tableName);
        genExtJavaFiles(p("proj.converter"), p("pkg.converter"), "converter.ftl", cfg, "Converter.java", tableName);
        genExtJavaFiles(p("proj.service"), p("pkg.service"), "service.ftl", cfg, "Service.java", tableName);
        genExtJavaFiles(p("proj.serviceImpl"), p("pkg.serviceImpl"), "serviceImpl.ftl", cfg, "ServiceImpl.java",
            tableName);
        genExtJavaFiles(p("proj.controller"), p("pkg.controller"), "controller.ftl", cfg, "Controller.java", tableName);

        return super.contextGenerateAdditionalJavaFiles(introspectedTable);
    }

    private void genExtJavaFiles(String targetProject, String targetPackage, String templateName, ConfigInfo ci,
                                 String fileNameSuffix, String tableName) {

        String fileName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, tableName) + fileNameSuffix;
        System.out.println("开始生成" + fileName);

        if (StringUtils.isEmpty(targetProject)) {
            throw new RuntimeException(fileName + "生成失败，properties配置中缺少对应参数:" + "targetProject");
        }
        if (StringUtils.isEmpty(targetPackage)) {
            throw new RuntimeException(fileName + "生成失败，properties配置中缺少对应参数:" + "targetPackage");
        }

        //目标文件（项目根目录+目标模块+包名+文件名）
        String targetFile = StringUtils.collectionToDelimitedString(
            Arrays.asList(System.getProperty("user.dir"), targetProject, targetPackage.replace(".", File.separator),
                fileName), File.separator);

        //渲染模板
        createFile(ci, templateName, targetFile);
    }

    private void fillColumns(ConfigInfo cfg, IntrospectedTable tbl) {

        List<ConfigInfo.PropertyInfo> columns = new ArrayList<>();

        Set<String> entityJavaImports = new HashSet<>();

        List<IntrospectedColumn> cols = tbl.getAllColumns();
        for (IntrospectedColumn c : cols) {
            ConfigInfo.PropertyInfo pi = new ConfigInfo.PropertyInfo();
            {
                pi.setColumn(c.getActualColumnName());
                if (StringUtils.isEmpty(c.getRemarks())) {
                    pi.setComment(c.getJavaProperty());
                } else {
                    pi.setComment(c.getRemarks());
                }
                pi.setProperty(c.getJavaProperty());
                pi.setJdbcType(c.getJdbcTypeName());
                pi.setUpFiled(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, c.getActualColumnName()));
                pi.setJavaType(c.getFullyQualifiedJavaType().getShortNameWithoutTypeArguments());
                pi.setFullyJavaType(c.getFullyQualifiedJavaType().getFullyQualifiedNameWithoutTypeParameters());

                if (!StringUtils.isEmpty(pi.getFullyJavaType()) && !pi.getFullyJavaType().contains("java.lang")) {
                    entityJavaImports.add(pi.getFullyJavaType());
                }
            }

            columns.add(pi);

            //id的类型
            if ("id".equalsIgnoreCase(c.getActualColumnName())) {
                cfg.setIdType(pi.getJavaType());
                cfg.setIdJdbcType(pi.getJdbcType());
            }

            //逻辑删除标识
            if ("1".equals(p("base.enableLogicalDel")) && "deleted".equalsIgnoreCase(c.getActualColumnName())) {
                cfg.setLogicDelete(true);
            }
        }

        //列视图
        cfg.setColumns(columns);

        //java imports
        cfg.setEntityJavaImports(new ArrayList<>(entityJavaImports));
    }

    private String p(String k) {
        return this.getProjectProperties().getProperty(k);
    }

    private void createFile(ConfigInfo dataModel, String templateName, String filePath) {
        FileWriter out = null;
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_28);
            configuration.setClassForTemplateLoading(this.getClass(), "/generator/template");
            configuration.setDefaultEncoding("utf-8");

            Template template = configuration.getTemplate(templateName);
            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            boolean existingOverwrite = false;

            if (!file.exists()) {
                file.createNewFile();
            } else {
                if (!dataModel.isOverwrite()) {
                    return;
                } else {
                    existingOverwrite = true;
                }
            }

            //设置输出流
            out = new FileWriter(file);
            //模板输出静态文件
            template.process(dataModel, out);

            if (existingOverwrite) {
                System.err.println("Existing file " + filePath + " was overwritten");
            }

            System.out.println("创建" + file.getName() + "成功");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
