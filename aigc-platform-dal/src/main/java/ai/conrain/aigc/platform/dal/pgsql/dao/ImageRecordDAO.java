package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageRecordExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageRecordDAO {
    List<ImageRecordDO> findPageable(@Param("query") ImageRecordExample query);
    long countPageable(@Param("query") ImageRecordExample query);
    List<ImageRecordDO> findImageGroupPageable(@Param("query") ImageRecordExample query);
    long countImageGroupPageable(@Param("query") ImageRecordExample query);
}
