package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.OrderSettlementVO;
import ai.conrain.aigc.platform.service.model.query.OrderSettlementQuery;
import ai.conrain.aigc.platform.service.component.OrderSettlementService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;

import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;

/**
 * OrderSettlement控制器
 *
 * <AUTHOR>
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/orderSettlement")
public class OrderSettlementController {

	/** orderSettlementService */
	@Autowired
	private OrderSettlementService orderSettlementService;

	@PostMapping("/queryByPage")
	public Result<PageInfo<OrderSettlementVO>> getOrderSettlementByPage(@Valid @RequestBody OrderSettlementQuery query) {
		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
				|| query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}

		if (RoleTypeEnum.DISTRIBUTOR == OperationContextHolder.getContext().getRoleType()) {
			query.setDistributorCorpId(OperationContextHolder.getCorpOrgId());
		}

		if (StringUtils.isBlank(query.getOrderBy())){
			query.setOrderBy("order_finish_time desc");
		}

		return Result.success(orderSettlementService.queryOrderSettlementByPage(query));
	}
}
