package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.PostgresExampleExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.PostgresExampleDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PostgresExampleDAO {
    long countByExample(PostgresExampleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PostgresExampleDO record);

    int insertSelective(PostgresExampleDO record);

    List<PostgresExampleDO> selectByExample(PostgresExampleExample example);

    PostgresExampleDO selectByPrimaryKey(Long id);

    PostgresExampleDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Long id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") PostgresExampleDO record, @Param("example") PostgresExampleExample example);

    int updateByExample(@Param("record") PostgresExampleDO record, @Param("example") PostgresExampleExample example);

    int updateByPrimaryKeySelective(PostgresExampleDO record);

    int updateByPrimaryKey(PostgresExampleDO record);

    int logicalDeleteByExample(@Param("example") PostgresExampleExample example);

    int logicalDeleteByPrimaryKey(Long id);
}