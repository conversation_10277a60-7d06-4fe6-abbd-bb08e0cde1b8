package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 图片案例表
 * 对应数据表：image_case
 */
@Data
public class ImageCaseDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 图片url
     */
    private String url;

    /**
     * 缩略图片url
     */
    private String miniUrl;

    /**
     * 任务id
     */
    private Integer taskId;

    /**
     * 批次id
     */
    private Integer batchId;

    /**
     * 存储地址
     */
    private String storePath;

    /**
     * 存储服务器
     */
    private String storeServer;

    /**
     * 标签列表，多个以逗号隔开
     */
    private String tags;

    /**
     * 状态，ENABLED、DISABLED
     */
    private String status;

    /**
     * 备注
     */
    private String memo;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     *  同步状态
     */
    private Boolean syncStatus;

    /**
     * 同步时间
     */
    private Date syncTime;


    /** 同步次数 */
    private Integer reSyncCount;

    /** 扩展信息 */
    private String extInfo;


    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    public String getMiniUrl() {
        return miniUrl;
    }

    public void setMiniUrl(String miniUrl) {
        this.miniUrl = miniUrl == null ? null : miniUrl.trim();
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public String getStorePath() {
        return storePath;
    }

    public void setStorePath(String storePath) {
        this.storePath = storePath == null ? null : storePath.trim();
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Boolean getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Boolean syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Date getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(Date syncTime) {
        this.syncTime = syncTime;
    }

    public Integer getReSyncCount() {
        return reSyncCount;
    }

    public void setReSyncCount(Integer reSyncCount) {
        this.reSyncCount = reSyncCount;
    }

}