package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;


@Data
public class IntelligentRecommendationRequest {

    /**
     * 服装类型(需要进行转换 upper garment/lower garment/outfit)
     *
     * @see ai.conrain.aigc.platform.service.enums.ClothTypeEnum
     */
    @NotBlank(message = "服装类型不为能空")
    private String clothType;

    /** 配置列表 */
    private List<String> configKeys;

    /** 图片抠图id */
    private Integer pictureMattingId;

    /** 服装样本图片地址列表 */
    private List<String> clothImgUrls;

    /** 服装类型列表 */
    private List<String> clothesTypeList;

    /** 服装性别类型 */
    private String genderType;

    /** 年龄范围 */
    private String ageRange;

    /**
     * 服装类型 转换
     *
     * @return 服装类型
     */
    public String getClothType() {
        if (clothType == null) {
            return null;
        }

        switch (clothType.toLowerCase()) {
            case "upper garment":
                return ClothTypeEnum.Tops.getCode();
            case "lower garment":
                return ClothTypeEnum.Bottoms.getCode();
            case "outfit":
                return ClothTypeEnum.TwoPiece.getCode();
            default:
                return clothType;
        }
    }
}
