package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.acm.AcmConfigService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserReviewActionEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.request.AssignCustomer2DistributorRequest;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.request.UserUpdate;
import ai.conrain.aigc.platform.service.model.vo.RelatedAccounts;
import ai.conrain.aigc.platform.service.model.vo.UserReviewInfo;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import ai.conrain.aigc.platform.service.validation.EnumValid;
import ai.conrain.aigc.platform.web.helper.SessionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * User控制器
 *
 * <AUTHOR>
 * @version UserService.java v 0.1 2024-01-20 01:21:36
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;
    @Autowired
    private SessionHelper sessionHelper;
    @Autowired
    private TairService tairService;
    @Autowired
    private AcmConfigService acmConfigService;

    @Lazy
    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @GetMapping("/getUserById/{id}")
    public Result<UserVO> getUserById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(userService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody UserRegister user) {
        RoleTypeEnum roleType = RoleTypeEnum.getByCode(user.getRoleType());
        if (null == roleType) {
            log.warn("添加用户失败，参数非法，user={}", user);
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "新建用户信息失败");
        }

        //创建渠道商时，默认初始化关联账号（商家账号、演示账号）
        if (roleType == RoleTypeEnum.DISTRIBUTOR) {
            user.setInitDistributorRelatedAccounts(true);
        }

        UserVO data = userService.create(user);
        return Result.success(data.getId());
    }

    @PostMapping("/genTestMobile")
    public Result<String> genTestMobile() {
        return Result.success(userService.genTestMobile());
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        userService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/update/back")
    public Result<?> updateBack(@Valid @RequestBody UserUpdate user) {
        RoleTypeEnum roleType = RoleTypeEnum.getByCode(user.getRoleType());
        if (null == roleType) {
            log.error("后台修改用户失败，参数非法，user={}", user);
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "修改用户信息失败");
        }

        userService.updateBack(user);
        return Result.success();
    }

    @PostMapping("/update/front")
    public Result<?> updateFront(@Valid @RequestBody UserUpdate user) {
        userService.updateFront(user);
        return Result.success();
    }

    @PostMapping("/getPageUser")
    public Result<PageInfo<UserVO>> getUserByPage(@Valid @RequestBody UserQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1
            || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }

        if (!OperationContextHolder.isAdmin() && query.getMasterId() == null) {
            query.setMasterId(OperationContextHolder.getMasterUserId());
        }

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("id desc");
        }

        return Result.success(userService.queryUserByPage(query));
    }

    @GetMapping("/myInfo")
    public Result<UserVO> myInfo() {
        UserVO userVO = userService.selectById(OperationContextHolder.getOperatorUserId());
        OperationContextHolder.getContext().setLoginUser(userVO);

        userVO.setMobile(SecurityUtils.maskPhoneNumber(userVO.getMobile()));
        userVO.setLoginId(SecurityUtils.maskPhoneNumber(userVO.getLoginId()));
        userVO.setMasterLoginId(SecurityUtils.maskPhoneNumber(userVO.getMasterLoginId()));
        return Result.success(userVO);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostMapping("/allMaster")
    public Result<List<UserVO>> queryAllMasterMetaInfo(@JsonArg List roleTypes) {
        if (CollectionUtils.isEmpty(roleTypes)) {
            roleTypes = Collections.singletonList(RoleTypeEnum.MERCHANT.getCode());
        }
        List<UserVO> list = userService.queryAllMasterMetaInfo(roleTypes);
        return Result.success(list);
    }


    @SuppressWarnings({"unchecked", "rawtypes"})
    @PostMapping("/allByRoleTypes")
    public Result<List<UserVO>> queryAllByRoleTypes(@JsonArg List roleTypes) {
        if (CollectionUtils.isEmpty(roleTypes)) {
            roleTypes = Collections.singletonList(RoleTypeEnum.MERCHANT.getCode());
        }
        List<UserVO> list = userService.queryAllByRoleTypes(roleTypes);
        return Result.success(list);
    }


    @PostMapping("/querySub/{userId}")
    public Result<List<UserVO>> queryAllSubByUserId(@PathVariable Integer userId) {
        if (userId == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户 id 不能为空！");
        }

        UserQuery userQuery = new UserQuery();
        userQuery.setMasterId(userId);
        userQuery.setUserType(UserTypeEnum.SUB.getCode());
        List<UserVO> list =  userService.queryUsers(userQuery);
        return Result.success(list);
    }

    @PostMapping("/review")
    public Result<?> reviewUser(@JsonArg @NotNull Integer id,
                                @JsonArg @NotBlank @EnumValid(UserReviewActionEnum.class) String reviewAction,
                                @JsonArg String reviewMemo) {
        UserVO user = userService.selectById(id);
        if (user == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户不存在");
        }
        if (user.getStatus() != UserStatusEnum.UNDER_REVIEW) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "用户状态不对");
        }

        AssertUtil.assertNotNull(user.getUserReviewInfo(), "审核中的审核信息不存在");

        UserVO target = new UserVO();
        target.setId(id);

        UserReviewInfo reviewInfo = user.getUserReviewInfo();
        reviewInfo.setReviewAction(reviewAction);
        reviewInfo.setReviewMemo(reviewMemo);
        reviewInfo.setReviewerUserId(OperationContextHolder.getOperatorUserId());
        reviewInfo.setReviewTime(new Date());

        target.setUserReviewInfo(reviewInfo);
        if (UserReviewActionEnum.PASS.getCode().equals(reviewAction)) {
            target.setStatus(UserStatusEnum.ENABLED);
        } else if (UserReviewActionEnum.REJECT.getCode().equals(reviewAction)) {
            target.setStatus(UserStatusEnum.REJECT);
        }

        userService.updateByIdSelective(target);

        return Result.success();
    }

    @PostMapping("/getUserCount")
    public Result<Long> getUserCount(@Valid @RequestBody UserQuery query) {
        if (query == null || query.getStatus() == null) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "查询请求参数非法");
        }
        return Result.success(userService.queryUserCount(query));
    }

    @PostMapping("/assignCustomer2Distributor")
    public Result<?> assignCustomer2Distributor(@Valid @RequestBody AssignCustomer2DistributorRequest request) {
        userService.assignCustomer2Distributor(request.getUserCorpName(), request.getUserId(), request.getDistributorOrgIdPath(), true);
        return Result.success();
    }

    @PostMapping("/queryRelatedAccounts")
    public Result<RelatedAccounts> queryRelatedAccounts() {
        return Result.success(userService.queryRelatedAccounts(OperationContextHolder.getOperatorUserId()));
    }

    @GetMapping("/isVip")
    public Result<Boolean> isVip() {
        return Result.success(userService.isVip(OperationContextHolder.getMasterUserId()));
    }

    @GetMapping("/isTrialAccount")
    public Result<Boolean> isTrialAccount() {
        return Result.success(userService.isTrialAccount(OperationContextHolder.getMasterUserId()));
    }
}
