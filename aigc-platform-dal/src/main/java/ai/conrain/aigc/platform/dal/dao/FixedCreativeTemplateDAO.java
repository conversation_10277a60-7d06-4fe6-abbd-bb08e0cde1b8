package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.FixedCreativeTemplateDO;
import ai.conrain.aigc.platform.dal.example.FixedCreativeTemplateExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FixedCreativeTemplateDAO {
    long countByExample(FixedCreativeTemplateExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(FixedCreativeTemplateDO record);

    int insertSelective(FixedCreativeTemplateDO record);

    List<FixedCreativeTemplateDO> selectByExampleWithBLOBs(FixedCreativeTemplateExample example);

    List<FixedCreativeTemplateDO> selectByExample(FixedCreativeTemplateExample example);

    FixedCreativeTemplateDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") FixedCreativeTemplateDO record, @Param("example") FixedCreativeTemplateExample example);

    int updateByExampleWithBLOBs(@Param("record") FixedCreativeTemplateDO record, @Param("example") FixedCreativeTemplateExample example);

    int updateByExample(@Param("record") FixedCreativeTemplateDO record, @Param("example") FixedCreativeTemplateExample example);

    int updateByPrimaryKeySelective(FixedCreativeTemplateDO record);

    int updateByPrimaryKeyWithBLOBs(FixedCreativeTemplateDO record);

    int updateByPrimaryKey(FixedCreativeTemplateDO record);
}