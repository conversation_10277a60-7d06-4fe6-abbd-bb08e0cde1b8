package ai.conrain.aigc.platform.dal.config;

import com.pgvector.PGvector;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PGvector类型处理器
 * 用于处理PostgreSQL的vector类型与Java的PGvector类型之间的转换
 */
public class VectorTypeHandler extends BaseTypeHandler<PGvector> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, PGvector parameter, JdbcType jdbcType) throws SQLException {
        ps.setObject(i, parameter);
    }

    @Override
    public PGvector getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        if (obj == null) {
            return null;
        }
        if (obj instanceof PGvector) {
            return (PGvector) obj;
        }
        if (obj instanceof PGobject) {
            PGobject pgObj = (PGobject) obj;
            return new PGvector(pgObj.getValue());
        }
        return new PGvector(obj.toString());
    }

    @Override
    public PGvector getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        if (obj instanceof PGvector) {
            return (PGvector) obj;
        }
        if (obj instanceof PGobject) {
            PGobject pgObj = (PGobject) obj;
            return new PGvector(pgObj.getValue());
        }
        return new PGvector(obj.toString());
    }

    @Override
    public PGvector getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        if (obj instanceof PGvector) {
            return (PGvector) obj;
        }
        if (obj instanceof PGobject) {
            PGobject pgObj = (PGobject) obj;
            return new PGvector(pgObj.getValue());
        }
        return new PGvector(obj.toString());
    }
}