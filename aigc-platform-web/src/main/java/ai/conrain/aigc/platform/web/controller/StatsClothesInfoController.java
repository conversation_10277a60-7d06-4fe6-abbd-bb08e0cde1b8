package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.service.model.vo.StatsClothesInfoVO;
import ai.conrain.aigc.platform.service.model.query.StatsClothesInfoQuery;
import ai.conrain.aigc.platform.service.component.StatsClothesInfoService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsClothesInfo控制器
 *
 * <AUTHOR>
 * @version StatsClothesInfoService.java v 0.1 2025-04-22 05:07:33
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsClothesInfo")
public class StatsClothesInfoController {

	/** statsClothesInfoService */
	@Autowired
	private StatsClothesInfoService statsClothesInfoService;
	
	@GetMapping("/getById/{id}")
	public Result<StatsClothesInfoVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(statsClothesInfoService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody StatsClothesInfoVO statsClothesInfo){
		try {
			StatsClothesInfoVO data = statsClothesInfoService.insert(statsClothesInfo);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加服装信息统计表失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建服装信息统计表失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		statsClothesInfoService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody StatsClothesInfoVO statsClothesInfo){
		statsClothesInfoService.updateByIdSelective(statsClothesInfo);
		return Result.success();
	}

	@PostMapping("/queryList")
	public Result<List<StatsClothesInfoVO>> queryStatsClothesInfoList(@Valid @RequestBody StatsClothesInfoQuery query){
		return Result.success(statsClothesInfoService.queryStatsClothesInfoList(query));
	}
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<StatsClothesInfoVO>> getStatsClothesInfoByPage(@Valid @RequestBody StatsClothesInfoQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(statsClothesInfoService.queryStatsClothesInfoByPageWithRealTime(query));
	}
}
