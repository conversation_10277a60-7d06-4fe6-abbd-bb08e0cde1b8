package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 素材关联的模型任务表
 * 对应数据表：material_model_task
 */
@Data
public class MaterialModelTaskDO implements Serializable {
    /**
     * 模型训练任务id
     */
    private Integer id;

    /**
     * 素材id
     */
    private Integer materialId;

    /**
     * 素材名称
     */
    private String materialName;

    /**
     * 任务类型, lora训练
     */
    private String taskType;

    /**
     * 任务状态
     */
    private String taskStatus;

    /**
     * 结果模型id
     */
    private Integer materialModelId;

    /**
     * 归属主账号id
     */
    private Integer userId;

    /**
     * 操作人账号id
     */
    private Integer operatorId;

    /**
     * 自定义请求参数
     */
    private String requestParams;

    /**
     * ComfyUI返回的唯一标识
     */
    private String promptId;

    /**
     * 扩展
     */
    private String extInfo;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * aigc请求报文
     */
    private String aigcRequest;

    /**
     * 结果详情
     */
    private String retDetail;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}