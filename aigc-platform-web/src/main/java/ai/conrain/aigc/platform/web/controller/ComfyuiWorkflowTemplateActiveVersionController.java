package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateActiveVersionService;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateActiveVersionVO;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateOption;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ComfyuiWorkflowTemplateActiveVersion控制器
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/comfyuiWorkflowTemplateActiveVersion")
public class ComfyuiWorkflowTemplateActiveVersionController {

	/** comfyuiWorkflowTemplateActiveVersionService */
	@Autowired
	private ComfyuiWorkflowTemplateActiveVersionService comfyuiWorkflowTemplateActiveVersionService;
	
	@GetMapping("/getById/{id}")
	public Result<ComfyuiWorkflowTemplateActiveVersionVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(comfyuiWorkflowTemplateActiveVersionService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion){
		try {
			ComfyuiWorkflowTemplateActiveVersionVO data = comfyuiWorkflowTemplateActiveVersionService.insert(comfyuiWorkflowTemplateActiveVersion);
			if (data != null) {
				return Result.success(data.getId());
			}
		} catch (Exception e) {
			log.error("添加comfyui模板激活版本失败：", e);
		}
		return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建comfyui模板激活版本失败");
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		comfyuiWorkflowTemplateActiveVersionService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateById(@Valid @RequestBody ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion){
		comfyuiWorkflowTemplateActiveVersionService.updateById(comfyuiWorkflowTemplateActiveVersion);
		return Result.success();
	}

	@PostMapping("/findAll")
	public Result<List<ComfyuiWorkflowTemplateActiveVersionVO>> findAll(){
		return Result.success(comfyuiWorkflowTemplateActiveVersionService.findAll());
	}

	@PostMapping("/getAllTemplateKeys")
	public Result<List<ComfyuiWorkflowTemplateOption>> getAllTemplateKeys(){
		List<ComfyuiWorkflowTemplateActiveVersionVO> all = comfyuiWorkflowTemplateActiveVersionService.findAll();
		if (CollectionUtils.isNotEmpty(all)) {
			return Result.success(all.stream().map(item -> new ComfyuiWorkflowTemplateOption(item.getTemplateKey(), item.getTemplateDesc())).collect(Collectors.toList()));
		}

		return Result.success();
	}
}
