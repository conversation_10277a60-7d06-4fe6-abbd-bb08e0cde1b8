/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz.abtest;

import ai.conrain.aigc.platform.service.model.request.TestSupportCreativeRequest;
import lombok.Data;

/**
 * 测试创作请求
 *
 * <AUTHOR>
 * @version : TestCreativeRequest.java, v 0.1 2025/8/13 19:22 renxiao.wu Exp $
 */
@Data
public class TestCreativeRequest extends TestSupportCreativeRequest {
    private static final long serialVersionUID = 3042578496283252790L;

    /** 工作流类型 */
    private String flowType;

    /** 测试用例id */
    private Integer testCaseId;

    /** 图片数量，默认1张 */
    private int imageNum = 1;
}
