package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 通用主体属性表
 * 对应数据表：principal_info
 */
@Data
public class PrincipalInfoDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 主体类型
     */
    private String principalType;

    /**
     * 关联的用户id
     */
    private Integer principalId;

    /**
     * key
     */
    private String infoKey;

    /**
     * 创建人用户id
     */
    private Integer creatorUserId;

    /**
     * 修改人用户id
     */
    private Integer modifyUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除，0未删除、1删除
     */
    private Boolean deleted;

    /**
     * 信息
     */
    private String infoValue;

    /**
     * 扩展信息
     */
    private String extInfo;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}