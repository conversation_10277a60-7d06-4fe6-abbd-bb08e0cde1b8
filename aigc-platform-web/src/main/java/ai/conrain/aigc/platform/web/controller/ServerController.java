package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Server控制器
 *
 * <AUTHOR>
 * @version ServerService.java v 0.1 2024-06-15 05:24:29
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/server")
public class ServerController {

    /** serverService */
    @Autowired
    private ServerService serverService;
    @Autowired
    private ServerHelper serverHelper;

    @GetMapping("/getById/{id}")
    public Result<ServerVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(serverService.queryByKey(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody ServerVO server) {
        try {
            server.setStatus(ServerStatusEnum.DISABLE);
            ServerVO data = serverService.insert(server);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加服务器配置失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建服务器配置失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        serverService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateById(@Valid @RequestBody ServerVO server) {
        serverService.updateById(server);
        return Result.success();
    }

    @PostMapping("/findAll")
    public Result<List<ServerVO>> findAll() {
        return Result.success(serverService.queryAll());
    }

    @PostMapping("/test")
    public Result<ServerVO> test(@JsonArg @NotNull Integer id) {
        return Result.success(serverService.test(id));
    }

    @PostMapping("/fetchRunLog")
    public Result<?> fetchRunLog(@JsonArg @NotNull Integer id) {
        String data = serverService.fetchRunLog(id);
        AssertUtil.assertNotBlank(data, ResultCode.SYS_ERROR, "获取服务端口的运行日志失败");
        return Result.success(data);
    }

    @PostMapping("/restartPort")
    public Result<?> restartPort(@JsonArg @NotNull Integer id) {
        boolean result = serverService.restartPort(id);
        AssertUtil.assertTrue(result, ResultCode.SYS_ERROR, "重启端口失败");
        return Result.success(result);
    }

    @PostMapping("/restartServer")
    public Result<?> restartServer(@JsonArg @NotNull Integer id, @JsonArg @NotNull String type) {
        boolean result = serverService.restartServer(id, type);
        AssertUtil.assertTrue(result, ResultCode.SYS_ERROR, "重启docker服务失败");
        return Result.success(result);
    }

    @PostMapping("/updateCreativeNode")
    public Result<?> updateCreativeNode(@JsonArg @NotNull Integer id) {
        boolean result = serverService.updateCreativeNode(id);
        AssertUtil.assertTrue(result, ResultCode.SYS_ERROR, "更新comfyui-tools失败");
        return Result.success(result);
    }

    //@GetMapping("/fetchAllFileServers")
    //public Result<List<ServerVO>> fetchAllFileServers() {
    //    return Result.success(serverHelper.getAllFileServers());
    //}

    @PostMapping("/initDeviceInfo")
    public Result<?> initDeviceInfo() {
        //TODO 临时使用
        serverService.initDeviceInfo();
        return Result.success();
    }
}
