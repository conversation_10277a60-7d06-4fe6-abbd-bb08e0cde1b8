/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

/**
 * 图片案例标签DO
 *
 * <AUTHOR>
 * @version : ImageCaseTagDO.java, v 0.1 2024/12/10 12:36 renxiao.wu Exp $
 */
@Data
public class ImageCaseTagDO {
    private Integer id;
    /** 案例id */
    private Integer caseId;
    /** 标签id */
    private Integer tagId;

    public ImageCaseTagDO() {

    }

    public ImageCaseTagDO(Integer caseId, Integer tagId) {
        this.caseId = caseId;
        this.tagId = tagId;
    }
}
