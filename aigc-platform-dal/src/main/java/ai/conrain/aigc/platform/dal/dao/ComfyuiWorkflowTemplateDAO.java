package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiWorkflowTemplateExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ComfyuiWorkflowTemplateDAO {
    long countByExample(ComfyuiWorkflowTemplateExample example);

    int deleteByExample(ComfyuiWorkflowTemplateExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ComfyuiWorkflowTemplateDO record);

    int insertSelective(ComfyuiWorkflowTemplateDO record);

    List<ComfyuiWorkflowTemplateDO> selectByExampleWithBLOBs(ComfyuiWorkflowTemplateExample example);

    List<ComfyuiWorkflowTemplateDO> selectByExample(ComfyuiWorkflowTemplateExample example);

    ComfyuiWorkflowTemplateDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ComfyuiWorkflowTemplateDO record, @Param("example") ComfyuiWorkflowTemplateExample example);

    int updateByExampleWithBLOBs(@Param("record") ComfyuiWorkflowTemplateDO record, @Param("example") ComfyuiWorkflowTemplateExample example);

    int updateByExample(@Param("record") ComfyuiWorkflowTemplateDO record, @Param("example") ComfyuiWorkflowTemplateExample example);

    int updateByPrimaryKeySelective(ComfyuiWorkflowTemplateDO record);

    int updateByPrimaryKeyWithBLOBs(ComfyuiWorkflowTemplateDO record);

    int updateByPrimaryKey(ComfyuiWorkflowTemplateDO record);

    ComfyuiWorkflowTemplateDO selectByTemplateKeyAndVersion(@Param("templateKey") String templateKey, @Param("version") String version);
}