package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.TestCaseItemDO;
import ai.conrain.aigc.platform.dal.example.TestCaseItemExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TestCaseItemDAO {
    long countByExample(TestCaseItemExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TestCaseItemDO record);

    int insertSelective(TestCaseItemDO record);

    List<TestCaseItemDO> selectByExampleWithBLOBs(TestCaseItemExample example);

    List<TestCaseItemDO> selectByExample(TestCaseItemExample example);

    TestCaseItemDO selectByPrimaryKey(Integer id);

    TestCaseItemDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") TestCaseItemDO record, @Param("example") TestCaseItemExample example);

    int updateByExampleWithBLOBs(@Param("record") TestCaseItemDO record, @Param("example") TestCaseItemExample example);

    int updateByExample(@Param("record") TestCaseItemDO record, @Param("example") TestCaseItemExample example);

    int updateByPrimaryKeySelective(TestCaseItemDO record);

    int updateByPrimaryKeyWithBLOBs(TestCaseItemDO record);

    int updateByPrimaryKey(TestCaseItemDO record);

    int logicalDeleteByExample(@Param("example") TestCaseItemExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    void batchInsert(List<TestCaseItemDO> list);
}