package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.SubUserService;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.request.SubUserUpdate;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 子账号控制器
 *
 * <AUTHOR>
 * @version : SubUserController.java, v 0.1 2024/1/17 15:44 renxiao.wu Exp $
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/user/sub")
public class SubUserController {
    @Autowired
    private SubUserService subUserService;

    @GetMapping("/queryList")
    public Result<List<UserVO>> querySubUsers() {
        return Result.success(subUserService.queryList());
    }

    @PostMapping("/create")
    public Result<UserVO> create(@Valid @RequestBody UserRegister user) {
        user.setRoleType(null);
        return Result.success(subUserService.create(user));
    }

    @PostMapping("/update")
    public Result<?> update(@Valid @RequestBody SubUserUpdate user) {
        subUserService.update(user);
        return Result.success();
    }

    @PostMapping("/disable")
    public Result<?> disable(@JsonArg @NotNull Integer userId) {
        subUserService.enableOrDisable(userId, UserStatusEnum.DISABLED);
        return Result.success();
    }

    @PostMapping("/enable")
    public Result<?> enable(@JsonArg @NotNull Integer userId) {
        subUserService.enableOrDisable(userId, UserStatusEnum.ENABLED);
        return Result.success();
    }
}
