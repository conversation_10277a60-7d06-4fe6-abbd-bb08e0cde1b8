package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ImageCaseSyncRecordDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(ImageCaseSyncRecordDO record);

    ImageCaseSyncRecordDO selectByPrimaryKey(Integer id);

    List<ImageCaseSyncRecordDO> selectAll();

    int updateByPrimaryKey(ImageCaseSyncRecordDO record);
}