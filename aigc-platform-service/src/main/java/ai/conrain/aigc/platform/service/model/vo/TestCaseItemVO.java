package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.abtest.BaseCaseItemDetail;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * TestCaseItemVO
 *
 * @version TestCaseItemService.java v 0.1 2025-08-12 07:18:08
 */
@Data
public class TestCaseItemVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试用例id */
    private Integer caseId;

    /** 测试用例项名称 */
    private String name;

    /** 类型 */
    private TestCaseTypeEnum type;

    /** 执行次数 */
    private Integer runTimes;

    /** 创建时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 修改时间 */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /** 扩展 */
    private JSONObject extInfo;

    /** 详情 */
    private BaseCaseItemDetail detail;

}