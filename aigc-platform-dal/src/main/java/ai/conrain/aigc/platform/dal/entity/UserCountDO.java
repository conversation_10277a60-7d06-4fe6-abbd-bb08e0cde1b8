/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 用户id+数量的DO
 *
 * <AUTHOR>
 * @version : UserCountDO.java, v 0.1 2025/4/7 11:49 renxiao.wu Exp $
 */
@Data
public class UserCountDO implements Serializable {
    private static final long serialVersionUID = -3829112138727053293L;
    /** 用户id */
    private Integer userId;
    /** 数量 */
    private Integer cnt;
}
