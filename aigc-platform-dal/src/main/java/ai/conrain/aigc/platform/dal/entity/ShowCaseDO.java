package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 优秀案例表
 * 对应数据表：show_case
 */
@Data
public class ShowCaseDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 案例名称
     */
    private String name;

    /**
     * 类型，IMAGE、VIDEO
     */
    private String type;

    /**
     * 结果图/视频地址url
     */
    private String mainUrl;

    /**
     * 展示图url
     */
    private String showImage;

    /**
     * 模特id
     */
    private Integer faceId;

    /**
     * 场景id
     */
    private Integer sceneId;

    /**
     * 服装模型id
     */
    private Integer modelId;

    /**
     * 服装url
     */
    private String modelUrl;

    /** 服装缩略图url */
    private String modelMiniUrl;

    /**
     * 排序
     */
    private Integer order;

    /**
     * 标签列表，多个以逗号隔开
     */
    private String tags;

    /**
     * 状态，ENABLED、DISABLED
     */
    private String status;

    /**
     * 是否置顶
     */
    private boolean topped;

    /**
     * 备注
     */
    private String memo;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 服装搭配信息
     */
    private String clothCollocation;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getMainUrl() {
        return mainUrl;
    }

    public void setMainUrl(String mainUrl) {
        this.mainUrl = mainUrl == null ? null : mainUrl.trim();
    }

    public String getShowImage() {
        return showImage;
    }

    public void setShowImage(String showImage) {
        this.showImage = showImage == null ? null : showImage.trim();
    }

    public Integer getFaceId() {
        return faceId;
    }

    public void setFaceId(Integer faceId) {
        this.faceId = faceId;
    }

    public Integer getSceneId() {
        return sceneId;
    }

    public void setSceneId(Integer sceneId) {
        this.sceneId = sceneId;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    public String getModelUrl() {
        return modelUrl;
    }

    public void setModelUrl(String modelUrl) {
        this.modelUrl = modelUrl == null ? null : modelUrl.trim();
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getClothCollocation() {
        return clothCollocation;
    }

    public void setClothCollocation(String clothCollocation) {
        this.clothCollocation = clothCollocation == null ? null : clothCollocation.trim();
    }
}