package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ShootingStyleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ShootingStyleDAO {
    int deleteByPrimaryKey(Integer id);

    int insert(ShootingStyleDO record);

    ShootingStyleDO selectByPrimaryKey(Integer id);

    List<ShootingStyleDO> selectAll();

    int updateByPrimaryKey(ShootingStyleDO record);

    int updateByPrimaryKeySelective(ShootingStyleDO record);
}