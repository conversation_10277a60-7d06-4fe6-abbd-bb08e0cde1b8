package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * AB测试项目表
 * 对应数据表：test_item
 */
@Data
public class TestItemDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 测试计划id
     */
    private Integer planId;

    /**
     * 类型，TRAIN、CREATIVE
     */
    private String type;

    /**
     * 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED
     */
    private String status;

    /**
     * 前置项目id
     */
    private Integer preId;

    /**
     * 前置项目状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED
     */
    private String preStatus;

    /**
     * 轮数
     */
    private Integer roundsNum;

    /**
     * 操作者id
     */
    private Integer operatorId;

    /** 结论 */
    private String conclusion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 共用的参数信息
     */
    private String sharedParams;

    /**
     * 实验项名称
     */
    private String name;

    private static final long serialVersionUID = 1L;
}