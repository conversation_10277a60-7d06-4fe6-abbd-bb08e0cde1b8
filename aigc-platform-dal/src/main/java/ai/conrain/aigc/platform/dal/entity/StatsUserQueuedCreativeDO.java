/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * 统计用户维度队列中的创作DO
 *
 * <AUTHOR>
 * @version : StatsUserQueuedCreativeDO.java, v 0.1 2025/5/13 00:46 renxiao.wu Exp $
 */
@Data
public class StatsUserQueuedCreativeDO implements Serializable {
    private static final long serialVersionUID = -8292901424550307949L;
    /** 用户id */
    private Integer userId;
    /** 用户昵称 */
    private String nickName;
    /** 总数 */
    private Integer total;
    /** 队列中的数量 */
    private Integer queueSize;
    /** 正在处理中的数量 */
    private Integer processingSize;
    /** 最早创建时间 */
    private String minCreateTime;
}
