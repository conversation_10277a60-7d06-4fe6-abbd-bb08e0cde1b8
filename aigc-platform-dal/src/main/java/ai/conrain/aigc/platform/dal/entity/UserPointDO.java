package ai.conrain.aigc.platform.dal.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户算力点表
 * 对应数据表：user_point
 */
@Data
public class UserPointDO implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 算力点
     */
    private Integer point;

    /** 赠送点数 */
    private Integer givePoint;

    /** 体验点 */
    private Integer experiencePoint;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    private static final long serialVersionUID = 1L;
}