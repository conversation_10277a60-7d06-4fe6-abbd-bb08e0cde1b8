package ai.conrain.aigc.platform.web.controller;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.resolver.JsonArg;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * UserPoint控制器
 *
 * <AUTHOR>
 * @version PointController.java v 0.1 2024-05-15 10:58:45
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/point")
public class PointController {

    /** userPointService */
    @Autowired
    private UserPointService userPointService;

    @GetMapping("/queryByImage")
    public Result<UserPointVO> queryImagePoint() {
        return Result.success(userPointService.queryImagePoint(OperationContextHolder.getMasterUserId()));
    }

    @PostMapping("/queryByUserId")
    public Result<UserPointVO> queryImagePoint(@NotNull @JsonArg Integer userId) {
        return Result.success(userPointService.queryImagePoint(userId));
    }

    @PostMapping("/topupByImage")
    @Deprecated
    public Result<?> topupByImage(@NotNull @JsonArg Integer userId, @NotNull @JsonArg Integer imagePoint,
                                  @NotNull @JsonArg Integer experiencePoint, @JsonArg Integer givePoint) {
        userPointService.topupByImage(userId, imagePoint, experiencePoint, givePoint == null ? 0 : givePoint);
        return Result.success();
    }

    @PostMapping("/give")
    public Result<?> give(@NotNull @JsonArg Integer userId, @JsonArg Integer experiencePoint,
                          @JsonArg Integer givePoint) {
        userPointService.adjustGivePoint(userId, experiencePoint == null ? 0 : experiencePoint,
            givePoint == null ? 0 : givePoint);
        return Result.success();
    }

    @PostMapping("/adjustMuse")
    @Deprecated
    public Result<?> adjustMuse(@NotNull @JsonArg Integer userId, @NotNull @JsonArg BigDecimal musePoint,
                                @NotNull @JsonArg String memo) {
        userPointService.adjustMuse(userId, musePoint, memo);
        return Result.success();
    }

    @PostMapping("/predict")
    public Result<PredictVO> predict(@JsonArg Integer modelId, @NotNull @Min(1) @JsonArg Integer imageNum,
                                     @JsonArg String creativeType, @JsonArg Integer timeSecs4Video,
                                     @JsonArg Boolean isUpload, @JsonArg String proportionType) {

        CreativeTypeEnum type = CreativeTypeEnum.getByCode(creativeType);

        AssertUtil.assertTrue(type != null || modelId != null, ResultCode.PARAM_INVALID,
            "creativeType or modelId is required");

        type = type == null ? CreativeTypeEnum.CREATE_IMAGE : type;
        isUpload = isUpload != null && isUpload;
        return Result.success(userPointService.predict(modelId, imageNum, type, timeSecs4Video, isUpload,
            ProportionTypeEnum.getByCode(proportionType)));
    }
}
