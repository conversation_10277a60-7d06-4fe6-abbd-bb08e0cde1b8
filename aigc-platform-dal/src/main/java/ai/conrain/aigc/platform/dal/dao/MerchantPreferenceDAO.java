package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.MerchantPreferenceDO;
import ai.conrain.aigc.platform.dal.example.MerchantPreferenceExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MerchantPreferenceDAO {
    long countByExample(MerchantPreferenceExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(MerchantPreferenceDO record);

    int insertSelective(MerchantPreferenceDO record);

    List<MerchantPreferenceDO> selectByExampleWithBLOBs(MerchantPreferenceExample example);

    List<MerchantPreferenceDO> selectByExample(MerchantPreferenceExample example);

    MerchantPreferenceDO selectByPrimaryKey(Integer id);

    MerchantPreferenceDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") MerchantPreferenceDO record, @Param("example") MerchantPreferenceExample example);

    int updateByExampleWithBLOBs(@Param("record") MerchantPreferenceDO record, @Param("example") MerchantPreferenceExample example);

    int updateByExample(@Param("record") MerchantPreferenceDO record, @Param("example") MerchantPreferenceExample example);

    int updateByPrimaryKeySelective(MerchantPreferenceDO record);

    int updateByPrimaryKeyWithBLOBs(MerchantPreferenceDO record);

    int updateByPrimaryKey(MerchantPreferenceDO record);

    int logicalDeleteByExample(@Param("example") MerchantPreferenceExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    int deleteByUserId(Integer userId);

    int batchInsert(@Param("list") List<MerchantPreferenceDO> list);
}