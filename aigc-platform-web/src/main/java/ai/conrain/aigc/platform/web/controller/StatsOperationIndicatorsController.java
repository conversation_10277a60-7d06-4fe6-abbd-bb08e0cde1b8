package ai.conrain.aigc.platform.web.controller;

import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsExcelVO;
import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsVO;
import ai.conrain.aigc.platform.service.model.query.StatsOperationIndicatorsQuery;
import ai.conrain.aigc.platform.service.component.StatsOperationIndicatorsService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import ai.conrain.aigc.platform.service.util.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * StatsOperationIndicators控制器
 *
 * <AUTHOR>
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-16 11:38:50
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/statsOperationIndicators")
public class StatsOperationIndicatorsController {

    /** statsOperationIndicatorsService */
    @Autowired
    private StatsOperationIndicatorsService statsOperationIndicatorsService;

    @GetMapping("/getById/{id}")
    public Result<StatsOperationIndicatorsVO> getById(@NotNull @PathVariable("id") Integer id) {
        return Result.success(statsOperationIndicatorsService.selectById(id));
    }

    @PostMapping("/create")
    public Result<Integer> create(@Valid @RequestBody StatsOperationIndicatorsVO statsOperationIndicators) {
        try {
            StatsOperationIndicatorsVO data = statsOperationIndicatorsService.insert(statsOperationIndicators);
            if (data != null) {
                return Result.success(data.getId());
            }
        } catch (Exception e) {
            log.error("添加失败：", e);
        }
        return Result.failedWithMessage(ResultCode.SYS_ERROR, "新建失败");
    }

    @PostMapping("/deleteById")
    public Result<?> deleteById(@NotNull @JsonArg Integer id) {
        statsOperationIndicatorsService.deleteById(id);
        return Result.success();
    }

    @PostMapping("/updateById")
    public Result<?> updateByIdSelective(@Valid @RequestBody StatsOperationIndicatorsVO statsOperationIndicators) {
        statsOperationIndicatorsService.updateByIdSelective(statsOperationIndicators);
        return Result.success();
    }

    @PostMapping("/queryList")
    public Result<List<StatsOperationIndicatorsVO>> queryStatsOperationIndicatorsList(@Valid @RequestBody StatsOperationIndicatorsQuery query) {
        return Result.success(statsOperationIndicatorsService.queryStatsOperationIndicatorsList(query));
    }

    @PostMapping("/queryByPage")
    public Result<PageInfo<StatsOperationIndicatorsVO>> getStatsOperationIndicatorsByPage(@Valid @RequestBody StatsOperationIndicatorsQuery query) {

        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        return Result.success(statsOperationIndicatorsService.queryStatsOperationIndicatorsByPage(query));
    }

    /**
     * 下载统计数据
     *
     * @param response 响应数据
     * @param query    查询参数
     */
    @PostMapping("/download")
    public void download(HttpServletResponse response, @Valid @RequestBody StatsOperationIndicatorsQuery query) {
        // 拉取数据
        List<StatsOperationIndicatorsExcelVO> excelDataList = statsOperationIndicatorsService.doDownload(query);
        // 下载文件
        EasyExcelUtils.export(response, StatsOperationIndicatorsExcelVO.class, excelDataList, "运营指标统计", "统计数据");
    }
}
