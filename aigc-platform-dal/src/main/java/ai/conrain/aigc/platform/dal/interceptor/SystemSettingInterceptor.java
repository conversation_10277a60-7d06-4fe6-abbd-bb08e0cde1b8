/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.dal.interceptor;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Invocation;
import org.springframework.beans.factory.annotation.Value;

/**
 * 系统设置拦截器
 *
 * <AUTHOR>
 * @version : SystemSettingInterceptor.java, v 0.1 2023/9/2 12:09 renxiao.wu Exp $
 */
@Slf4j
//@Component
//@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
//             @Signature(type = Executor.class, method = "query",
//                 args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})})
public class SystemSettingInterceptor implements Interceptor {
    /** 租户 */
    @Value("${app.custom.tenant}")
    private String tenant;
    /** 环境 */
    @Value("${app.custom.env}")
    private String env;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            MappedStatement mappedStatement = (MappedStatement)invocation.getArgs()[0];
            Object parameter = null;
            if (invocation.getArgs().length > 1) {
                parameter = invocation.getArgs()[1];

                //TODO by半泉:带do的可以通过一下字段设置，但其他设置不了，需要再优化
                if (parameter instanceof ParamMap && ((ParamMap)parameter).containsKey("list")) {
                    Object list = ((ParamMap)parameter).get("list");
                    for (Object each : (List<?>)list) {
                        initSystemSetting(each);
                    }
                } else {
                    initSystemSetting(parameter);
                }
            }

            //sql语句的id
            String sqlID = mappedStatement.getId();

            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            //获取sql语句
            log.info("sqlID: {}, parameter: {},Sql:{}", sqlID, parameter, boundSql);
        } catch (Throwable e) {
            log.error("SqlMonitorInterceptor error", e);
        }

        // 执行完上面的任务后，不改变原有的sql执行过程
        return invocation.proceed();
    }

    /**
     * 初始化
     *
     * @param parameter 参数
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private void initSystemSetting(Object parameter) throws IllegalAccessException, InvocationTargetException {
        if (null == parameter) {
            return;
        }

        BeanUtils.setProperty(parameter, "tenant", tenant);
        BeanUtils.setProperty(parameter, "env", env);
    }
}
