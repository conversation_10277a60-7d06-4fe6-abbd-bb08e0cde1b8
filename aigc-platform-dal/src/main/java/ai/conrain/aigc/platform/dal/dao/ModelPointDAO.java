package ai.conrain.aigc.platform.dal.dao;

import ai.conrain.aigc.platform.dal.entity.ModelPointDO;
import ai.conrain.aigc.platform.dal.example.ModelPointExample;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ModelPointDAO {
    long countByExample(ModelPointExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ModelPointDO record);

    int insertSelective(ModelPointDO record);

    List<ModelPointDO> selectByExample(ModelPointExample example);

    ModelPointDO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ModelPointDO record, @Param("example") ModelPointExample example);

    int updateByExample(@Param("record") ModelPointDO record, @Param("example") ModelPointExample example);

    int updateByPrimaryKeySelective(ModelPointDO record);

    int updateByPrimaryKey(ModelPointDO record);

    ModelPointDO lockByModelId(Integer modelId, Integer userId);

    List<ModelPointDO> batchSelectByModel(List<Integer> modelIds, Integer userId);
}